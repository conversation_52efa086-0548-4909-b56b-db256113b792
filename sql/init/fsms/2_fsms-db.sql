/*
 Navicat Premium Data Transfer

 Source Server         : *********
 Source Server Type    : PostgreSQL
 Source Server Version : 110016
 Source Host           : *********:30448
 Source Catalog        : fscm
 Source Schema         : zjdata

 Target Server Type    : PostgreSQL
 Target Server Version : 110016
 File Encoding         : 65001

 Date: 07/03/2023 13:45:42
*/


-- ----------------------------
-- Sequence structure for act_evt_log_log_nr__seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS  "zjdata"."act_evt_log_log_nr__seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 2147483647
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for d_c_field_relation_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."d_c_field_relation_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for d_c_field_relation_type_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."d_c_field_relation_type_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for f_d_flow_head_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."f_d_flow_head_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 99999999
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for f_d_post_certificate_head_voucher_num_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."f_d_post_certificate_head_voucher_num_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 2147483647
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for f_g_c_cache_data_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."f_g_c_cache_data_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;


-- ----------------------------
-- Sequence structure for org_company_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."org_company_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for org_factory_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."org_factory_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for org_purchase_group_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."org_purchase_group_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for org_purchase_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS  "zjdata"."org_purchase_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for org_stock_place_id_seq
-- ----------------------------
CREATE SEQUENCE IF NOT EXISTS   "zjdata"."org_stock_place_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;









-- ----------------------------
-- Table structure for act_evt_log
-- ----------------------------
CREATE TABLE IF NOT EXISTS   "zjdata"."act_evt_log" (
    "log_nr_" int4 NOT NULL DEFAULT nextval('"zjdata".act_evt_log_log_nr__seq'::regclass),
    "type_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "time_stamp_" timestamp(6) NOT NULL,
    "user_id_" varchar(255) COLLATE "pg_catalog"."default",
    "data_" bytea,
    "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
    "lock_time_" timestamp(6),
    "is_processed_" int2 DEFAULT 0
    )
;

-- ----------------------------
-- Table structure for act_ge_bytearray
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ge_bytearray" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "name_" varchar(255) COLLATE "pg_catalog"."default",
    "deployment_id_" varchar(64) COLLATE "pg_catalog"."default",
    "bytes_" bytea,
    "generated_" bool
    )
;

-- ----------------------------
-- Table structure for act_ge_property
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ge_property" (
    "name_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "value_" varchar(300) COLLATE "pg_catalog"."default",
    "rev_" int4
    )
;

-- ----------------------------
-- Table structure for act_hi_actinst
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_hi_actinst" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "act_id_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "call_proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "act_name_" varchar(255) COLLATE "pg_catalog"."default",
    "act_type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "assignee_" varchar(255) COLLATE "pg_catalog"."default",
    "start_time_" timestamp(6) NOT NULL,
    "end_time_" timestamp(6),
    "duration_" int8,
    "delete_reason_" varchar(4000) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;

-- ----------------------------
-- Table structure for act_hi_attachment
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_hi_attachment" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "user_id_" varchar(255) COLLATE "pg_catalog"."default",
    "name_" varchar(255) COLLATE "pg_catalog"."default",
    "description_" varchar(4000) COLLATE "pg_catalog"."default",
    "type_" varchar(255) COLLATE "pg_catalog"."default",
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "url_" varchar(4000) COLLATE "pg_catalog"."default",
    "content_id_" varchar(64) COLLATE "pg_catalog"."default",
    "time_" timestamp(6)
    )
;

-- ----------------------------
-- Table structure for act_hi_comment
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_hi_comment" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "type_" varchar(255) COLLATE "pg_catalog"."default",
    "time_" timestamp(6) NOT NULL,
    "user_id_" varchar(255) COLLATE "pg_catalog"."default",
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "action_" varchar(255) COLLATE "pg_catalog"."default",
    "message_" varchar(4000) COLLATE "pg_catalog"."default",
    "full_msg_" bytea
    )
;

-- ----------------------------
-- Table structure for act_hi_detail
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_hi_detail" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "act_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "name_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "var_type_" varchar(64) COLLATE "pg_catalog"."default",
    "rev_" int4,
    "time_" timestamp(6) NOT NULL,
    "bytearray_id_" varchar(64) COLLATE "pg_catalog"."default",
    "double_" float8,
    "long_" int8,
    "text_" varchar(4000) COLLATE "pg_catalog"."default",
    "text2_" varchar(4000) COLLATE "pg_catalog"."default"
    )
;

-- ----------------------------
-- Table structure for act_hi_identitylink
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_hi_identitylink" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "group_id_" varchar(255) COLLATE "pg_catalog"."default",
    "type_" varchar(255) COLLATE "pg_catalog"."default",
    "user_id_" varchar(255) COLLATE "pg_catalog"."default",
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default"
    )
;

-- ----------------------------
-- Table structure for act_hi_procinst
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_hi_procinst" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "business_key_" varchar(255) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "start_time_" timestamp(6) NOT NULL,
    "end_time_" timestamp(6),
    "duration_" int8,
    "start_user_id_" varchar(255) COLLATE "pg_catalog"."default",
    "start_act_id_" varchar(255) COLLATE "pg_catalog"."default",
    "end_act_id_" varchar(255) COLLATE "pg_catalog"."default",
    "super_process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
    "delete_reason_" varchar(4000) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "name_" varchar(255) COLLATE "pg_catalog"."default"
    )
;

-- ----------------------------
-- Table structure for act_hi_taskinst
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_hi_taskinst" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "task_def_key_" varchar(255) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "name_" varchar(255) COLLATE "pg_catalog"."default",
    "parent_task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "description_" varchar(4000) COLLATE "pg_catalog"."default",
    "owner_" varchar(255) COLLATE "pg_catalog"."default",
    "assignee_" varchar(255) COLLATE "pg_catalog"."default",
    "start_time_" timestamp(6) NOT NULL,
    "claim_time_" timestamp(6),
    "end_time_" timestamp(6),
    "duration_" int8,
    "delete_reason_" varchar(4000) COLLATE "pg_catalog"."default",
    "priority_" int4,
    "due_date_" timestamp(6),
    "form_key_" varchar(255) COLLATE "pg_catalog"."default",
    "category_" varchar(255) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;

-- ----------------------------
-- Table structure for act_hi_varinst
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_hi_varinst" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "name_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "var_type_" varchar(100) COLLATE "pg_catalog"."default",
    "rev_" int4,
    "bytearray_id_" varchar(64) COLLATE "pg_catalog"."default",
    "double_" float8,
    "long_" int8,
    "text_" varchar(4000) COLLATE "pg_catalog"."default",
    "text2_" varchar(4000) COLLATE "pg_catalog"."default",
    "create_time_" timestamp(6),
    "last_updated_time_" timestamp(6)
    )
;

-- ----------------------------
-- Table structure for act_procdef_info
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_procdef_info" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "info_json_id_" varchar(64) COLLATE "pg_catalog"."default"
    )
;

-- ----------------------------
-- Table structure for act_re_deployment
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_re_deployment" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "name_" varchar(255) COLLATE "pg_catalog"."default",
    "category_" varchar(255) COLLATE "pg_catalog"."default",
    "key_" varchar(255) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "deploy_time_" timestamp(6),
    "engine_version_" varchar(255) COLLATE "pg_catalog"."default"
    )
;

-- ----------------------------
-- Table structure for act_re_model
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_re_model" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "name_" varchar(255) COLLATE "pg_catalog"."default",
    "key_" varchar(255) COLLATE "pg_catalog"."default",
    "category_" varchar(255) COLLATE "pg_catalog"."default",
    "create_time_" timestamp(6),
    "last_update_time_" timestamp(6),
    "version_" int4,
    "meta_info_" varchar(4000) COLLATE "pg_catalog"."default",
    "deployment_id_" varchar(64) COLLATE "pg_catalog"."default",
    "editor_source_value_id_" varchar(64) COLLATE "pg_catalog"."default",
    "editor_source_extra_value_id_" varchar(64) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;

-- ----------------------------
-- Table structure for act_re_procdef
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_re_procdef" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "category_" varchar(255) COLLATE "pg_catalog"."default",
    "name_" varchar(255) COLLATE "pg_catalog"."default",
    "key_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "version_" int4 NOT NULL,
    "deployment_id_" varchar(64) COLLATE "pg_catalog"."default",
    "resource_name_" varchar(4000) COLLATE "pg_catalog"."default",
    "dgrm_resource_name_" varchar(4000) COLLATE "pg_catalog"."default",
    "description_" varchar(4000) COLLATE "pg_catalog"."default",
    "has_start_form_key_" bool,
    "has_graphical_notation_" bool,
    "suspension_state_" int4,
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "engine_version_" varchar(255) COLLATE "pg_catalog"."default"
    )
;

-- ----------------------------
-- Table structure for act_ru_deadletter_job
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_deadletter_job" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "exclusive_" bool,
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
    "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
    "duedate_" timestamp(6),
    "repeat_" varchar(255) COLLATE "pg_catalog"."default",
    "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
    "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;

-- ----------------------------
-- Table structure for act_ru_event_subscr
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_event_subscr" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "event_type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "event_name_" varchar(255) COLLATE "pg_catalog"."default",
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "activity_id_" varchar(64) COLLATE "pg_catalog"."default",
    "configuration_" varchar(255) COLLATE "pg_catalog"."default",
    "created_" timestamp(6) NOT NULL,
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;

-- ----------------------------
-- Table structure for act_ru_execution
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_execution" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "business_key_" varchar(255) COLLATE "pg_catalog"."default",
    "parent_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "super_exec_" varchar(64) COLLATE "pg_catalog"."default",
    "root_proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "act_id_" varchar(255) COLLATE "pg_catalog"."default",
    "is_active_" bool,
    "is_concurrent_" bool,
    "is_scope_" bool,
    "is_event_scope_" bool,
    "is_mi_root_" bool,
    "suspension_state_" int4,
    "cached_ent_state_" int4,
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "name_" varchar(255) COLLATE "pg_catalog"."default",
    "start_time_" timestamp(6),
    "start_user_id_" varchar(255) COLLATE "pg_catalog"."default",
    "lock_time_" timestamp(6),
    "is_count_enabled_" bool,
    "evt_subscr_count_" int4,
    "task_count_" int4,
    "job_count_" int4,
    "timer_job_count_" int4,
    "susp_job_count_" int4,
    "deadletter_job_count_" int4,
    "var_count_" int4,
    "id_link_count_" int4
    )
;

-- ----------------------------
-- Table structure for act_ru_identitylink
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_identitylink" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "group_id_" varchar(255) COLLATE "pg_catalog"."default",
    "type_" varchar(255) COLLATE "pg_catalog"."default",
    "user_id_" varchar(255) COLLATE "pg_catalog"."default",
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default"
    )
;

-- ----------------------------
-- Table structure for act_ru_job
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_job" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "lock_exp_time_" timestamp(6),
    "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
    "exclusive_" bool,
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "retries_" int4,
    "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
    "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
    "duedate_" timestamp(6),
    "repeat_" varchar(255) COLLATE "pg_catalog"."default",
    "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
    "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;

-- ----------------------------
-- Table structure for act_ru_suspended_job
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_suspended_job" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "exclusive_" bool,
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "retries_" int4,
    "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
    "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
    "duedate_" timestamp(6),
    "repeat_" varchar(255) COLLATE "pg_catalog"."default",
    "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
    "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;

-- ----------------------------
-- Table structure for act_ru_task
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_task" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "name_" varchar(255) COLLATE "pg_catalog"."default",
    "parent_task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "description_" varchar(4000) COLLATE "pg_catalog"."default",
    "task_def_key_" varchar(255) COLLATE "pg_catalog"."default",
    "owner_" varchar(255) COLLATE "pg_catalog"."default",
    "assignee_" varchar(255) COLLATE "pg_catalog"."default",
    "delegation_" varchar(64) COLLATE "pg_catalog"."default",
    "priority_" int4,
    "create_time_" timestamp(6),
    "due_date_" timestamp(6),
    "category_" varchar(255) COLLATE "pg_catalog"."default",
    "suspension_state_" int4,
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "form_key_" varchar(255) COLLATE "pg_catalog"."default",
    "claim_time_" timestamp(6)
    )
;

-- ----------------------------
-- Table structure for act_ru_timer_job
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_timer_job" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "lock_exp_time_" timestamp(6),
    "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
    "exclusive_" bool,
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
    "retries_" int4,
    "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
    "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
    "duedate_" timestamp(6),
    "repeat_" varchar(255) COLLATE "pg_catalog"."default",
    "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
    "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
    "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;

-- ----------------------------
-- Table structure for act_ru_variable
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."act_ru_variable" (
    "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "rev_" int4,
    "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "name_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
    "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
    "task_id_" varchar(64) COLLATE "pg_catalog"."default",
    "bytearray_id_" varchar(64) COLLATE "pg_catalog"."default",
    "double_" float8,
    "long_" int8,
    "text_" varchar(4000) COLLATE "pg_catalog"."default",
    "text2_" varchar(4000) COLLATE "pg_catalog"."default"
    )
;

-- ----------------------------
-- Table structure for biz_approval
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."biz_approval" (
    "id" int8 NOT NULL,
    "type" char(20) COLLATE "pg_catalog"."default",
    "title" varchar(100) COLLATE "pg_catalog"."default",
    "reason" varchar(500) COLLATE "pg_catalog"."default",
    "start_time" timestamp(6),
    "end_time" timestamp(6),
    "total_time" int8,
    "instance_id" varchar(32) COLLATE "pg_catalog"."default",
    "create_by" varchar(64) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default",
    "update_time" timestamp(6),
    "apply_user" varchar(64) COLLATE "pg_catalog"."default",
    "apply_time" timestamp(6),
    "reality_start_time" timestamp(6),
    "reality_end_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."biz_approval"."id" IS '主键ID';
COMMENT ON COLUMN "zjdata"."biz_approval"."type" IS '请假类型';
COMMENT ON COLUMN "zjdata"."biz_approval"."title" IS '标题';
COMMENT ON COLUMN "zjdata"."biz_approval"."reason" IS '原因';
COMMENT ON COLUMN "zjdata"."biz_approval"."start_time" IS '开始时间';
COMMENT ON COLUMN "zjdata"."biz_approval"."end_time" IS '结束时间';
COMMENT ON COLUMN "zjdata"."biz_approval"."total_time" IS '请假时长，单位秒';
COMMENT ON COLUMN "zjdata"."biz_approval"."instance_id" IS '流程实例ID';
COMMENT ON COLUMN "zjdata"."biz_approval"."create_by" IS '创建者';
COMMENT ON COLUMN "zjdata"."biz_approval"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."biz_approval"."update_by" IS '更新者';
COMMENT ON COLUMN "zjdata"."biz_approval"."update_time" IS '更新时间';
COMMENT ON COLUMN "zjdata"."biz_approval"."apply_user" IS '申请人';
COMMENT ON COLUMN "zjdata"."biz_approval"."apply_time" IS '申请时间';
COMMENT ON COLUMN "zjdata"."biz_approval"."reality_start_time" IS '实际开始时间';
COMMENT ON COLUMN "zjdata"."biz_approval"."reality_end_time" IS '实际结束时间';

-- ----------------------------
-- Table structure for biz_todo_item
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."biz_todo_item" (
    "id" int8 NOT NULL,
    "item_name" varchar(100) COLLATE "pg_catalog"."default",
    "item_content" varchar(500) COLLATE "pg_catalog"."default",
    "module" varchar(50) COLLATE "pg_catalog"."default",
    "task_id" varchar(64) COLLATE "pg_catalog"."default",
    "instance_id" varchar(32) COLLATE "pg_catalog"."default",
    "task_name" varchar(50) COLLATE "pg_catalog"."default",
    "node_name" varchar(50) COLLATE "pg_catalog"."default",
    "is_view" char(1) COLLATE "pg_catalog"."default",
    "is_handle" char(1) COLLATE "pg_catalog"."default",
    "todo_user_id" varchar(20) COLLATE "pg_catalog"."default",
    "todo_user_name" varchar(30) COLLATE "pg_catalog"."default",
    "handle_user_id" varchar(20) COLLATE "pg_catalog"."default",
    "handle_user_name" varchar(30) COLLATE "pg_catalog"."default",
    "todo_time" timestamp(6),
    "handle_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."biz_todo_item"."id" IS '主键 ID';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."item_name" IS '事项标题';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."item_content" IS '事项内容';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."module" IS '模块名称 (必须以 uri 一致)';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."task_id" IS '任务 ID';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."instance_id" IS '流程实例 ID';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."task_name" IS '任务名称 (必须以表单页面名称一致)';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."node_name" IS '节点名称';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."is_view" IS '是否查看 default 0 (0 否 1 是)';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."is_handle" IS '是否处理 default 0 (0 否 1 是)';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."todo_user_id" IS '待办人 ID';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."todo_user_name" IS '待办人名称';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."handle_user_id" IS '处理人 ID';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."handle_user_name" IS '处理人名称';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."todo_time" IS '通知时间';
COMMENT ON COLUMN "zjdata"."biz_todo_item"."handle_time" IS '处理时间';
COMMENT ON TABLE "zjdata"."biz_todo_item" IS '待办事项表';

-- ----------------------------
-- Table structure for d_c_field_relation
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."d_c_field_relation" (
    "id" int8 NOT NULL DEFAULT nextval('"zjdata".d_c_field_relation_id_seq'::regclass),
    "base_field_name" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "to_mysql" varchar(255) COLLATE "pg_catalog"."default",
    "from_mysql" varchar(255) COLLATE "pg_catalog"."default",
    "to_postgresql" varchar(255) COLLATE "pg_catalog"."default",
    "from_postgresql" varchar(255) COLLATE "pg_catalog"."default",
    "to_iceberg" varchar(255) COLLATE "pg_catalog"."default",
    "from_iceberg" varchar(255) COLLATE "pg_catalog"."default",
    "to_flink" varchar(255) COLLATE "pg_catalog"."default",
    "from_flink" varchar(255) COLLATE "pg_catalog"."default",
    "to_trino" varchar(255) COLLATE "pg_catalog"."default",
    "from_trino" varchar(255) COLLATE "pg_catalog"."default",
    "to_sqlserver" varchar(255) COLLATE "pg_catalog"."default",
    "from_sqlserver" varchar(255) COLLATE "pg_catalog"."default",
    "to_doris" varchar(255) COLLATE "pg_catalog"."default",
    "from_doris" varchar(255) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."id" IS '系统字段类型id';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."base_field_name" IS '系统字段名称';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."to_mysql" IS '转换到mysql的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."from_mysql" IS 'mysql转换到系统字段类型的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."to_postgresql" IS '转换到postgresql的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."from_postgresql" IS 'postgresql转换到系统字段类型的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."to_iceberg" IS '转换到iceberg的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."from_iceberg" IS 'iceberg转换到系统字段类型的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."to_flink" IS '转换到flink的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."from_flink" IS 'flink转换到系统字段类型的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."to_trino" IS '转换到trino的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."from_trino" IS 'trino转换到系统字段类型的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."to_sqlserver" IS '转换到sqlserver的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."from_sqlserver" IS 'sqlserver转换到系统字段类型的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."to_doris" IS '转换到doris的关系';
COMMENT ON COLUMN "zjdata"."d_c_field_relation"."from_doris" IS 'doris转换到系统字段类型的关系';
COMMENT ON TABLE "zjdata"."d_c_field_relation" IS '数据库字段';

-- ----------------------------
-- Table structure for d_c_field_relation_type
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."d_c_field_relation_type" (
    "id" int8 NOT NULL DEFAULT nextval('"zjdata".d_c_field_relation_type_id_seq'::regclass),
    "code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "name" varchar(60) COLLATE "pg_catalog"."default",
    "description" text COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."d_c_field_relation_type"."id" IS '数据库字段id';
COMMENT ON COLUMN "zjdata"."d_c_field_relation_type"."code" IS '数据库字段编码';
COMMENT ON COLUMN "zjdata"."d_c_field_relation_type"."name" IS '数据库字段名称';
COMMENT ON COLUMN "zjdata"."d_c_field_relation_type"."description" IS '数据库字段描述';
COMMENT ON TABLE "zjdata"."d_c_field_relation_type" IS '数据库字段类型';

-- ----------------------------
-- Table structure for f_d_allot_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_allot_head" (
    "transfer_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "invoices" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 5,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "temporary" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "username" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "paper_date" date NOT NULL,
    "rise_text" varchar(200) COLLATE "pg_catalog"."default",
    "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "update_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."transfer_order_num" IS '调拨单号';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."invoices" IS '单据类别';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."statu" IS '单据状态';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."temporary" IS '暂存';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."username" IS '单据创建人';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."paper_date" IS '凭证日期';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."rise_text" IS '抬头文本';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."create_by" IS '创建者';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."update_by" IS '更新者';
COMMENT ON COLUMN "zjdata"."f_d_allot_head"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for f_d_allot_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_allot_row" (
    "transfer_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "transfer_order_row_num" numeric(5) NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default",
    "factory_code" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_code_ship" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_code_receive" varchar(60) COLLATE "pg_catalog"."default",
    "batch_number_ship" varchar(30) COLLATE "pg_catalog"."default",
    "batch_number_receive" varchar(30) COLLATE "pg_catalog"."default",
    "piece_ship" varchar(30) COLLATE "pg_catalog"."default",
    "piece_receive" varchar(30) COLLATE "pg_catalog"."default",
    "bin_num_ship" varchar(30) COLLATE "pg_catalog"."default",
    "bin_num_receive" varchar(30) COLLATE "pg_catalog"."default",
    "stock_statu_ship" varchar(1) COLLATE "pg_catalog"."default",
    "stock_statu_receive" varchar(1) COLLATE "pg_catalog"."default",
    "reason" varchar(200) COLLATE "pg_catalog"."default",
    "transfer_quantity" numeric(13,3),
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."transfer_order_num" IS '调拨单号';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."transfer_order_row_num" IS '调拨单行号';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."factory_code" IS '工厂';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."stock_p_code_ship" IS '发出库存地点';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."stock_p_code_receive" IS '收回库存地点';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."batch_number_ship" IS '发出批次号';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."batch_number_receive" IS '收回批次号';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."piece_ship" IS '发出片号';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."piece_receive" IS '收回片号';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."bin_num_ship" IS '发出BIN号';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."bin_num_receive" IS '收回BIN号';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."stock_statu_ship" IS '发出库存状态';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."stock_statu_receive" IS '收货库存状态';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."reason" IS '处理原因';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."transfer_quantity" IS '调拨数量';
COMMENT ON COLUMN "zjdata"."f_d_allot_row"."basic_unit" IS '基本单位';

-- ----------------------------
-- Table structure for f_d_batch_dimensionality
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_batch_dimensionality" (
    "manage_dimension" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_batch_dimensionality"."manage_dimension" IS '管理维度';

-- ----------------------------
-- Table structure for f_d_batch_property_config
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_batch_property_config" (
    "attribute_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_desc" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "field_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "lenth" numeric(3) NOT NULL,
    "decimal" numeric(1) NOT NULL,
    "id" int8 NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_batch_property_config"."attribute_name" IS '属性名';
COMMENT ON COLUMN "zjdata"."f_d_batch_property_config"."attribute_desc" IS '属性描述';
COMMENT ON COLUMN "zjdata"."f_d_batch_property_config"."field_type" IS '字段类型1-字符串；2-整数；3-浮点数；4-日期；5-时间';
COMMENT ON COLUMN "zjdata"."f_d_batch_property_config"."lenth" IS '长度';
COMMENT ON COLUMN "zjdata"."f_d_batch_property_config"."decimal" IS '小数位';

-- ----------------------------
-- Table structure for f_d_batch_serial_number
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_batch_serial_number" (
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "date" date NOT NULL,
    "max_number" varchar(4) COLLATE "pg_catalog"."default",
    "version" int8 NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_batch_serial_number"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_batch_serial_number"."factory_code" IS '工厂代码';
COMMENT ON COLUMN "zjdata"."f_d_batch_serial_number"."date" IS '日期';
COMMENT ON COLUMN "zjdata"."f_d_batch_serial_number"."version" IS '版本';

-- ----------------------------
-- Table structure for f_d_bin_property_config
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_bin_property_config" (
    "attribute_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_desc" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "field_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "lenth" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "decimal" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "id" int8 NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_bin_property_config"."attribute_name" IS '属性名';
COMMENT ON COLUMN "zjdata"."f_d_bin_property_config"."attribute_desc" IS '属性描述';
COMMENT ON COLUMN "zjdata"."f_d_bin_property_config"."field_type" IS '字段类型';
COMMENT ON COLUMN "zjdata"."f_d_bin_property_config"."lenth" IS '长度';
COMMENT ON COLUMN "zjdata"."f_d_bin_property_config"."decimal" IS '小数位';

-- ----------------------------
-- Table structure for f_d_execution_schedule_wip
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_execution_schedule_wip" (
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "manuf_code" varchar(10) COLLATE "pg_catalog"."default",
    "manuf_type" varchar(1) COLLATE "pg_catalog"."default",
    "process" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "flow" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "flow_versions" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "work_order_row_num" numeric(5) NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "serial_num" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "stage" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "predict_out_date" date,
    "quantity_predict" numeric(13,3),
    "quantity_actual" numeric(13,3)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."supplier_code" IS '供应商编码';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."manuf_code" IS '制造商代码';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."manuf_type" IS '制造商类别';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."process" IS '制程';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."flow" IS 'FLOW';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."flow_versions" IS 'FLOW版本';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."statu" IS 'FLOW状态';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."work_order_row_num" IS '工单行号';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."material_code" IS '收货物料编码';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."batch_number" IS '收货批次号';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."serial_num" IS 'stage序号';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."stage" IS 'stage';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."predict_out_date" IS '预计产出日期';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."quantity_predict" IS '预计产出数量';
COMMENT ON COLUMN "zjdata"."f_d_execution_schedule_wip"."quantity_actual" IS '数量';

-- ----------------------------
-- Table structure for f_d_flow_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_flow_head" (
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "manuf_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "manuf_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "process" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "flow" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "flow_versions" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "is_universal" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "head_id" int4 NOT NULL,
    "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "update_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_flow_head"."create_by" IS '创建者';
COMMENT ON COLUMN "zjdata"."f_d_flow_head"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."f_d_flow_head"."update_by" IS '更新者';
COMMENT ON COLUMN "zjdata"."f_d_flow_head"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for f_d_flow_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_flow_row" (
    "head_id" int4 NOT NULL,
    "serial_num" varchar(4) COLLATE "pg_catalog"."default" NOT NULL,
    "operation" varchar(50) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_flow_row"."head_id" IS '头表id';
COMMENT ON COLUMN "zjdata"."f_d_flow_row"."serial_num" IS '步骤';
COMMENT ON COLUMN "zjdata"."f_d_flow_row"."operation" IS '步骤描述';

-- ----------------------------
-- Table structure for f_d_get_return_material_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_get_return_material_head" (
    "pick_return_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "invoices" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 6,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "temporary" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "username" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "paper_date" date NOT NULL,
    "rise_text" varchar(200) COLLATE "pg_catalog"."default",
    "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "update_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."pick_return_order_num" IS '领退料单号';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."invoices" IS '单据类别';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."statu" IS '单据状态';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."temporary" IS '暂存';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."username" IS '单据创建人';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."paper_date" IS '凭证日期';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."rise_text" IS '抬头文本';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."create_by" IS '创建者';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."update_by" IS '更新者';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_head"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for f_d_get_return_material_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_get_return_material_row" (
    "pick_return_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "pick_return_order_row_num" numeric(5) NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "pick_return_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default",
    "factory_code" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default",
    "batch_number" varchar(30) COLLATE "pg_catalog"."default",
    "piece" varchar(30) COLLATE "pg_catalog"."default",
    "bin_num" varchar(30) COLLATE "pg_catalog"."default",
    "reason" varchar(200) COLLATE "pg_catalog"."default",
    "pick_return_quantity" numeric(13,3),
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."pick_return_order_num" IS '领退料单号';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."pick_return_order_row_num" IS '领退料单行号';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."pick_return_type" IS '领退类型';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."factory_code" IS '工厂';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."stock_p_code" IS '库存地点';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."batch_number" IS '批次号';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."piece" IS '片号';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."bin_num" IS 'BIN号';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."reason" IS '处理原因';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."pick_return_quantity" IS '领退数量';
COMMENT ON COLUMN "zjdata"."f_d_get_return_material_row"."basic_unit" IS '基本单位';

-- ----------------------------
-- Table structure for f_d_material_batch_property
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_material_batch_property" (
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "serial_num" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "value" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_desc" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_material_batch_property"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_material_batch_property"."factory_code" IS '工厂';
COMMENT ON COLUMN "zjdata"."f_d_material_batch_property"."batch_number" IS '批次号';
COMMENT ON COLUMN "zjdata"."f_d_material_batch_property"."attribute_name" IS '属性名';
COMMENT ON COLUMN "zjdata"."f_d_material_batch_property"."serial_num" IS '值序号';
COMMENT ON COLUMN "zjdata"."f_d_material_batch_property"."value" IS '值';
COMMENT ON COLUMN "zjdata"."f_d_material_batch_property"."attribute_desc" IS '属性描述';

-- ----------------------------
-- Table structure for f_d_material_bin_property
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_material_bin_property" (
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "piece" varchar(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "bin_num" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "serial_num" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "value" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_desc" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
    )
;

-- ----------------------------
-- Table structure for f_d_material_piece_property
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_material_piece_property" (
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "piece" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "serial_num" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "value" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_desc" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_material_piece_property"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_material_piece_property"."factory_code" IS '工厂';
COMMENT ON COLUMN "zjdata"."f_d_material_piece_property"."batch_number" IS '批次号';
COMMENT ON COLUMN "zjdata"."f_d_material_piece_property"."piece" IS '片号';
COMMENT ON COLUMN "zjdata"."f_d_material_piece_property"."attribute_name" IS '属性名';
COMMENT ON COLUMN "zjdata"."f_d_material_piece_property"."serial_num" IS '值序号';
COMMENT ON COLUMN "zjdata"."f_d_material_piece_property"."value" IS '值';

-- ----------------------------
-- Table structure for f_d_move_type
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_move_type" (
    "move_type" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "move_type_desc" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
    )
;

-- ----------------------------
-- Table structure for f_d_piece_config
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_piece_config" (
    "attribute_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_desc" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "field_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "lenth" numeric(3) NOT NULL,
    "decimal" numeric(1) NOT NULL,
    "id" int8 NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_piece_config"."attribute_name" IS '属性名';
COMMENT ON COLUMN "zjdata"."f_d_piece_config"."attribute_desc" IS '属性描述';
COMMENT ON COLUMN "zjdata"."f_d_piece_config"."field_type" IS '字段类型';
COMMENT ON COLUMN "zjdata"."f_d_piece_config"."lenth" IS '长度';
COMMENT ON COLUMN "zjdata"."f_d_piece_config"."decimal" IS '小数位';

-- ----------------------------
-- Table structure for f_d_post_certificate_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_post_certificate_head" (
    "voucher_num" int4 NOT NULL DEFAULT nextval('"zjdata".f_d_post_certificate_head_voucher_num_seq'::regclass),
    "voucher_vintage" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "post_date" date NOT NULL,
    "entering_date" date NOT NULL,
    "entering_time" time(6) NOT NULL,
    "username" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "rise_text" varchar(200) COLLATE "pg_catalog"."default",
    "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "update_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."voucher_num" IS '过账凭证号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."voucher_vintage" IS '过账凭证年份';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."post_date" IS '过账日期';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."entering_date" IS '录入日期';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."entering_time" IS '录入时间';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."username" IS '操作账户名';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."rise_text" IS '凭证抬头文本';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."create_by" IS '创建者';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."update_by" IS '更新者';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_head"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for f_d_post_certificate_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_post_certificate_row" (
    "voucher_num" int4 NOT NULL,
    "voucher_vintage" varchar(4) COLLATE "pg_catalog"."default" NOT NULL,
    "voucher_row_num" numeric(5) NOT NULL,
    "invoices" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 8,
    "move_type" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code_ship" varchar(60) COLLATE "pg_catalog"."default",
    "factory_code_receive" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_code_ship" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_code_receive" varchar(60) COLLATE "pg_catalog"."default",
    "batch_number_ship" varchar(30) COLLATE "pg_catalog"."default",
    "batch_number_receive" varchar(30) COLLATE "pg_catalog"."default",
    "piece_ship" varchar(30) COLLATE "pg_catalog"."default",
    "piece_receive" varchar(30) COLLATE "pg_catalog"."default",
    "bin_num_ship" varchar(30) COLLATE "pg_catalog"."default",
    "bin_num_receive" varchar(30) COLLATE "pg_catalog"."default",
    "post_quantity" numeric(13,3) NOT NULL,
    "post_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "invoices_reference" varchar(10) COLLATE "pg_catalog"."default",
    "order_num" varchar(20) COLLATE "pg_catalog"."default",
    "order_row_num" numeric(5),
    "subproject_num" numeric(5),
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "work_order_row_num" numeric(5),
    "work_order_children_num" numeric(5),
    "transfer_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "transfer_order_row_num" numeric(5),
    "pick_return_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "pick_return_order_row_num" numeric(5),
    "scrap_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "scrap_order_row_num" numeric(5),
    "ship_request_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "ship_request_order_row_num" numeric(5),
    "voucher_num_reference" varchar(12) COLLATE "pg_catalog"."default",
    "voucher_vintage_reference" varchar(4) COLLATE "pg_catalog"."default",
    "voucher_row_num_reference" numeric(5),
    "write_off" varchar(1) COLLATE "pg_catalog"."default",
    "row_text" varchar(200) COLLATE "pg_catalog"."default",
    "fd_quantity" numeric(13,3),
    "stock_statu_ship" varchar(1) COLLATE "pg_catalog"."default",
    "stock_statu_receive" varchar(1) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."voucher_num" IS '过账凭证号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."voucher_vintage" IS '过账凭证年份';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."voucher_row_num" IS '过账凭证行号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."invoices" IS '单据类别';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."move_type" IS '移动类型';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."factory_code_ship" IS '发出工厂';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."factory_code_receive" IS '收回工厂';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."stock_p_code_ship" IS '发出库存地点';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."stock_p_code_receive" IS '收回库存地点';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."batch_number_ship" IS '发出批次';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."batch_number_receive" IS '收回批次';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."piece_ship" IS '发出片号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."piece_receive" IS '收回片号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."bin_num_ship" IS '发出BIN号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."bin_num_receive" IS '收回BIN号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."post_quantity" IS '过账数量';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."post_unit" IS '过账单位';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."invoices_reference" IS '参考单据类别';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."order_num" IS '采购合同/订单号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."order_row_num" IS '采购合同/订单行号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."subproject_num" IS '采购订单子件项目号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."work_order_row_num" IS '工单行号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."work_order_children_num" IS '工单子件项目号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."transfer_order_num" IS '调拨单号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."transfer_order_row_num" IS '调拨单行号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."pick_return_order_num" IS '领退料单号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."pick_return_order_row_num" IS '领退料单行号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."scrap_order_num" IS '报废单号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."scrap_order_row_num" IS '报废单行号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."ship_request_order_num" IS '出货需求单号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."ship_request_order_row_num" IS '出货需求单行号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."voucher_num_reference" IS '参考过账凭证号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."voucher_vintage_reference" IS '参考过账凭证年份';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."voucher_row_num_reference" IS '参考过账凭证行号';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."write_off" IS '是否被冲销';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."row_text" IS '凭证行项目文本';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."fd_quantity" IS '不良品数量';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."stock_statu_ship" IS '发出库存状态';
COMMENT ON COLUMN "zjdata"."f_d_post_certificate_row"."stock_statu_receive" IS '收货库存状态';

-- ----------------------------
-- Table structure for f_d_provisional_single_out
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_provisional_single_out" (
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "work_order_row_num" numeric(5) NOT NULL,
    "work_order_children_num" numeric(5) NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "material_desc" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "piece" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "bin_num" varchar(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "quantity_delivery" numeric(13,3) NOT NULL,
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "quantity_delivery_versions" numeric(13)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."work_order_row_num" IS '工单行号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."work_order_children_num" IS '工单子件项目号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."material_code" IS '发料物料编码';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."material_desc" IS '发料物料描述';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."factory_code" IS '发料工厂';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."stock_p_code" IS '发料库存地点';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."batch_number" IS '发料批次号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."piece" IS '发料片号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."bin_num" IS '发料BIN号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."quantity_delivery" IS '发料数量';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out"."quantity_delivery_versions" IS '历史发料数量';

-- ----------------------------
-- Table structure for f_d_provisional_single_out_cache
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_provisional_single_out_cache" (
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "work_order_row_num" numeric(5) NOT NULL,
    "work_order_children_num" numeric(5) NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "material_desc" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "piece" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "bin_num" varchar(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "quantity_delivery" numeric(13,3) NOT NULL,
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "quantity_delivery_versions" numeric(13)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."work_order_row_num" IS '工单行号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."work_order_children_num" IS '工单子件项目号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."material_code" IS '发料物料编码';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."material_desc" IS '发料物料描述';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."factory_code" IS '发料工厂';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."stock_p_code" IS '发料库存地点';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."batch_number" IS '发料批次号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."piece" IS '发料片号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."bin_num" IS '发料BIN号';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."quantity_delivery" IS '发料数量';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."f_d_provisional_single_out_cache"."quantity_delivery_versions" IS '历史发料数量';

-- ----------------------------
-- Table structure for f_d_receipts_state
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_receipts_state" (
    "paper_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "statu_desc" varchar(20) COLLATE "pg_catalog"."default",
    "id" int8
    )
;
COMMENT ON COLUMN "zjdata"."f_d_receipts_state"."paper_type" IS '单据类别';
COMMENT ON COLUMN "zjdata"."f_d_receipts_state"."statu" IS '单据状态值';
COMMENT ON COLUMN "zjdata"."f_d_receipts_state"."statu_desc" IS '单据状态值描述';

-- ----------------------------
-- Table structure for f_d_repertory
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_repertory" (
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "stock_b_p_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "piece" varchar(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "bin_num" varchar(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "unrestricted_stock" numeric(13,3) NOT NULL,
    "freeze_stock" numeric(13,3) NOT NULL DEFAULT 0.000,
    "process_stock" numeric(13,3) NOT NULL DEFAULT 0.000,
    "initial_num" numeric(13,3) NOT NULL,
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_repertory"."factory_code" IS '工厂';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."stock_p_code" IS '库存地点';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."stock_b_p_code" IS '库位';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."batch_number" IS '批次号';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."piece" IS '片号';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."bin_num" IS 'BIN号';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."unrestricted_stock" IS '非限制库存数量';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."freeze_stock" IS '冻结库存数量';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."process_stock" IS '加工锁定库存数量';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."initial_num" IS '入库时初始数量';
COMMENT ON COLUMN "zjdata"."f_d_repertory"."basic_unit" IS '基本单位';

-- ----------------------------
-- Table structure for f_d_scrap_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_scrap_head" (
    "scrap_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "invoices" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 7,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "temporary" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "username" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "paper_date" date NOT NULL,
    "rise_text" varchar(200) COLLATE "pg_catalog"."default",
    "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "update_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."scrap_order_num" IS '报废单号';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."invoices" IS '单据类别';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."statu" IS '单据状态';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."temporary" IS '暂存';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."username" IS '单据创建人';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."paper_date" IS '凭证日期';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."rise_text" IS '抬头文本';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."create_by" IS '创建者';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."update_by" IS '更新者';
COMMENT ON COLUMN "zjdata"."f_d_scrap_head"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for f_d_scrap_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_scrap_row" (
    "scrap_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "scrap_order_row_num" numeric(5) NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default",
    "factory_code" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default",
    "batch_number" varchar(30) COLLATE "pg_catalog"."default",
    "piece" varchar(30) COLLATE "pg_catalog"."default",
    "bin_num" varchar(30) COLLATE "pg_catalog"."default",
    "reason" varchar(200) COLLATE "pg_catalog"."default",
    "scrap_quantity" numeric(13,3),
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."scrap_order_num" IS '报废单号';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."scrap_order_row_num" IS '报废单行号';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."factory_code" IS '工厂';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."stock_p_code" IS '库存地点';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."batch_number" IS '批次号';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."piece" IS '片号';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."bin_num" IS 'BIN号';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."reason" IS '处理原因';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."scrap_quantity" IS '报废数量';
COMMENT ON COLUMN "zjdata"."f_d_scrap_row"."basic_unit" IS '基本单位';

-- ----------------------------
-- Table structure for f_d_segment_config
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_segment_config" (
    "type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "start_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "end_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "id" int8 NOT NULL,
    "now_num" varchar(20) COLLATE "pg_catalog"."default",
    "version" int4 NOT NULL DEFAULT 1
    )
;
COMMENT ON COLUMN "zjdata"."f_d_segment_config"."type" IS '工单类型';
COMMENT ON COLUMN "zjdata"."f_d_segment_config"."start_num" IS '起始号码';
COMMENT ON COLUMN "zjdata"."f_d_segment_config"."end_num" IS '终止号码';
COMMENT ON COLUMN "zjdata"."f_d_segment_config"."now_num" IS '当前号码';
COMMENT ON COLUMN "zjdata"."f_d_segment_config"."version" IS '当前版本';

-- ----------------------------
-- Table structure for f_d_ship_plan_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_ship_plan_head" (
    "ship_plan_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "sale_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "customer_purchase_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "sale_to" varchar(100) COLLATE "pg_catalog"."default",
    "ship_to" varchar(100) COLLATE "pg_catalog"."default",
    "ship_to_addr" varchar(200) COLLATE "pg_catalog"."default",
    "ship_to_linkman" varchar(10) COLLATE "pg_catalog"."default",
    "ship_to_phone" varchar(30) COLLATE "pg_catalog"."default",
    "incoterms" varchar(10) COLLATE "pg_catalog"."default",
    "incoterms_position" varchar(30) COLLATE "pg_catalog"."default",
    "comment" varchar(200) COLLATE "pg_catalog"."default",
    "invoices" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 2,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "create_by" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "update_date" date NOT NULL,
    "update_time" time(6) NOT NULL,
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
    )
;
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."ship_plan_order_num" IS '出货计划单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."sale_order_num" IS '销售订单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."customer_purchase_order_num" IS '客户订单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."sale_to" IS '售达方';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."ship_to" IS '送达方';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."ship_to_addr" IS '送达方地址';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."ship_to_linkman" IS '送达方联系人';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."ship_to_phone" IS '送达方联系电话';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."incoterms" IS '国际贸易术语';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."incoterms_position" IS '国际贸易术语位置';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."comment" IS '抬头备注';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."invoices" IS '单据类别';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."statu" IS '单据状态';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."create_by" IS '创建人';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."update_date" IS '最后维护日期';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."update_time" IS '最后维护时间';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_head"."update_by" IS '更新者';

-- ----------------------------
-- Table structure for f_d_ship_plan_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_ship_plan_row" (
    "ship_plan_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "ship_plan_order_row_num" numeric(5) NOT NULL,
    "sale_order_row_num" numeric(5),
    "sale_order_plan_row__num" numeric(5),
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "delivery_date" date NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "device_name" varchar(200) COLLATE "pg_catalog"."default",
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default",
    "batch_number" varchar(30) COLLATE "pg_catalog"."default",
    "quantity_delivery" numeric(13,3) NOT NULL,
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "is_return" varchar(1) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."ship_plan_order_num" IS '出货计划单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."ship_plan_order_row_num" IS '出货计划单行号';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."sale_order_row_num" IS '销售订单行号';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."sale_order_plan_row__num" IS '销售订单计划行号';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."delivery_date" IS '计划交货日期';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."device_name" IS '产品名';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."factory_code" IS '工厂';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."stock_p_code" IS '库存地点';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."batch_number" IS '批次号';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."quantity_delivery" IS '交货数量';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."f_d_ship_plan_row"."is_return" IS '是否退运';

-- ----------------------------
-- Table structure for f_d_ship_request_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_ship_request_head" (
    "ship_request_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "ship_plan_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "sale_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "customer_purchase_order_num" varchar(20) COLLATE "pg_catalog"."default",
    "sale_to" varchar(100) COLLATE "pg_catalog"."default",
    "ship_to" varchar(100) COLLATE "pg_catalog"."default",
    "ship_to_addr" varchar(200) COLLATE "pg_catalog"."default",
    "ship_to_linkman" varchar(10) COLLATE "pg_catalog"."default",
    "ship_to_phone" varchar(30) COLLATE "pg_catalog"."default",
    "incoterms" varchar(10) COLLATE "pg_catalog"."default",
    "incoterms_position" varchar(30) COLLATE "pg_catalog"."default",
    "invoices" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 3,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "temporary" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "username" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "paper_date" date NOT NULL,
    "rise_text" varchar(200) COLLATE "pg_catalog"."default",
    "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "update_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."ship_request_order_num" IS '出货需求单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."ship_plan_order_num" IS '出货计划单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."sale_order_num" IS '销售订单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."customer_purchase_order_num" IS '客户订单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."sale_to" IS '售达方';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."ship_to" IS '送达方';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."ship_to_addr" IS '送达方地址';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."ship_to_linkman" IS '送达方联系人';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."ship_to_phone" IS '送达方联系电话';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."incoterms" IS '国际贸易术语';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."incoterms_position" IS '国际贸易术语位置';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."invoices" IS '单据类别';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."statu" IS '单据状态';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."temporary" IS '暂存';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."username" IS '单据创建人';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."paper_date" IS '凭证日期';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."rise_text" IS '抬头文本';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."create_by" IS '创建者';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."update_by" IS '更新者';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_head"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for f_d_ship_request_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_ship_request_row" (
    "ship_request_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "ship_request_order_row_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "ship_plan_order_row_num" numeric(5),
    "sale_order_row_num" numeric(5),
    "sale_order_plan_row_num" numeric(5),
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "delivery_date" date,
    "material_code" varchar(40) COLLATE "pg_catalog"."default",
    "device_name" varchar(200) COLLATE "pg_catalog"."default",
    "factory_code" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default",
    "batch_number" varchar(30) COLLATE "pg_catalog"."default",
    "piece" varchar(30) COLLATE "pg_catalog"."default",
    "bin_num" varchar(30) COLLATE "pg_catalog"."default",
    "quantity_delivery" numeric(13,3),
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default",
    "is_return" varchar(1) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."ship_request_order_num" IS '出货需求单号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."ship_request_order_row_num" IS '出货需求单行号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."ship_plan_order_row_num" IS '出货计划单行号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."sale_order_row_num" IS '销售订单行号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."sale_order_plan_row_num" IS '销售订单计划行号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."delivery_date" IS '计划交货日期';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."device_name" IS '产品名';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."factory_code" IS '工厂';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."stock_p_code" IS '库存地点';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."batch_number" IS '批次号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."piece" IS '片号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."bin_num" IS 'BIN号';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."quantity_delivery" IS '交货数量';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."f_d_ship_request_row"."is_return" IS '是否退运';

-- ----------------------------
-- Table structure for f_d_supplier_linkman
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_supplier_linkman" (
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "manuf_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "manuf_type" varchar(4) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "serial_num" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "phone" varchar(30) COLLATE "pg_catalog"."default",
    "email" varchar(100) COLLATE "pg_catalog"."default",
    "department" varchar(30) COLLATE "pg_catalog"."default",
    "post" varchar(30) COLLATE "pg_catalog"."default",
    "default_linkman" varchar(1) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."supplier_code" IS '供应商编码
';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."manuf_code" IS '制造商代码';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."manuf_type" IS '制造商类别';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."serial_num" IS '联系人序号';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."name" IS '联系人姓名';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."phone" IS '联系电话';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."email" IS '联系邮箱';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."department" IS '联系人部门';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."post" IS '联系人岗位';
COMMENT ON COLUMN "zjdata"."f_d_supplier_linkman"."default_linkman" IS '是否默认联系人';

-- ----------------------------
-- Table structure for f_d_work_order_child
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_child" (
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "work_order_row_num" numeric(5) NOT NULL,
    "work_order_children_num" numeric(5) NOT NULL,
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_description" text COLLATE "pg_catalog"."default",
    "material_code" varchar(40) COLLATE "pg_catalog"."default",
    "material_desc" varchar(200) COLLATE "pg_catalog"."default",
    "quantity_delivery" numeric(13,3),
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default",
    "batch_number" varchar(30) COLLATE "pg_catalog"."default",
    "father_count" int4,
    "children_count" int4,
    "is_master_chip" varchar(1) COLLATE "pg_catalog"."default",
    "be_substituted_row_num" varchar(255) COLLATE "pg_catalog"."default",
    "is_substitute" varchar(1) COLLATE "pg_catalog"."default"

    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."work_order_row_num" IS '工单行号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."work_order_children_num" IS '工单子件项目号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."stock_p_code" IS '发料库存地点';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."stock_p_description" IS '发料库存地点描述';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."material_code" IS '发料物料编码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."material_desc" IS '发料物料描述';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."quantity_delivery" IS '发料数量';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."batch_number" IS '发料批次号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."father_count" IS '父件基本数量';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."children_count" IS '组件用量';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."is_master_chip" IS '是否主芯片';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."is_substitute" IS '是否替代料';
COMMENT ON COLUMN "zjdata"."f_d_work_order_child"."be_substituted_row_num" IS '被替代料件行号';


-- ----------------------------
-- Table structure for f_d_work_order_config
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_config" (
    "work_order_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "work_order_type_desc" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "id" int8 NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_config"."work_order_type" IS '工单类型存储表';
COMMENT ON COLUMN "zjdata"."f_d_work_order_config"."work_order_type_desc" IS '工单类型存储表';

-- ----------------------------
-- Table structure for f_d_work_order_field_config
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_field_config" (
    "work_order_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "process" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "screen_mode" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "field" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "location" varchar(2) COLLATE "pg_catalog"."default" NOT NULL,
    "field_desc" varchar(100) COLLATE "pg_catalog"."default",
    "control" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "location_xy" varchar(3) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_field_config"."work_order_type" IS '工单类型';
COMMENT ON COLUMN "zjdata"."f_d_work_order_field_config"."process" IS '委外制程';
COMMENT ON COLUMN "zjdata"."f_d_work_order_field_config"."screen_mode" IS '屏幕模式';
COMMENT ON COLUMN "zjdata"."f_d_work_order_field_config"."statu" IS '单据状态';
COMMENT ON COLUMN "zjdata"."f_d_work_order_field_config"."field" IS '字段名';
COMMENT ON COLUMN "zjdata"."f_d_work_order_field_config"."field_desc" IS '字段描述';
COMMENT ON COLUMN "zjdata"."f_d_work_order_field_config"."location_xy" IS '抬头字段坐标位置';

-- ----------------------------
-- Table structure for f_d_work_order_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_head" (
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "paper_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 1,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "work_order_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "temporary" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "username" varchar COLLATE "pg_catalog"."default" NOT NULL,
    "purchase_linkman" varchar(10) COLLATE "pg_catalog"."default",
    "purchase_phone" varchar(30) COLLATE "pg_catalog"."default",
    "purchase_email" varchar(100) COLLATE "pg_catalog"."default",
    "paper_date" date NOT NULL,
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default",
    "manuf_code" varchar(10) COLLATE "pg_catalog"."default",
    "manuf_type" varchar(1) COLLATE "pg_catalog"."default",
    "purchase_code" varchar(60) COLLATE "pg_catalog"."default",
    "factory_code" varchar(60) COLLATE "pg_catalog"."default",
    "process" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "rework" varchar(1) COLLATE "pg_catalog"."default",
    "produce_statu" varchar(10) COLLATE "pg_catalog"."default",
    "head_note" varchar(200) COLLATE "pg_catalog"."default",
    "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
    "update_time" timestamp(6)
    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."paper_type" IS '单据类别';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."statu" IS '单据状态';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."work_order_type" IS '工单类型';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."temporary" IS '暂存';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."username" IS '单据创建人';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."purchase_linkman" IS '采购方联系人';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."purchase_phone" IS '采购方联系电话';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."purchase_email" IS '采购方联系邮箱';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."paper_date" IS '凭证日期';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."supplier_code" IS '供应商编码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."manuf_code" IS '制造商代码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."manuf_type" IS '制造商类别';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."purchase_code" IS '采购组织';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."factory_code" IS '工厂代码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."process" IS '委外制程';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."rework" IS '是否返工';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."produce_statu" IS '生产状态';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."head_note" IS '抬头备注';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."create_by" IS '创建者';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."create_time" IS '创建时间';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."update_by" IS '更新者';
COMMENT ON COLUMN "zjdata"."f_d_work_order_head"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for f_d_work_order_production_config
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_production_config" (
    "work_order_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "produce_statu" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "produce_statu_desc" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "id" int8
    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_production_config"."work_order_type" IS '工单类型';
COMMENT ON COLUMN "zjdata"."f_d_work_order_production_config"."produce_statu" IS '生产状态';
COMMENT ON COLUMN "zjdata"."f_d_work_order_production_config"."produce_statu_desc" IS '生产状态描述';

-- ----------------------------
-- Table structure for f_d_work_order_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_row" (
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "work_order_row_num" numeric(5) NOT NULL,
    "order_num" varchar(20) COLLATE "pg_catalog"."default",
    "order_row_num" numeric(5),
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default",
    "material_desc" varchar(200) COLLATE "pg_catalog"."default",
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_description" text COLLATE "pg_catalog"."default",
    "quantity_receive" numeric(13,3),
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default",
    "package_way" varchar(200) COLLATE "pg_catalog"."default",
    "mark" varchar(200) COLLATE "pg_catalog"."default",
    "delivery_limit" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "excessive_delivery" numeric(3,1),
    "insufficient_delivery" numeric(3,1),
    "delivery_date" date,
    "encapsulation_versions" varchar(3) COLLATE "pg_catalog"."default",
    "wire_rod" varchar(100) COLLATE "pg_catalog"."default",
    "bd_graph" varchar(100) COLLATE "pg_catalog"."default",
    "bd_graph_versions" varchar(100) COLLATE "pg_catalog"."default",
    "wire_size" varchar(100) COLLATE "pg_catalog"."default",
    "number" varchar(100) COLLATE "pg_catalog"."default",
    "slices_way" varchar(100) COLLATE "pg_catalog"."default",
    "dedicated" varchar(100) COLLATE "pg_catalog"."default",
    "sticker_way" varchar(100) COLLATE "pg_catalog"."default",
    "encapsulation_mode" varchar(100) COLLATE "pg_catalog"."default",
    "test_routines" varchar(255) COLLATE "pg_catalog"."default",
    "bom_versions" varchar(255) COLLATE "pg_catalog"."default",
    "status" varchar(255) COLLATE "pg_catalog"."default",
    "batch_number" varchar(30) COLLATE "pg_catalog"."default",
    "row_note" varchar(200) COLLATE "pg_catalog"."default",
    "process" varchar(10) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."work_order_row_num" IS '工单行号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."order_num" IS '采购合同/订单号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."order_row_num" IS '采购合同/订单行号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."material_code" IS '收货物料编码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."material_desc" IS '收货物料描述';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."stock_p_code" IS '收货库存地点';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."stock_p_description" IS '收货库存地点描述';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."quantity_receive" IS '预计收货数量';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."package_way" IS '包装方式';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."mark" IS '芯片丝印';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."delivery_limit" IS '交货限制';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."excessive_delivery" IS '过量交货容差';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."insufficient_delivery" IS '交货不足容差';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."delivery_date" IS '需求交货日期';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."encapsulation_versions" IS '封裝信息版本';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."wire_rod" IS '线材';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."bd_graph" IS '打线图（BD图）';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."bd_graph_versions" IS '打线图版本';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."wire_size" IS '焊丝尺寸（线径）';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."number" IS '印章文件编号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."slices_way" IS '取片方式';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."dedicated" IS '框架是否专用';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."sticker_way" IS '贴膜方式';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."encapsulation_mode" IS '封装形式';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."test_routines" IS '测试程序';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."bom_versions" IS 'BOM版本';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."status" IS 'BOM状态';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."batch_number" IS '收货批次号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."row_note" IS '行项目备注';
COMMENT ON COLUMN "zjdata"."f_d_work_order_row"."process" IS '制程';


-- ----------------------------
-- Table structure for f_d_work_order_segment_config
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_segment_config" (
    "work_order_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "encode" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "start_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "end_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "id" int8 NOT NULL,
    "now_num" varchar(20) COLLATE "pg_catalog"."default",
    "version" int4 NOT NULL DEFAULT 1
    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_segment_config"."work_order_type" IS '工单类型';
COMMENT ON COLUMN "zjdata"."f_d_work_order_segment_config"."encode" IS '给号方式';
COMMENT ON COLUMN "zjdata"."f_d_work_order_segment_config"."start_num" IS '起始号码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_segment_config"."end_num" IS '终止号码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_segment_config"."now_num" IS '当前号码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_segment_config"."version" IS '当前版本';

-- ----------------------------
-- Table structure for f_d_work_order_store_issue
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_store_issue" (
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 0,
    "work_order_row_num" numeric(5) NOT NULL,
    "work_order_children_num" numeric(5) NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "material_desc" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "piece" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "bin_num" varchar(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "quantity_delivery" numeric(13,3) NOT NULL,
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "quantity_consume" numeric(13,3) NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."work_order_row_num" IS '工单行号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."work_order_children_num" IS '工单子件项目号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."material_code" IS '发料物料编码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."material_desc" IS '发料物料描述';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."factory_code" IS '发料工厂';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."stock_p_code" IS '发料库存地点';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."batch_number" IS '发料批次号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."piece" IS '发料片号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."bin_num" IS '发料BIN号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."quantity_delivery" IS '发料数量';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."f_d_work_order_store_issue"."quantity_consume" IS '已消耗数量';

-- ----------------------------
-- Table structure for f_d_work_order_test
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_d_work_order_test" (
    "work_order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "work_order_row_num" numeric(5) NOT NULL,
    "item_num" numeric(5) NOT NULL,
    "test_routine" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "test_versions" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "combined_code" varchar(100) COLLATE "pg_catalog"."default",
    "temperature" varchar(100) COLLATE "pg_catalog"."default",
    "test_mode" varchar(100) COLLATE "pg_catalog"."default",
    "explain" varchar(100) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."work_order_num" IS '工单号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."work_order_row_num" IS '工单行号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."item_num" IS '测试程序序号';
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."test_routine" IS '测试程序';
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."test_versions" IS '测试程序版本';
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."combined_code" IS '组合代码';
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."temperature" IS '测试环境（温度）';
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."test_mode" IS '测试方式';
COMMENT ON COLUMN "zjdata"."f_d_work_order_test"."explain" IS '程序说明';

-- ----------------------------
-- Table structure for f_g_c_cache_data
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."f_g_c_cache_data" (
    "id" int8 NOT NULL DEFAULT nextval('"zjdata".f_g_c_cache_data_id_seq'::regclass),
    "cachecode" varchar(255) COLLATE "pg_catalog"."default",
    "jobid" varchar(255) COLLATE "pg_catalog"."default",
    "status" bool
    )
;
COMMENT ON COLUMN "zjdata"."f_g_c_cache_data"."id" IS '缓存数据id';
COMMENT ON COLUMN "zjdata"."f_g_c_cache_data"."cachecode" IS '缓存的code';
COMMENT ON COLUMN "zjdata"."f_g_c_cache_data"."jobid" IS '缓存任务的的jobId';
COMMENT ON COLUMN "zjdata"."f_g_c_cache_data"."status" IS '缓存任务的状态';



-- ----------------------------
-- Table structure for org_bwkey
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."org_bwkey" (
    "id" int8 NOT NULL,
    "code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "abbreviation" varchar(255) COLLATE "pg_catalog"."default",
    "description" varchar(255) COLLATE "pg_catalog"."default",
    "kokrs_id" int8,
    "create_by" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "create_time" date NOT NULL,
    "update_by" varchar(255) COLLATE "pg_catalog"."default",
    "update_time" date,
    "enable" bool NOT NULL,
    "del_flag" bool NOT NULL
    )
;

-- ----------------------------
-- Table structure for org_company
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."org_company" (
    "id" int8 NOT NULL DEFAULT nextval('"zjdata".org_company_id_seq'::regclass),
    "company_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "company_abbreviation" varchar(60) COLLATE "pg_catalog"."default",
    "company_description" text COLLATE "pg_catalog"."default",
    "registered_address" varchar(60) COLLATE "pg_catalog"."default",
    "kokrs_id" int8,
    "city" varchar(60) COLLATE "pg_catalog"."default",
    "country" varchar(60) COLLATE "pg_catalog"."default",
    "currency" varchar(60) COLLATE "pg_catalog"."default",
    "language" varchar(60) COLLATE "pg_catalog"."default",
    "enable" bool DEFAULT true,
    "del_flag" bool DEFAULT false,
    "create_by" varchar(64) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default",
    "update_time" timestamp(6)
    )
;

-- ----------------------------
-- Table structure for org_factory
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."org_factory" (
    "id" int8 NOT NULL DEFAULT nextval('"zjdata".org_factory_id_seq'::regclass),
    "factory_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_abbreviation" varchar(60) COLLATE "pg_catalog"."default",
    "factory_description" text COLLATE "pg_catalog"."default",
    "factory_address" varchar(60) COLLATE "pg_catalog"."default",
    "company_id" int8,
    "bwkey_id" int8,
    "country" varchar(60) COLLATE "pg_catalog"."default",
    "language" varchar(60) COLLATE "pg_catalog"."default",
    "factory_calendar" varchar(60) COLLATE "pg_catalog"."default",
    "enable" bool DEFAULT true,
    "del_flag" bool DEFAULT false,
    "create_by" varchar(64) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default",
    "update_time" timestamp(6)
    )
;

-- ----------------------------
-- Table structure for org_kokrs
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."org_kokrs" (
    "id" int8 NOT NULL,
    "code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "abbreviation" varchar(255) COLLATE "pg_catalog"."default",
    "description" text COLLATE "pg_catalog"."default",
    "create_by" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "create_time" date NOT NULL,
    "update_by" varchar(255) COLLATE "pg_catalog"."default",
    "update_time" date,
    "enable" bool,
    "del_flag" bool
    )
;
COMMENT ON COLUMN "zjdata"."org_kokrs"."id" IS '表ID，主键，供其他表做外键';
COMMENT ON COLUMN "zjdata"."org_kokrs"."code" IS '成本范围代码';

-- ----------------------------
-- Table structure for org_purchase
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."org_purchase" (
    "id" int8 NOT NULL DEFAULT nextval('"zjdata".org_purchase_id_seq'::regclass),
    "purchase_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "purchase_description" text COLLATE "pg_catalog"."default",
    "company_id" int8,
    "enable" bool DEFAULT true,
    "del_flag" bool DEFAULT false,
    "create_by" varchar(64) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default",
    "update_time" timestamp(6)
    )
;

-- ----------------------------
-- Table structure for org_purchase_group
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."org_purchase_group" (
    "id" int8 NOT NULL DEFAULT nextval('"zjdata".org_purchase_group_id_seq'::regclass),
    "purchase_g_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "purchase_g_description" text COLLATE "pg_catalog"."default",
    "enable" bool DEFAULT true,
    "del_flag" bool DEFAULT false,
    "create_by" varchar(64) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default",
    "update_time" timestamp(6)
    )
;

-- ----------------------------
-- Table structure for org_stock_bin_place
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."org_stock_bin_place" (
    "id" int8 NOT NULL,
    "stock_b_p_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "stock_b_p_description" text COLLATE "pg_catalog"."default",
    "stock_place_id" int8,
    "enable" bool NOT NULL DEFAULT true,
    "del_flag" bool NOT NULL DEFAULT false,
    "create_by" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "create_time" time(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default",
    "update_time" time(6)
    )
;

-- ----------------------------
-- Table structure for org_stock_place
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."org_stock_place" (
    "id" int8 NOT NULL DEFAULT nextval('"zjdata".org_stock_place_id_seq'::regclass),
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
    "stock_p_description" text COLLATE "pg_catalog"."default",
    "factory_id" int8,
    "enable" bool DEFAULT true,
    "del_flag" bool DEFAULT false,
    "create_by" varchar(64) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default",
    "update_time" timestamp(6),
    "bin_flag" bool
    )
;

-- ----------------------------
-- Table structure for p_d_base_material
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_base_material" (
    "id" int8,
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "manuf_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "manuf_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_material_desc" varchar(200) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."p_d_base_material"."supplier_code" IS '供应商编码 ';
COMMENT ON COLUMN "zjdata"."p_d_base_material"."manuf_code" IS '制造商代码 ';
COMMENT ON COLUMN "zjdata"."p_d_base_material"."manuf_type" IS '制造商类别
';
COMMENT ON COLUMN "zjdata"."p_d_base_material"."material_code" IS '内部物料编码
';
COMMENT ON COLUMN "zjdata"."p_d_base_material"."supplier_material_code" IS '供应商物料编码';
COMMENT ON COLUMN "zjdata"."p_d_base_material"."supplier_material_desc" IS '供应商物料描述
';

-- ----------------------------
-- Table structure for p_d_bom_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_bom_head" (
    "head_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "factory_code" varchar(255) COLLATE "pg_catalog"."default",
    "parent_material_code" varchar(255) COLLATE "pg_catalog"."default",
    "parent_material_describe" varchar(255) COLLATE "pg_catalog"."default",
    "status" varchar(255) COLLATE "pg_catalog"."default",
    "effective_start_date" date,
    "valid_deadline" date,
    "creator" varchar(255) COLLATE "pg_catalog"."default",
    "date_created" date,
    "versions" varchar(255) COLLATE "pg_catalog"."default",
    "count" int4,
    "unit" varchar(255) COLLATE "pg_catalog"."default",
    "process" varchar(10) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."head_id" IS '头表id';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."factory_code" IS '工厂代码';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."parent_material_code" IS '父件物料编码';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."parent_material_describe" IS '父件物料描述';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."status" IS 'BOM状态';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."effective_start_date" IS 'BOM有效起始日';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."valid_deadline" IS 'BOM有效截止日';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."creator" IS '创建人';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."date_created" IS '创建日期';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."versions" IS 'BOM版本';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."count" IS '父件基本数量';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."unit" IS '父件基本单位';
COMMENT ON COLUMN "zjdata"."p_d_bom_head"."process" IS '制程';

-- ----------------------------
-- Table structure for p_d_bom_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_bom_row" (
    "head_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "row_num" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "children_material_code" varchar(255) COLLATE "pg_catalog"."default",
    "children_material_describe" varchar(255) COLLATE "pg_catalog"."default",
    "count" numeric,
    "unit" varchar(255) COLLATE "pg_catalog"."default",
    "attrition_rate" varchar(255) COLLATE "pg_catalog"."default",
    "is_master_chip" varchar(255) COLLATE "pg_catalog"."default",
    "children_material_versions" varchar(255) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."head_id" IS '头表id';
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."row_num" IS '行号';
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."children_material_code" IS '子件物料编码';
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."children_material_describe" IS '子件物料描述';
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."count" IS '组件用量';
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."unit" IS '组件单位';
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."attrition_rate" IS '损耗率';
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."is_master_chip" IS '是否主芯片';
COMMENT ON COLUMN "zjdata"."p_d_bom_row"."children_material_versions" IS '子件物料BOM版本';

-- ----------------------------
-- Table structure for p_d_capsulation_info
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_capsulation_info" (
    "id" int8 NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "manuf_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "manuf_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "versions" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "wire_rod" varchar(100) COLLATE "pg_catalog"."default",
    "bd_graph" varchar(100) COLLATE "pg_catalog"."default",
    "bd_graph_versions" varchar(100) COLLATE "pg_catalog"."default",
    "wire_size" varchar(100) COLLATE "pg_catalog"."default",
    "number" varchar(100) COLLATE "pg_catalog"."default",
    "slices_way" varchar(100) COLLATE "pg_catalog"."default",
    "dedicated" varchar(100) COLLATE "pg_catalog"."default",
    "sticker_way" varchar(100) COLLATE "pg_catalog"."default",
    "encapsulation_mode" varchar(100) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."supplier_code" IS '供应商编码';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."manuf_code" IS '制造商代码';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."manuf_type" IS '制造商类别';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."versions" IS '封裝信息版本';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."statu" IS '封裝信息状态';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."wire_rod" IS '线材';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."bd_graph" IS '打线图（BD图）';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."bd_graph_versions" IS '打线图版本';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."wire_size" IS '焊丝尺寸（线径）';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."number" IS '印章文件编号';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."slices_way" IS '取片方式';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."dedicated" IS '框架是否专用';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."sticker_way" IS '贴膜方式';
COMMENT ON COLUMN "zjdata"."p_d_capsulation_info"."encapsulation_mode" IS '封装形式';

-- ----------------------------
-- Table structure for p_d_manufacturer
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_manufacturer" (
    "manuf_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "manuf_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "manuf_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "manuf_addr" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "postal_code" varchar(20) COLLATE "pg_catalog"."default",
    "update_date" date,
    "update_time" time(1)
    )
;
COMMENT ON COLUMN "zjdata"."p_d_manufacturer"."manuf_code" IS '制造商代码';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer"."manuf_type" IS '制造商类别';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer"."supplier_code" IS '供应商编码';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer"."manuf_name" IS '制造商名称';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer"."manuf_addr" IS '制造商地址';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer"."postal_code" IS '制造商邮政编码';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer"."update_date" IS '最后维护日期';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer"."update_time" IS '最后维护时间';

-- ----------------------------
-- Table structure for p_d_manufacturer_manage
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_manufacturer_manage" (
    "serial_num" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "desc" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."p_d_manufacturer_manage"."serial_num" IS '序号';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer_manage"."desc" IS '描述';
COMMENT ON COLUMN "zjdata"."p_d_manufacturer_manage"."statu" IS '状态';

-- ----------------------------
-- Table structure for p_d_material
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_material" (
    "id" int8 NOT NULL,
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "material_desc" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "material_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "material_type_desc" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "material_group" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "material_group_desc" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "effective_date" date,
    "material_statu" varchar(20) COLLATE "pg_catalog"."default",
    "material_statu_desc" varchar(50) COLLATE "pg_catalog"."default",
    "update_date" date NOT NULL,
    "update_time" time(0) NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."p_d_material"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."p_d_material"."material_desc" IS '物料描述';
COMMENT ON COLUMN "zjdata"."p_d_material"."material_type" IS '物料类型';
COMMENT ON COLUMN "zjdata"."p_d_material"."material_type_desc" IS '物料类型描述';
COMMENT ON COLUMN "zjdata"."p_d_material"."material_group" IS '物料组';
COMMENT ON COLUMN "zjdata"."p_d_material"."material_group_desc" IS '物料组描述';
COMMENT ON COLUMN "zjdata"."p_d_material"."unit" IS '基本计量单位';
COMMENT ON COLUMN "zjdata"."p_d_material"."effective_date" IS '有效日期';
COMMENT ON COLUMN "zjdata"."p_d_material"."material_statu" IS '物料状态';
COMMENT ON COLUMN "zjdata"."p_d_material"."material_statu_desc" IS '物料状态描述';
COMMENT ON COLUMN "zjdata"."p_d_material"."update_date" IS '最后维护日期';
COMMENT ON COLUMN "zjdata"."p_d_material"."update_time" IS '最后维护时间';

-- ----------------------------
-- Table structure for p_d_material_property
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_material_property" (
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "attribute_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
    "serial_num" varchar(3) COLLATE "pg_catalog"."default" NOT NULL,
    "value" varchar(200) COLLATE "pg_catalog"."default",
    "id" int8 NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."p_d_material_property"."material_code" IS '内部物料编码';
COMMENT ON COLUMN "zjdata"."p_d_material_property"."attribute_name" IS '属性名';
COMMENT ON COLUMN "zjdata"."p_d_material_property"."serial_num" IS '值序号';
COMMENT ON COLUMN "zjdata"."p_d_material_property"."value" IS '值';

-- ----------------------------
-- Table structure for p_d_order_child
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_order_child" (
    "order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "order_row_num" int8,
    "subproject_num" int8 NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default",
    "factory" varchar(60) COLLATE "pg_catalog"."default",
    "stock_p_code" varchar(60) COLLATE "pg_catalog"."default",
    "material_code" varchar(40) COLLATE "pg_catalog"."default",
    "order_quantity_ship" varchar(13) COLLATE "pg_catalog"."default" NOT NULL,
    "basic_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_number" varchar(30) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."p_d_order_child"."order_num" IS '采购合同/订单号';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."order_row_num" IS '采购合同/订单行号';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."subproject_num" IS '子件项目号';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."factory" IS '工厂';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."stock_p_code" IS '发料库存地点';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."material_code" IS '发料物料编码';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."order_quantity_ship" IS '发料数量';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."p_d_order_child"."batch_number" IS '发料批次号';

-- ----------------------------
-- Table structure for p_d_order_head
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_order_head" (
    "order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "invoices" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "statu" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "order_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "order_type_desc" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
    "update_date" date NOT NULL,
    "update_time" time(0) NOT NULL,
    "create_by" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "purchase_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "company_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "comment" varchar(255) COLLATE "pg_catalog"."default"
    )
;
COMMENT ON COLUMN "zjdata"."p_d_order_head"."order_num" IS '采购合同/订单号';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."invoices" IS '单据类别';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."statu" IS '单据状态';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."order_type" IS '订单类型';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."order_type_desc" IS '订单类型描述';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."update_date" IS '最后维护日期';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."update_time" IS '最后维护时间';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."create_by" IS '创建人';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."supplier_code" IS '供应商编码';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."purchase_code" IS '采购组织';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."company_code" IS '公司代码';
COMMENT ON COLUMN "zjdata"."p_d_order_head"."comment" IS '抬头备注';

-- ----------------------------
-- Table structure for p_d_order_row
-- ----------------------------
CREATE TABLE IF NOT EXISTS "zjdata"."p_d_order_row" (
                 "order_num" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                 "order_row_num" int8 NOT NULL,
                 "del_flag" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
                 "factory" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
                 "stock_p_code" varchar(60) COLLATE "pg_catalog"."default",
                 "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
                 "order_quantity_receive" int8 NOT NULL,
                 "order_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                 "basic_unit" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                 "basic_unit_num" numeric(5) NOT NULL,
                 "order_unit_num" numeric(5) NOT NULL,
                 "delivery_control" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
                 "excessive_tolerance" numeric(3),
                 "deficiency_tolerance" numeric(3),
                 "project_type" varchar(1) COLLATE "pg_catalog"."default",
                 "project_type_desc" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                 "delivery_date" date NOT NULL,
                 "batch_number" varchar(255) COLLATE "pg_catalog"."default",
                 "return_flag" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                 "comment" varchar(255) COLLATE "pg_catalog"."default",
                 "bom_versions" varchar(255),
                 "status" varchar(255)
)
;
COMMENT ON COLUMN "zjdata"."p_d_order_row"."order_num" IS '采购合同/订单号';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."order_row_num" IS '采购合同/订单行号';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."del_flag" IS '删除标识';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."factory" IS '工厂';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."stock_p_code" IS '收货库存地点';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."material_code" IS '收货物料编码';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."order_quantity_receive" IS '订单数量';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."order_unit" IS '订单单位';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."basic_unit" IS '基本单位';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."basic_unit_num" IS '基本单位数量';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."order_unit_num" IS '订单单位数量';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."delivery_control" IS '交货限制';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."excessive_tolerance" IS '过量交货容差';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."deficiency_tolerance" IS '交货不足容差';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."project_type" IS '项目类别';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."project_type_desc" IS '项目类别描述';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."delivery_date" IS '需求交货日期';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."batch_number" IS '收货批次号';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."return_flag" IS '退货标识';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."comment" IS '行项目备注';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."bom_versions" IS 'BOM版本';
COMMENT ON COLUMN "zjdata"."p_d_order_row"."status" IS 'BOM状态';

-- ----------------------------
-- Table structure for p_d_processing
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_processing" (
    "process" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "process_desc" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
    )
;
COMMENT ON COLUMN "zjdata"."p_d_processing"."process" IS '制程';
COMMENT ON COLUMN "zjdata"."p_d_processing"."process_desc" IS '制程描述';
COMMENT ON TABLE "zjdata"."p_d_processing" IS '委外制程管理存储表';

-- ----------------------------
-- Table structure for p_d_supplier
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_supplier" (
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_abbreviation" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_addr" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
    "postal_code" varchar(20) COLLATE "pg_catalog"."default",
    "one_classify" varchar(10) COLLATE "pg_catalog"."default",
    "one_classify_desc" varchar(50) COLLATE "pg_catalog"."default",
    "two_classify" varchar(10) COLLATE "pg_catalog"."default",
    "two_classify_desc" varchar(50) COLLATE "pg_catalog"."default",
    "three_classify" varchar(10) COLLATE "pg_catalog"."default",
    "three_classify_desc" varchar(50) COLLATE "pg_catalog"."default",
    "update_date" date,
    "update_time" time(0)
    )
;
COMMENT ON COLUMN "zjdata"."p_d_supplier"."supplier_code" IS '供应商编码';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."supplier_name" IS '供应商名称';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."supplier_abbreviation" IS '供应商简称';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."supplier_addr" IS '供应商地址';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."postal_code" IS '供应商邮政编码';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."one_classify" IS '供应商一级分类';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."one_classify_desc" IS '供应商一级分类描述';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."two_classify" IS '供应商二级分类';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."two_classify_desc" IS '供应商二级分类描述';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."three_classify" IS '供应商三级分类';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."three_classify_desc" IS '供应商三级分类描述';
COMMENT ON COLUMN "zjdata"."p_d_supplier"."update_date" IS '最后维护日期';

-- ----------------------------
-- Table structure for p_d_test_procedure
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_test_procedure" (
    "material_code" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
    "supplier_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "manuf_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "manuf_type" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'empty'::character varying,
    "test_routine" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "test_versions" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "test_statu" varchar(1) COLLATE "pg_catalog"."default",
    "combined_code" varchar(100) COLLATE "pg_catalog"."default",
    "temperature" varchar(100) COLLATE "pg_catalog"."default",
    "test_mode" varchar(100) COLLATE "pg_catalog"."default",
    "explain" varchar(100) COLLATE "pg_catalog"."default",
    "id" int8
    )
;
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."material_code" IS '内部物料编码
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."supplier_code" IS '供应商编码
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."manuf_code" IS '制造商代码
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."manuf_type" IS '制造商类别
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."test_routine" IS '测试程序
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."test_versions" IS '测试程序版本
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."test_statu" IS '测试程序状态
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."combined_code" IS '组合代码
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."temperature" IS '测试环境（温度）
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."test_mode" IS '测试方式
';
COMMENT ON COLUMN "zjdata"."p_d_test_procedure"."explain" IS '程序说明
';

-- ----------------------------
-- Table structure for p_d_werks
-- ----------------------------
CREATE TABLE IF NOT EXISTS  "zjdata"."p_d_werks" (
   "id" int8 NOT NULL,
    "code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "abbreviation" varchar(255) COLLATE "pg_catalog"."default",
    "description" text COLLATE "pg_catalog"."default",
    "factory_id" varchar(255) COLLATE "pg_catalog"."default",
    "company_id" varchar(255) COLLATE "pg_catalog"."default",
    "bwkey_id" int8,
    "kokrs_id" int8,
    "enable" bool NOT NULL,
    "del_flag" bool NOT NULL,
    "create_by" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "create_time" date NOT NULL,
    "update_by" varchar(255) COLLATE "pg_catalog"."default",
    "update_time" time(0)
    )
;


-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."act_evt_log_log_nr__seq"
    OWNED BY "zjdata"."act_evt_log"."log_nr_";
SELECT setval('"zjdata"."act_evt_log_log_nr__seq"', 4, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."d_c_field_relation_id_seq"
    OWNED BY "zjdata"."d_c_field_relation"."id";
SELECT setval('"zjdata"."d_c_field_relation_id_seq"', 2, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."d_c_field_relation_type_id_seq"
    OWNED BY "zjdata"."d_c_field_relation_type"."id";
SELECT setval('"zjdata"."d_c_field_relation_type_id_seq"', 2, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"zjdata"."f_d_flow_head_id_seq"', 1, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."f_d_post_certificate_head_voucher_num_seq"
    OWNED BY "zjdata"."f_d_post_certificate_head"."voucher_num";
SELECT setval('"zjdata"."f_d_post_certificate_head_voucher_num_seq"', 7, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."f_g_c_cache_data_id_seq"
    OWNED BY "zjdata"."f_g_c_cache_data"."id";
SELECT setval('"zjdata"."f_g_c_cache_data_id_seq"', 3, false);


-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."org_company_id_seq"
    OWNED BY "zjdata"."org_company"."id";
SELECT setval('"zjdata"."org_company_id_seq"', 3, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."org_factory_id_seq"
    OWNED BY "zjdata"."org_factory"."id";
SELECT setval('"zjdata"."org_factory_id_seq"', 3, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."org_purchase_group_id_seq"
    OWNED BY "zjdata"."org_purchase_group"."id";
SELECT setval('"zjdata"."org_purchase_group_id_seq"', 3, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."org_purchase_id_seq"
    OWNED BY "zjdata"."org_purchase"."id";
SELECT setval('"zjdata"."org_purchase_id_seq"', 3, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "zjdata"."org_stock_place_id_seq"
    OWNED BY "zjdata"."org_stock_place"."id";
SELECT setval('"zjdata"."org_stock_place_id_seq"', 3, false);








-- ----------------------------
-- Primary Key structure for table act_evt_log
-- ----------------------------
ALTER TABLE "zjdata"."act_evt_log" ADD CONSTRAINT "act_evt_log_pkey" PRIMARY KEY ("log_nr_");

-- ----------------------------
-- Indexes structure for table act_ge_bytearray
-- ----------------------------
CREATE INDEX "act_idx_bytear_depl" ON "zjdata"."act_ge_bytearray" USING btree (
    "deployment_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ge_bytearray
-- ----------------------------
ALTER TABLE "zjdata"."act_ge_bytearray" ADD CONSTRAINT "act_ge_bytearray_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_ge_property
-- ----------------------------
ALTER TABLE "zjdata"."act_ge_property" ADD CONSTRAINT "act_ge_property_pkey" PRIMARY KEY ("name_");

-- ----------------------------
-- Indexes structure for table act_hi_actinst
-- ----------------------------
CREATE INDEX "act_idx_hi_act_inst_end" ON "zjdata"."act_hi_actinst" USING btree (
    "end_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_act_inst_exec" ON "zjdata"."act_hi_actinst" USING btree (
    "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
    "act_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_act_inst_procinst" ON "zjdata"."act_hi_actinst" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
    "act_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_act_inst_start" ON "zjdata"."act_hi_actinst" USING btree (
    "start_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_hi_actinst
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_actinst" ADD CONSTRAINT "act_hi_actinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_hi_attachment
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_attachment" ADD CONSTRAINT "act_hi_attachment_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_hi_comment
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_comment" ADD CONSTRAINT "act_hi_comment_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_detail
-- ----------------------------
CREATE INDEX "act_idx_hi_detail_act_inst" ON "zjdata"."act_hi_detail" USING btree (
    "act_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_detail_name" ON "zjdata"."act_hi_detail" USING btree (
    "name_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_detail_proc_inst" ON "zjdata"."act_hi_detail" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_detail_task_id" ON "zjdata"."act_hi_detail" USING btree (
    "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_detail_time" ON "zjdata"."act_hi_detail" USING btree (
    "time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_hi_detail
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_detail" ADD CONSTRAINT "act_hi_detail_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_identitylink
-- ----------------------------
CREATE INDEX "act_idx_hi_ident_lnk_procinst" ON "zjdata"."act_hi_identitylink" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_ident_lnk_task" ON "zjdata"."act_hi_identitylink" USING btree (
    "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_ident_lnk_user" ON "zjdata"."act_hi_identitylink" USING btree (
    "user_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_hi_identitylink
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_identitylink" ADD CONSTRAINT "act_hi_identitylink_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_procinst
-- ----------------------------
CREATE INDEX "act_idx_hi_pro_i_buskey" ON "zjdata"."act_hi_procinst" USING btree (
    "business_key_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_pro_inst_end" ON "zjdata"."act_hi_procinst" USING btree (
    "end_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Uniques structure for table act_hi_procinst
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_procinst" ADD CONSTRAINT "act_hi_procinst_proc_inst_id__key" UNIQUE ("proc_inst_id_");

-- ----------------------------
-- Primary Key structure for table act_hi_procinst
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_procinst" ADD CONSTRAINT "act_hi_procinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_taskinst
-- ----------------------------
CREATE INDEX "act_idx_hi_task_inst_procinst" ON "zjdata"."act_hi_taskinst" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_hi_taskinst
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_taskinst" ADD CONSTRAINT "act_hi_taskinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_varinst
-- ----------------------------
CREATE INDEX "act_idx_hi_procvar_name_type" ON "zjdata"."act_hi_varinst" USING btree (
    "name_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
    "var_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_procvar_proc_inst" ON "zjdata"."act_hi_varinst" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_hi_procvar_task_id" ON "zjdata"."act_hi_varinst" USING btree (
    "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_hi_varinst
-- ----------------------------
ALTER TABLE "zjdata"."act_hi_varinst" ADD CONSTRAINT "act_hi_varinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_procdef_info
-- ----------------------------
CREATE INDEX "act_idx_procdef_info_json" ON "zjdata"."act_procdef_info" USING btree (
    "info_json_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_procdef_info_proc" ON "zjdata"."act_procdef_info" USING btree (
    "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Uniques structure for table act_procdef_info
-- ----------------------------
ALTER TABLE "zjdata"."act_procdef_info" ADD CONSTRAINT "act_uniq_info_procdef" UNIQUE ("proc_def_id_");

-- ----------------------------
-- Primary Key structure for table act_procdef_info
-- ----------------------------
ALTER TABLE "zjdata"."act_procdef_info" ADD CONSTRAINT "act_procdef_info_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_re_deployment
-- ----------------------------
ALTER TABLE "zjdata"."act_re_deployment" ADD CONSTRAINT "act_re_deployment_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_re_model
-- ----------------------------
CREATE INDEX "act_idx_model_deployment" ON "zjdata"."act_re_model" USING btree (
    "deployment_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_model_source" ON "zjdata"."act_re_model" USING btree (
    "editor_source_value_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_model_source_extra" ON "zjdata"."act_re_model" USING btree (
    "editor_source_extra_value_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE UNIQUE INDEX "key_" ON "zjdata"."act_re_model" USING btree (
    "key_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_re_model
-- ----------------------------
ALTER TABLE "zjdata"."act_re_model" ADD CONSTRAINT "act_re_model_pkey" PRIMARY KEY ("id_");


-- ----------------------------
-- Primary Key structure for table act_re_procdef
-- ----------------------------
ALTER TABLE "zjdata"."act_re_procdef" ADD CONSTRAINT "act_re_procdef_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_deadletter_job
-- ----------------------------
CREATE INDEX "act_idx_deadletter_job_exception" ON "zjdata"."act_ru_deadletter_job" USING btree (
    "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_deadletter_job_execution_id" ON "zjdata"."act_ru_deadletter_job" USING btree (
    "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_deadletter_job_proc_def_id" ON "zjdata"."act_ru_deadletter_job" USING btree (
    "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_deadletter_job_process_instance_id" ON "zjdata"."act_ru_deadletter_job" USING btree (
    "process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_deadletter_job
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_deadletter_job" ADD CONSTRAINT "act_ru_deadletter_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_event_subscr
-- ----------------------------
CREATE INDEX "act_idx_event_subscr" ON "zjdata"."act_ru_event_subscr" USING btree (
    "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_event_subscr_config_" ON "zjdata"."act_ru_event_subscr" USING btree (
    "configuration_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_event_subscr
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_event_subscr" ADD CONSTRAINT "act_ru_event_subscr_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_execution
-- ----------------------------
CREATE INDEX "act_idx_exe_parent" ON "zjdata"."act_ru_execution" USING btree (
    "parent_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_exe_procdef" ON "zjdata"."act_ru_execution" USING btree (
    "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_exe_procinst" ON "zjdata"."act_ru_execution" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_exe_root" ON "zjdata"."act_ru_execution" USING btree (
    "root_proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_exe_super" ON "zjdata"."act_ru_execution" USING btree (
    "super_exec_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_exec_buskey" ON "zjdata"."act_ru_execution" USING btree (
    "business_key_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_execution
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_execution" ADD CONSTRAINT "act_ru_execution_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_identitylink
-- ----------------------------
CREATE INDEX "act_idx_athrz_procedef" ON "zjdata"."act_ru_identitylink" USING btree (
    "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_ident_lnk_group" ON "zjdata"."act_ru_identitylink" USING btree (
    "group_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_ident_lnk_user" ON "zjdata"."act_ru_identitylink" USING btree (
    "user_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_idl_procinst" ON "zjdata"."act_ru_identitylink" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_tskass_task" ON "zjdata"."act_ru_identitylink" USING btree (
    "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_identitylink
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_identitylink" ADD CONSTRAINT "act_ru_identitylink_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_job
-- ----------------------------
CREATE INDEX "act_idx_job_exception" ON "zjdata"."act_ru_job" USING btree (
    "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_job_execution_id" ON "zjdata"."act_ru_job" USING btree (
    "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_job_proc_def_id" ON "zjdata"."act_ru_job" USING btree (
    "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_job_process_instance_id" ON "zjdata"."act_ru_job" USING btree (
    "process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_job
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_job" ADD CONSTRAINT "act_ru_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_suspended_job
-- ----------------------------
CREATE INDEX "act_idx_suspended_job_exception" ON "zjdata"."act_ru_suspended_job" USING btree (
    "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_suspended_job_execution_id" ON "zjdata"."act_ru_suspended_job" USING btree (
    "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_suspended_job_proc_def_id" ON "zjdata"."act_ru_suspended_job" USING btree (
    "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_suspended_job_process_instance_id" ON "zjdata"."act_ru_suspended_job" USING btree (
    "process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_suspended_job
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_suspended_job" ADD CONSTRAINT "act_ru_suspended_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_task
-- ----------------------------
CREATE INDEX "act_idx_task_create" ON "zjdata"."act_ru_task" USING btree (
    "create_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_task_exec" ON "zjdata"."act_ru_task" USING btree (
    "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_task_procdef" ON "zjdata"."act_ru_task" USING btree (
    "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_task_procinst" ON "zjdata"."act_ru_task" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_task
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_task" ADD CONSTRAINT "act_ru_task_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_timer_job
-- ----------------------------
CREATE INDEX "act_idx_timer_job_exception" ON "zjdata"."act_ru_timer_job" USING btree (
    "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_timer_job_execution_id" ON "zjdata"."act_ru_timer_job" USING btree (
    "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_timer_job_proc_def_id" ON "zjdata"."act_ru_timer_job" USING btree (
    "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_timer_job_process_instance_id" ON "zjdata"."act_ru_timer_job" USING btree (
    "process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_timer_job
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_timer_job" ADD CONSTRAINT "act_ru_timer_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_variable
-- ----------------------------
CREATE INDEX "act_idx_var_bytearray" ON "zjdata"."act_ru_variable" USING btree (
    "bytearray_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_var_exe" ON "zjdata"."act_ru_variable" USING btree (
    "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_var_procinst" ON "zjdata"."act_ru_variable" USING btree (
    "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "act_idx_variable_task_id" ON "zjdata"."act_ru_variable" USING btree (
    "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table act_ru_variable
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_variable" ADD CONSTRAINT "act_ru_variable_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table biz_approval
-- ----------------------------
ALTER TABLE "zjdata"."biz_approval" ADD CONSTRAINT "biz_approval_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table biz_todo_item
-- ----------------------------
ALTER TABLE "zjdata"."biz_todo_item" ADD CONSTRAINT "biz_todo_item_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table d_c_field_relation
-- ----------------------------
ALTER TABLE "zjdata"."d_c_field_relation" ADD CONSTRAINT "d_c_field_relation_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table d_c_field_relation_type
-- ----------------------------
ALTER TABLE "zjdata"."d_c_field_relation_type" ADD CONSTRAINT "d_c_field_relation_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table f_d_allot_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_allot_head" ADD CONSTRAINT "f_d_allot_head_pkey" PRIMARY KEY ("transfer_order_num");

-- ----------------------------
-- Primary Key structure for table f_d_allot_row
-- ----------------------------
ALTER TABLE "zjdata"."f_d_allot_row" ADD CONSTRAINT "f_d_allot_row_pkey" PRIMARY KEY ("transfer_order_num", "transfer_order_row_num");

-- ----------------------------
-- Primary Key structure for table f_d_batch_dimensionality
-- ----------------------------
ALTER TABLE "zjdata"."f_d_batch_dimensionality" ADD CONSTRAINT "f_d_batch_dimensionality_pkey" PRIMARY KEY ("manage_dimension");

-- ----------------------------
-- Uniques structure for table f_d_batch_property_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_batch_property_config" ADD CONSTRAINT "f_d_batch_property_config_id_key" UNIQUE ("id");

-- ----------------------------
-- Primary Key structure for table f_d_batch_property_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_batch_property_config" ADD CONSTRAINT "f_d_batch_property_config_pkey" PRIMARY KEY ("attribute_name");

-- ----------------------------
-- Primary Key structure for table f_d_batch_serial_number
-- ----------------------------
ALTER TABLE "zjdata"."f_d_batch_serial_number" ADD CONSTRAINT "f_d_batch_serial_number_pkey" PRIMARY KEY ("material_code", "factory_code", "date");

-- ----------------------------
-- Uniques structure for table f_d_bin_property_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_bin_property_config" ADD CONSTRAINT "f_d_bin_property_config_id_key" UNIQUE ("id");

-- ----------------------------
-- Primary Key structure for table f_d_bin_property_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_bin_property_config" ADD CONSTRAINT "f_d_bin_property_config_pkey" PRIMARY KEY ("attribute_name");

-- ----------------------------
-- Primary Key structure for table f_d_execution_schedule_wip
-- ----------------------------
ALTER TABLE "zjdata"."f_d_execution_schedule_wip" ADD CONSTRAINT "f_d_execution_schedule_wip_pkey" PRIMARY KEY ("work_order_num", "work_order_row_num", "stage");

-- ----------------------------
-- Uniques structure for table f_d_flow_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_flow_head" ADD CONSTRAINT "f_d_flow_head_supplier_code_manuf_code_manuf_type_material__key" UNIQUE ("supplier_code", "manuf_code", "manuf_type", "material_code", "process", "flow", "flow_versions");

-- ----------------------------
-- Primary Key structure for table f_d_flow_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_flow_head" ADD CONSTRAINT "f_d_flow_head_pkey" PRIMARY KEY ("supplier_code", "manuf_code", "manuf_type", "material_code", "process", "flow", "flow_versions");

-- ----------------------------
-- Primary Key structure for table f_d_flow_row
-- ----------------------------
ALTER TABLE "zjdata"."f_d_flow_row" ADD CONSTRAINT "f_d_flow_row_pkey" PRIMARY KEY ("head_id", "serial_num");

-- ----------------------------
-- Primary Key structure for table f_d_get_return_material_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_get_return_material_head" ADD CONSTRAINT "f_d_get_return_material_head_pkey" PRIMARY KEY ("pick_return_order_num");

-- ----------------------------
-- Primary Key structure for table f_d_get_return_material_row
-- ----------------------------
ALTER TABLE "zjdata"."f_d_get_return_material_row" ADD CONSTRAINT "f_d_get_return_material_row_pkey" PRIMARY KEY ("pick_return_order_num", "pick_return_order_row_num");

-- ----------------------------
-- Primary Key structure for table f_d_material_batch_property
-- ----------------------------
ALTER TABLE "zjdata"."f_d_material_batch_property" ADD CONSTRAINT "f_d_material_batch_property_pkey" PRIMARY KEY ("material_code", "factory_code", "batch_number", "attribute_name", "serial_num");

-- ----------------------------
-- Primary Key structure for table f_d_material_bin_property
-- ----------------------------
ALTER TABLE "zjdata"."f_d_material_bin_property" ADD CONSTRAINT "f_d_material_bin_property_pkey" PRIMARY KEY ("material_code", "factory_code", "batch_number", "piece", "bin_num", "attribute_name", "serial_num");

-- ----------------------------
-- Primary Key structure for table f_d_material_piece_property
-- ----------------------------
ALTER TABLE "zjdata"."f_d_material_piece_property" ADD CONSTRAINT "f_d_material_piece_property_pkey" PRIMARY KEY ("material_code", "factory_code", "batch_number", "piece", "attribute_name", "serial_num");

-- ----------------------------
-- Primary Key structure for table f_d_move_type
-- ----------------------------
ALTER TABLE "zjdata"."f_d_move_type" ADD CONSTRAINT "f_d_move_type_pkey" PRIMARY KEY ("move_type");

-- ----------------------------
-- Uniques structure for table f_d_piece_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_piece_config" ADD CONSTRAINT "f_d_piece_config_id_key" UNIQUE ("id");

-- ----------------------------
-- Primary Key structure for table f_d_piece_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_piece_config" ADD CONSTRAINT "f_d_piece_config_pkey" PRIMARY KEY ("attribute_name");

-- ----------------------------
-- Primary Key structure for table f_d_post_certificate_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_post_certificate_head" ADD CONSTRAINT "f_d_post_certificate_head_pkey" PRIMARY KEY ("voucher_num", "voucher_vintage");

-- ----------------------------
-- Primary Key structure for table f_d_post_certificate_row
-- ----------------------------
ALTER TABLE "zjdata"."f_d_post_certificate_row" ADD CONSTRAINT "f_d_post_certificate_row
_pkey" PRIMARY KEY ("voucher_num", "voucher_vintage", "voucher_row_num");

-- ----------------------------
-- Uniques structure for table f_d_provisional_single_out
-- ----------------------------
ALTER TABLE "zjdata"."f_d_provisional_single_out" ADD CONSTRAINT "f_d_provisional_single_out_work_order_num_work_order_row_nu_key" UNIQUE ("work_order_num", "work_order_row_num", "work_order_children_num", "material_code", "material_desc", "factory_code", "stock_p_code", "batch_number", "piece", "bin_num");

-- ----------------------------
-- Primary Key structure for table f_d_provisional_single_out
-- ----------------------------
ALTER TABLE "zjdata"."f_d_provisional_single_out" ADD CONSTRAINT "f_d_provisional_single_out_pkey" PRIMARY KEY ("work_order_num", "work_order_row_num", "work_order_children_num", "material_code", "material_desc", "factory_code", "stock_p_code", "batch_number", "piece", "bin_num");

-- ----------------------------
-- Uniques structure for table f_d_provisional_single_out_cache
-- ----------------------------
ALTER TABLE "zjdata"."f_d_provisional_single_out_cache" ADD CONSTRAINT "f_d_provisional_single_out_ca_work_order_num_work_order_row_key" UNIQUE ("work_order_num", "work_order_row_num", "work_order_children_num", "material_code", "material_desc", "factory_code", "stock_p_code", "batch_number", "piece", "bin_num");

-- ----------------------------
-- Primary Key structure for table f_d_provisional_single_out_cache
-- ----------------------------
ALTER TABLE "zjdata"."f_d_provisional_single_out_cache" ADD CONSTRAINT "f_d_provisional_single_out_cache_pkey" PRIMARY KEY ("work_order_num", "work_order_row_num", "work_order_children_num", "material_code", "material_desc", "factory_code", "stock_p_code", "batch_number", "piece", "bin_num");

-- ----------------------------
-- Uniques structure for table f_d_receipts_state
-- ----------------------------
ALTER TABLE "zjdata"."f_d_receipts_state" ADD CONSTRAINT "f_d_receipts_state_pk" UNIQUE ("id");

-- ----------------------------
-- Primary Key structure for table f_d_receipts_state
-- ----------------------------
ALTER TABLE "zjdata"."f_d_receipts_state" ADD CONSTRAINT "f_d_receipts_state_pk_2" PRIMARY KEY ("paper_type", "statu");

-- ----------------------------
-- Primary Key structure for table f_d_repertory
-- ----------------------------
ALTER TABLE "zjdata"."f_d_repertory" ADD CONSTRAINT "f_d_repertory_pkey" PRIMARY KEY ("factory_code", "stock_p_code", "stock_b_p_code", "material_code", "batch_number", "piece", "bin_num");

-- ----------------------------
-- Primary Key structure for table f_d_scrap_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_scrap_head" ADD CONSTRAINT "f_d_scrap_head_pkey" PRIMARY KEY ("scrap_order_num");

-- ----------------------------
-- Primary Key structure for table f_d_scrap_row
-- ----------------------------
ALTER TABLE "zjdata"."f_d_scrap_row" ADD CONSTRAINT "f_d_scrap_row_pkey" PRIMARY KEY ("scrap_order_num", "scrap_order_row_num");

-- ----------------------------
-- Uniques structure for table f_d_segment_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_segment_config" ADD CONSTRAINT "f_d_work_order_segment_config_copy1_id_key" UNIQUE ("id");

-- ----------------------------
-- Primary Key structure for table f_d_segment_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_segment_config" ADD CONSTRAINT "f_d_work_order_segment_config_copy1_pkey" PRIMARY KEY ("type");

-- ----------------------------
-- Primary Key structure for table f_d_ship_plan_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_ship_plan_head" ADD CONSTRAINT "f_d_ship_plan_head_pkey" PRIMARY KEY ("ship_plan_order_num");

-- ----------------------------
-- Primary Key structure for table f_d_ship_plan_row
-- ----------------------------
ALTER TABLE "zjdata"."f_d_ship_plan_row" ADD CONSTRAINT "f_d_ship_plan_row_pkey" PRIMARY KEY ("ship_plan_order_num", "ship_plan_order_row_num");

-- ----------------------------
-- Primary Key structure for table f_d_ship_request_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_ship_request_head" ADD CONSTRAINT "f_d_ship_request_head_pkey" PRIMARY KEY ("ship_request_order_num");

-- ----------------------------
-- Primary Key structure for table f_d_ship_request_row
-- ----------------------------
ALTER TABLE "zjdata"."f_d_ship_request_row" ADD CONSTRAINT "f_d_ship_request_row_pkey" PRIMARY KEY ("ship_request_order_num", "ship_request_order_row_num");

-- ----------------------------
-- Primary Key structure for table f_d_supplier_linkman
-- ----------------------------
ALTER TABLE "zjdata"."f_d_supplier_linkman" ADD CONSTRAINT "f_d_supplier_linkman_pkey" PRIMARY KEY ("supplier_code", "manuf_code", "manuf_type", "serial_num");

-- ----------------------------
-- Primary Key structure for table f_d_work_order_child
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_child" ADD CONSTRAINT "f_d_work_order_child_pkey" PRIMARY KEY ("work_order_num", "work_order_row_num", "work_order_children_num");

-- ----------------------------
-- Uniques structure for table f_d_work_order_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_config" ADD CONSTRAINT "f_d_work_order_config_pk_2" UNIQUE ("work_order_type");

-- ----------------------------
-- Primary Key structure for table f_d_work_order_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_config" ADD CONSTRAINT "f_d_work_order_config_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table f_d_work_order_field_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_field_config" ADD CONSTRAINT "f_d_work_order_field_config_pk_2" PRIMARY KEY ("work_order_type", "process", "screen_mode", "statu", "field", "location");

-- ----------------------------
-- Primary Key structure for table f_d_work_order_head
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_head" ADD CONSTRAINT "f_d_work_order_head_pk" PRIMARY KEY ("work_order_num");

-- ----------------------------
-- Uniques structure for table f_d_work_order_production_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_production_config" ADD CONSTRAINT "f_d_work_order_production_config_pk" UNIQUE ("id");

-- ----------------------------
-- Primary Key structure for table f_d_work_order_production_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_production_config" ADD CONSTRAINT "f_d_work_order_production_config_pk_2" PRIMARY KEY ("work_order_type", "produce_statu");

-- ----------------------------
-- Primary Key structure for table f_d_work_order_row
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_row" ADD CONSTRAINT "f_d_work_order_row_pkey" PRIMARY KEY ("work_order_num", "work_order_row_num");

-- ----------------------------
-- Uniques structure for table f_d_work_order_segment_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_segment_config" ADD CONSTRAINT "f_d_work_order_segment_config_pk" UNIQUE ("id");

-- ----------------------------
-- Primary Key structure for table f_d_work_order_segment_config
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_segment_config" ADD CONSTRAINT "f_d_work_order_segment_config_pk_2" PRIMARY KEY ("work_order_type", "encode");

-- ----------------------------
-- Uniques structure for table f_d_work_order_store_issue
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_store_issue" ADD CONSTRAINT "f_d_work_order_store_issue_work_order_num_work_order_row_nu_key" UNIQUE ("work_order_num", "work_order_row_num", "work_order_children_num", "material_code", "material_desc", "factory_code", "stock_p_code", "batch_number", "piece", "bin_num");

-- ----------------------------
-- Primary Key structure for table f_d_work_order_store_issue
-- ----------------------------
ALTER TABLE "zjdata"."f_d_work_order_store_issue" ADD CONSTRAINT "f_d_work_order_store_issue_pkey" PRIMARY KEY ("work_order_num", "work_order_row_num", "work_order_children_num", "material_code", "material_desc", "factory_code", "stock_p_code", "batch_number", "piece", "bin_num");

-- ----------------------------
-- Primary Key structure for table f_g_c_cache_data
-- ----------------------------
ALTER TABLE "zjdata"."f_g_c_cache_data" ADD CONSTRAINT "f_g_c_cache_data_pkey" PRIMARY KEY ("id");


-- ----------------------------
-- Indexes structure for table org_bwkey
-- ----------------------------
CREATE INDEX "b_id" ON "zjdata"."org_bwkey" USING btree (
    "id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table org_bwkey
-- ----------------------------
ALTER TABLE "zjdata"."org_bwkey" ADD CONSTRAINT "org_bwkey_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table org_company
-- ----------------------------
ALTER TABLE "zjdata"."org_company" ADD CONSTRAINT "org_company_company_code_key" UNIQUE ("company_code");

-- ----------------------------
-- Primary Key structure for table org_company
-- ----------------------------
ALTER TABLE "zjdata"."org_company" ADD CONSTRAINT "org_company_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table org_factory
-- ----------------------------
ALTER TABLE "zjdata"."org_factory" ADD CONSTRAINT "org_factory_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table org_kokrs
-- ----------------------------
CREATE INDEX "k_id" ON "zjdata"."org_kokrs" USING btree (
    "id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table org_purchase
-- ----------------------------
ALTER TABLE "zjdata"."org_purchase" ADD CONSTRAINT "org_purchase_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table org_purchase_group
-- ----------------------------
ALTER TABLE "zjdata"."org_purchase_group" ADD CONSTRAINT "org_purchase_group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table org_stock_bin_place
-- ----------------------------
CREATE UNIQUE INDEX "org_stock_bin_place_id_uindex" ON "zjdata"."org_stock_bin_place" USING btree (
    "id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table org_stock_place
-- ----------------------------
ALTER TABLE "zjdata"."org_stock_place" ADD CONSTRAINT "org_stock_place_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table p_d_base_material
-- ----------------------------
CREATE UNIQUE INDEX "p_d_base_material_id_uindex" ON "zjdata"."p_d_base_material" USING btree (
    "id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table p_d_base_material
-- ----------------------------
ALTER TABLE "zjdata"."p_d_base_material" ADD CONSTRAINT "p_d_base_material_pk" PRIMARY KEY ("supplier_code", "manuf_code", "manuf_type");

-- ----------------------------
-- Primary Key structure for table p_d_bom_head
-- ----------------------------
ALTER TABLE "zjdata"."p_d_bom_head" ADD CONSTRAINT "p_d_bom_head_pkey" PRIMARY KEY ("head_id");

-- ----------------------------
-- Primary Key structure for table p_d_bom_row
-- ----------------------------
ALTER TABLE "zjdata"."p_d_bom_row" ADD CONSTRAINT "p_d_bom_row_pk" PRIMARY KEY ("head_id", "row_num");

-- ----------------------------
-- Uniques structure for table p_d_capsulation_info
-- ----------------------------
ALTER TABLE "zjdata"."p_d_capsulation_info" ADD CONSTRAINT "p_d_capsulation_info_id_key" UNIQUE ("material_code", "supplier_code", "manuf_code", "manuf_type", "versions");

-- ----------------------------
-- Primary Key structure for table p_d_capsulation_info
-- ----------------------------
ALTER TABLE "zjdata"."p_d_capsulation_info" ADD CONSTRAINT "p_d_capsulation_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table p_d_manufacturer
-- ----------------------------
ALTER TABLE "zjdata"."p_d_manufacturer" ADD CONSTRAINT "p_d_manufacturer_pk" PRIMARY KEY ("manuf_code", "manuf_type");

-- ----------------------------
-- Indexes structure for table p_d_material
-- ----------------------------
CREATE UNIQUE INDEX "m_id" ON "zjdata"."p_d_material" USING btree (
    "id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table p_d_material
-- ----------------------------
ALTER TABLE "zjdata"."p_d_material" ADD CONSTRAINT "p_d_material_pkey" PRIMARY KEY ("material_code");

-- ----------------------------
-- Uniques structure for table p_d_material_property
-- ----------------------------
ALTER TABLE "zjdata"."p_d_material_property" ADD CONSTRAINT "p_d_materal_property_pk_2" UNIQUE ("material_code", "attribute_name", "serial_num");

-- ----------------------------
-- Primary Key structure for table p_d_material_property
-- ----------------------------
ALTER TABLE "zjdata"."p_d_material_property" ADD CONSTRAINT "p_d_materal_property_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table p_d_order_child
-- ----------------------------
ALTER TABLE "zjdata"."p_d_order_child" ADD CONSTRAINT "p_d_order_child_order_num_order_row_num_subproject_num_key" UNIQUE ("order_num", "order_row_num", "subproject_num");

-- ----------------------------
-- Primary Key structure for table p_d_order_head
-- ----------------------------
ALTER TABLE "zjdata"."p_d_order_head" ADD CONSTRAINT "p_d_order_head_pkey" PRIMARY KEY ("order_num");

-- ----------------------------
-- Primary Key structure for table p_d_order_row
-- ----------------------------
ALTER TABLE "zjdata"."p_d_order_row" ADD CONSTRAINT "p_d_order_row_pkey" PRIMARY KEY ("order_num", "order_row_num");

-- ----------------------------
-- Indexes structure for table p_d_processing
-- ----------------------------
CREATE UNIQUE INDEX "p_d_processing_process_uindex" ON "zjdata"."p_d_processing" USING btree (
    "process" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table p_d_processing
-- ----------------------------
ALTER TABLE "zjdata"."p_d_processing" ADD CONSTRAINT "p_d_processing_pkey" PRIMARY KEY ("process");

-- ----------------------------
-- Primary Key structure for table p_d_supplier
-- ----------------------------
ALTER TABLE "zjdata"."p_d_supplier" ADD CONSTRAINT "p_d_supplier_pkey" PRIMARY KEY ("supplier_code");

-- ----------------------------
-- Indexes structure for table p_d_test_procedure
-- ----------------------------
CREATE UNIQUE INDEX "p_d_test_procedure_id_uindex" ON "zjdata"."p_d_test_procedure" USING btree (
    "id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table p_d_test_procedure
-- ----------------------------
ALTER TABLE "zjdata"."p_d_test_procedure" ADD CONSTRAINT "p_d_test_procedure_pk" PRIMARY KEY ("material_code", "supplier_code", "manuf_code", "manuf_type", "test_routine", "test_versions");

-- ----------------------------
-- Indexes structure for table p_d_werks
-- ----------------------------
CREATE INDEX "werks_id" ON "zjdata"."p_d_werks" USING btree (
    "id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );


-- ----------------------------
ALTER TABLE "zjdata"."act_ge_bytearray" ADD CONSTRAINT "act_fk_bytearr_depl" FOREIGN KEY ("deployment_id_") REFERENCES "zjdata"."act_re_deployment" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_procdef_info
-- ----------------------------
ALTER TABLE "zjdata"."act_procdef_info" ADD CONSTRAINT "act_fk_info_json_ba" FOREIGN KEY ("info_json_id_") REFERENCES "zjdata"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_procdef_info" ADD CONSTRAINT "act_fk_info_procdef" FOREIGN KEY ("proc_def_id_") REFERENCES "zjdata"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_re_model
-- ----------------------------
ALTER TABLE "zjdata"."act_re_model" ADD CONSTRAINT "act_fk_model_deployment" FOREIGN KEY ("deployment_id_") REFERENCES "zjdata"."act_re_deployment" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_re_model" ADD CONSTRAINT "act_fk_model_source" FOREIGN KEY ("editor_source_value_id_") REFERENCES "zjdata"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_re_model" ADD CONSTRAINT "act_fk_model_source_extra" FOREIGN KEY ("editor_source_extra_value_id_") REFERENCES "zjdata"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_deadletter_job
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "zjdata"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_execution" FOREIGN KEY ("execution_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_proc_def" FOREIGN KEY ("proc_def_id_") REFERENCES "zjdata"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_process_instance" FOREIGN KEY ("process_instance_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_event_subscr
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_event_subscr" ADD CONSTRAINT "act_fk_event_exec" FOREIGN KEY ("execution_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_execution
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_execution" ADD CONSTRAINT "act_fk_exe_parent" FOREIGN KEY ("parent_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_execution" ADD CONSTRAINT "act_fk_exe_procdef" FOREIGN KEY ("proc_def_id_") REFERENCES "zjdata"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_execution" ADD CONSTRAINT "act_fk_exe_procinst" FOREIGN KEY ("proc_inst_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_execution" ADD CONSTRAINT "act_fk_exe_super" FOREIGN KEY ("super_exec_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_identitylink
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_identitylink" ADD CONSTRAINT "act_fk_athrz_procedef" FOREIGN KEY ("proc_def_id_") REFERENCES "zjdata"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_identitylink" ADD CONSTRAINT "act_fk_idl_procinst" FOREIGN KEY ("proc_inst_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_identitylink" ADD CONSTRAINT "act_fk_tskass_task" FOREIGN KEY ("task_id_") REFERENCES "zjdata"."act_ru_task" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_job
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_job" ADD CONSTRAINT "act_fk_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "zjdata"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_job" ADD CONSTRAINT "act_fk_job_execution" FOREIGN KEY ("execution_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_job" ADD CONSTRAINT "act_fk_job_proc_def" FOREIGN KEY ("proc_def_id_") REFERENCES "zjdata"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_job" ADD CONSTRAINT "act_fk_job_process_instance" FOREIGN KEY ("process_instance_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_suspended_job
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "zjdata"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_execution" FOREIGN KEY ("execution_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_proc_def" FOREIGN KEY ("proc_def_id_") REFERENCES "zjdata"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_process_instance" FOREIGN KEY ("process_instance_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_task
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_task" ADD CONSTRAINT "act_fk_task_exe" FOREIGN KEY ("execution_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_task" ADD CONSTRAINT "act_fk_task_procdef" FOREIGN KEY ("proc_def_id_") REFERENCES "zjdata"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_task" ADD CONSTRAINT "act_fk_task_procinst" FOREIGN KEY ("proc_inst_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_timer_job
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "zjdata"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_execution" FOREIGN KEY ("execution_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_proc_def" FOREIGN KEY ("proc_def_id_") REFERENCES "zjdata"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_process_instance" FOREIGN KEY ("process_instance_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_variable
-- ----------------------------
ALTER TABLE "zjdata"."act_ru_variable" ADD CONSTRAINT "act_fk_var_bytearray" FOREIGN KEY ("bytearray_id_") REFERENCES "zjdata"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_variable" ADD CONSTRAINT "act_fk_var_exe" FOREIGN KEY ("execution_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "zjdata"."act_ru_variable" ADD CONSTRAINT "act_fk_var_procinst" FOREIGN KEY ("proc_inst_id_") REFERENCES "zjdata"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;




create TABLE IF NOT EXISTS   zjdata.p_d_processing_single
(
    turnkey_process varchar(10) not null,
    serial_num      varchar(3)  not null,
    process         varchar(10),
    id              bigint      not null
    constraint p_d_processing_single_pk
    primary key,
    constraint p_d_processing_single_pk2
    unique (serial_num, turnkey_process),
    "create_by" varchar(64) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "update_by" varchar(64) COLLATE "pg_catalog"."default",
    "update_time" timestamp(6)
    );




------------
ALTER TABLE "zjdata"."p_d_processing" ADD COLUMN is_turnkey VARCHAR ( 1 ) DEFAULT '' not null;
alter table "zjdata"."p_d_processing" alter column is_turnkey drop DEFAULT ;
comment on column "zjdata"."p_d_processing".is_turnkey is '是否turnkey';


ALTER TABLE "zjdata"."f_d_work_order_row" ADD COLUMN is_final VARCHAR ( 1 );
comment on column "zjdata"."f_d_work_order_row".is_final is '是否最后产出品';

ALTER TABLE "zjdata"."f_d_work_order_row" ADD COLUMN up_layer_num "numeric" ( 5 );
comment on column "zjdata"."f_d_work_order_row".up_layer_num is '上层行号';


ALTER TABLE "zjdata".p_d_bom_head ADD COLUMN update_date "date" DEFAULT '2023-01-11' not null;
alter table "zjdata"."p_d_bom_head" alter column update_date drop DEFAULT ;
comment on column "zjdata".p_d_bom_head.update_date is '最后维护日期';

ALTER TABLE "zjdata".p_d_bom_head ADD COLUMN update_time "time" DEFAULT '00:00:00' not null;
alter table "zjdata"."p_d_bom_head" alter column update_time drop DEFAULT ;
comment on column "zjdata".p_d_bom_head.update_time is '最后维护时间';

ALTER TABLE "zjdata".p_d_bom_head ADD COLUMN supplier_code VARCHAR ( 10 );
comment on column "zjdata"."p_d_bom_head".supplier_code is '供应商编码';

ALTER TABLE zjdata.p_d_bom_head
DROP COLUMN factory_code;


ALTER TABLE "zjdata"."p_d_material" ADD COLUMN from_system VARCHAR ( 10 ) DEFAULT '' not null;
alter table "zjdata"."p_d_material" alter column from_system drop DEFAULT ;
comment on column "zjdata"."p_d_material".from_system is '来源系统';

ALTER TABLE "zjdata"."p_d_supplier" ADD COLUMN from_system VARCHAR ( 10 ) DEFAULT '' not null;
alter table "zjdata"."p_d_supplier" alter column from_system drop DEFAULT ;
comment on column "zjdata"."p_d_supplier".from_system is '来源系统';

ALTER TABLE "zjdata"."p_d_manufacturer" ADD COLUMN from_system VARCHAR ( 10 ) DEFAULT '' not null;
alter table "zjdata"."p_d_manufacturer" alter column from_system drop DEFAULT ;
comment on column "zjdata"."p_d_manufacturer".from_system is '来源系统';

ALTER TABLE "zjdata"."p_d_order_head" ADD COLUMN from_system VARCHAR ( 10 ) DEFAULT '' not null;
alter table "zjdata"."p_d_order_head" alter column from_system drop DEFAULT ;
comment on column "zjdata"."p_d_order_head".from_system is '来源系统';

ALTER TABLE "zjdata"."f_d_ship_plan_head" ADD COLUMN from_system VARCHAR ( 10 ) DEFAULT '' not null;
alter table "zjdata"."f_d_ship_plan_head" alter column from_system drop DEFAULT ;
comment on column "zjdata"."f_d_ship_plan_head".from_system is '来源系统';



ALTER TABLE "zjdata"."f_d_move_type" ADD COLUMN opposite_move_type VARCHAR ( 3 ) ;
comment on column "zjdata"."f_d_move_type".opposite_move_type is '反向移动类型';


ALTER TABLE "zjdata"."org_stock_place" ADD COLUMN stock_type VARCHAR ( 1 ) DEFAULT '1' not null;
alter table "zjdata"."org_stock_place" alter column stock_type drop DEFAULT ;
comment on column "zjdata"."org_stock_place".stock_type is '库存地点类别';

ALTER TABLE "zjdata"."org_stock_place" ADD COLUMN manuf_code VARCHAR ( 10 ) ;
comment on column "zjdata"."org_stock_place".manuf_code is '制造商代码';

ALTER TABLE "zjdata"."org_stock_place" ADD COLUMN manuf_type VARCHAR ( 1 ) ;
comment on column "zjdata"."org_stock_place".manuf_type is '制造商类别';


ALTER TABLE "zjdata"."org_stock_place" ADD COLUMN supplier_code VARCHAR ( 10 ) ;
comment on column "zjdata"."org_stock_place".supplier_code is '供应商编码';

--------------------------------------------
create TABLE IF NOT EXISTS   zjdata.f_s_price_definition
(
    price_factor      varchar(30) not null
    constraint f_s_price_definition_pk
    primary key,
    price_factor_desc varchar(30) not null,
    price_factor_type varchar(1)  not null,
    "table"           varchar(100)
    );

comment on column zjdata.f_s_price_definition.price_factor is '价格因子';

comment on column zjdata.f_s_price_definition.price_factor_desc is '价格因子描述';

comment on column zjdata.f_s_price_definition.price_factor_type is '价格因子类型';

comment on column zjdata.f_s_price_definition."table" is '存值表';

alter table zjdata.f_s_price_definition
    owner to postgres;

------------------------------------------

create TABLE IF NOT EXISTS   zjdata.f_s_price_element
(
    price_factor         varchar(30) not null,
    changing_factor      varchar(30) not null,
    changing_factor_desc varchar(30) not null,
    constraint f_s_price_element_pk
    primary key (price_factor, changing_factor)
    );

comment on column zjdata.f_s_price_element.price_factor is '价格因子';

comment on column zjdata.f_s_price_element.changing_factor is '变化因素';

comment on column zjdata.f_s_price_element.changing_factor_desc is '变化因素描述';

alter table zjdata.f_s_price_element
    owner to postgres;



-------------------------------------------

create TABLE IF NOT EXISTS   zjdata.f_s_formula_definition
(
    process      varchar(10) not null,
    formula      varchar(10) not null,
    formula_desc varchar     not null,
    constraint f_s_formula_definition_pk
    primary key (process, formula)
    );

comment on column zjdata.f_s_formula_definition.process is '制程';

comment on column zjdata.f_s_formula_definition.formula is '公式名';

comment on column zjdata.f_s_formula_definition.formula_desc is '公式内容';

alter table zjdata.f_s_formula_definition
    owner to postgres;




------------------------------------------------

create TABLE IF NOT EXISTS   zjdata.f_s_formula_allocation
(
    process       varchar(10) not null,
    formula       varchar(10) not null,
    supplier_code varchar(10) not null,
    constraint f_s_formula_allocation_pk
    primary key (process, formula, supplier_code)
    );

comment on column zjdata.f_s_formula_allocation.process is '制程';

comment on column zjdata.f_s_formula_allocation.formula is '公式名';

comment on column zjdata.f_s_formula_allocation.supplier_code is '供应商编码';

alter table zjdata.f_s_formula_allocation
    owner to postgres;
---------------------------------------------
create TABLE IF NOT EXISTS   zjdata.f_d_allot_config
(
    transfer_type      varchar(10)  not null,
    transfer_type_desc varchar(30)  not null,
    field              varchar(30)  not null,
    field_desc         varchar(100) not null,
    transfer_control   varchar(1)   not null,
    primary key (transfer_type, field)
    );

comment on column zjdata.f_d_allot_config.transfer_type is '调拨类别';

comment on column zjdata.f_d_allot_config.transfer_type_desc is '类别描述';

comment on column zjdata.f_d_allot_config.field is '字段名';

comment on column zjdata.f_d_allot_config.field_desc is '字段描述';

comment on column zjdata.f_d_allot_config.transfer_control is '字段控制';

alter table zjdata.f_d_allot_config
    owner to postgres;


-------------------------------------------------------
--------------------2023-04-02 发布-------------------------
--------------------------------------------------------
ALTER TABLE "zjdata"."f_d_post_certificate_row" ADD COLUMN turnkey_not_final VARCHAR ( 1 ) ;
comment on column "zjdata"."f_d_post_certificate_row".turnkey_not_final is 'turnkey非最后产出';

ALTER TABLE "zjdata"."f_d_work_order_row" ADD COLUMN is_close VARCHAR ( 1 ) DEFAULT '0' not null;
alter table "zjdata"."f_d_work_order_row" alter column is_close drop DEFAULT ;
comment on column "zjdata"."f_d_work_order_row".is_close is '关闭标识';

ALTER TABLE "zjdata".p_d_bom_row ADD COLUMN be_substituted_row_num VARCHAR ( 255 ) ;
comment on column "zjdata".p_d_bom_row.be_substituted_row_num is '被替代料件行号';

ALTER TABLE "zjdata".p_d_bom_row ADD COLUMN is_substitute VARCHAR ( 1 ) DEFAULT '0' not null;
alter table "zjdata"."p_d_bom_row" alter column is_substitute drop DEFAULT ;
comment on column "zjdata".p_d_bom_row.is_substitute is '是否替代料件';

ALTER TABLE "zjdata".f_d_repertory ADD COLUMN transfer_stock numeric ( 13,3 ) DEFAULT '0.0' not null;
alter table "zjdata"."f_d_repertory" alter column transfer_stock drop DEFAULT ;
comment on column "zjdata".f_d_repertory.transfer_stock is '调拨锁定库存数量';

ALTER TABLE "zjdata".f_d_repertory ADD COLUMN pick_stock numeric ( 13,3 ) DEFAULT '0.0' not null;
alter table "zjdata"."f_d_repertory" alter column pick_stock drop DEFAULT ;
comment on column "zjdata".f_d_repertory.pick_stock is '领料锁定库存数量';

ALTER TABLE "zjdata".f_d_repertory ADD COLUMN scrap_stock numeric ( 13,3 ) DEFAULT '0.0' not null;
alter table "zjdata"."f_d_repertory" alter column scrap_stock drop DEFAULT ;
comment on column "zjdata".f_d_repertory.scrap_stock is '报废锁定库存数量';

ALTER TABLE "zjdata".f_d_repertory ADD COLUMN ship_stock numeric ( 13,3 ) DEFAULT '0.0' not null;
alter table "zjdata"."f_d_repertory" alter column ship_stock drop DEFAULT ;
comment on column "zjdata".f_d_repertory.ship_stock is '出货锁定库存数量';

INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'stock_p_code_receive', '收回库存地点', '2');
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'batch_number_receive', '收回批次号', '1');
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'piece_receive', '收回片号', '1');
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'bin_num_receive', '收回BIN号', '1');
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'stock_statu_receive', '收货库存状态', '1');


ALTER TABLE zjdata.f_d_allot_head ADD COLUMN transfer_type VARCHAR ( 10 ) ;
comment on column zjdata.f_d_allot_head.transfer_type is '调拨类别';


create TABLE IF NOT EXISTS   zjdata.p_d_bin_main_data
(
    bin_num       varchar(30)
    constraint p_d_bin_main_data_pk
    primary key,
    bin_attribute varchar(1)
    );

comment on column zjdata.p_d_bin_main_data.bin_num is 'BIN别';

comment on column zjdata.p_d_bin_main_data.bin_attribute is 'BIN属性';

alter table zjdata.f_d_provisional_single_out ALTER  COLUMN work_order_num set default 'empty';
alter table zjdata.f_d_provisional_single_out ALTER  COLUMN work_order_row_num set default '0';
alter table zjdata.f_d_provisional_single_out ALTER  COLUMN work_order_children_num set default '0';

----在挑片表添加调拨单号行号，出货需求单号行号，退领单号行号，报废单号行号
ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN transfer_order_num VARCHAR ( 20 ) DEFAULT 'empty' not null;
comment on column zjdata.f_d_provisional_single_out.transfer_order_num is '调拨单号';

ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN transfer_order_row_num numeric ( 5 ) DEFAULT '0' not null;
comment on column zjdata.f_d_provisional_single_out.transfer_order_row_num is '调拨单行号';

ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN ship_request_order_num VARCHAR ( 20 ) DEFAULT 'empty' not null;
comment on column zjdata.f_d_provisional_single_out.ship_request_order_num is '出货需求单号';

ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN ship_request_order_row_num VARCHAR ( 20 ) DEFAULT 'empty' not null;
comment on column zjdata.f_d_provisional_single_out.ship_request_order_row_num is '出货需求单行号';

ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN pick_return_order_num VARCHAR ( 20 ) DEFAULT 'empty' not null;
comment on column zjdata.f_d_provisional_single_out.pick_return_order_num is '退领单号';

ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN pick_return_order_row_num numeric ( 5 ) DEFAULT '0' not null;
comment on column zjdata.f_d_provisional_single_out.pick_return_order_row_num is '退领单行号';

ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN scrap_order_num VARCHAR ( 20 ) DEFAULT 'empty' not null;
comment on column zjdata.f_d_provisional_single_out.scrap_order_num is '报废单号';

ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN scrap_order_row_num numeric ( 5 ) DEFAULT '0' not null;
comment on column zjdata.f_d_provisional_single_out.scrap_order_row_num is '报废单行号' ;


ALTER TABLE zjdata.f_d_provisional_single_out DROP CONSTRAINT "f_d_provisional_single_out_pkey";
ALTER TABLE zjdata.f_d_provisional_single_out add primary key (work_order_num,
                                                               work_order_row_num,
                                                               work_order_children_num,
                                                               material_code,
                                                               material_desc,
                                                               factory_code,
                                                               stock_p_code,
                                                               batch_number,
                                                               piece,
                                                               bin_num,
                                                               transfer_order_num,
                                                               transfer_order_row_num,
                                                               ship_request_order_num,
                                                               ship_request_order_row_num,
                                                               pick_return_order_num,
                                                               pick_return_order_row_num,
                                                               scrap_order_num,
                                                               scrap_order_row_num
    );


ALTER TABLE zjdata.f_d_post_certificate_row ALTER COLUMN write_off SET DEFAULT '0'  ;
UPDATE zjdata.f_d_post_certificate_row SET write_off='0' WHERE write_off is null ;
ALTER TABLE zjdata.f_d_post_certificate_row ALTER COLUMN write_off SET not null   ;


UPDATE zjdata.f_d_allot_row SET piece_receive='empty' WHERE piece_receive is null ;
UPDATE zjdata.f_d_allot_row SET piece_ship='empty' WHERE piece_ship is null ;
UPDATE zjdata.f_d_allot_row SET bin_num_receive='empty' WHERE bin_num_receive is null ;
UPDATE zjdata.f_d_allot_row SET bin_num_ship='empty' WHERE bin_num_ship is null ;

ALTER TABLE zjdata.f_d_allot_row ALTER COLUMN piece_receive SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_allot_row ALTER COLUMN piece_receive SET not null   ;

ALTER TABLE zjdata.f_d_allot_row ALTER COLUMN piece_ship SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_allot_row ALTER COLUMN piece_ship SET not null   ;

ALTER TABLE zjdata.f_d_allot_row ALTER COLUMN bin_num_receive SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_allot_row ALTER COLUMN bin_num_receive SET not null   ;

ALTER TABLE zjdata.f_d_allot_row ALTER COLUMN bin_num_ship SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_allot_row ALTER COLUMN bin_num_ship SET not null   ;


UPDATE zjdata.f_d_scrap_row SET piece='empty' WHERE piece is null ;

ALTER TABLE zjdata.f_d_scrap_row ALTER COLUMN piece SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_scrap_row ALTER COLUMN piece SET not null   ;


UPDATE zjdata.f_d_scrap_row SET bin_num='empty' WHERE bin_num is null ;

ALTER TABLE zjdata.f_d_scrap_row ALTER COLUMN bin_num SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_scrap_row ALTER COLUMN bin_num SET not null   ;

UPDATE zjdata.f_d_ship_request_row SET piece='empty' WHERE piece is null ;
UPDATE zjdata.f_d_ship_request_row SET  bin_num='empty' WHERE bin_num is null ;
UPDATE zjdata.f_d_get_return_material_row SET piece='empty' WHERE piece is null ;
UPDATE zjdata.f_d_get_return_material_row SET bin_num='empty' WHERE bin_num is null ;

ALTER TABLE zjdata.f_d_ship_request_row ALTER COLUMN piece SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_ship_request_row ALTER COLUMN piece SET not null   ;

ALTER TABLE zjdata.f_d_ship_request_row ALTER COLUMN bin_num SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_ship_request_row ALTER COLUMN bin_num SET not null   ;

ALTER TABLE zjdata.f_d_get_return_material_row ALTER COLUMN piece SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_get_return_material_row ALTER COLUMN piece SET not null   ;

ALTER TABLE zjdata.f_d_get_return_material_row ALTER COLUMN bin_num SET DEFAULT 'empty'  ;
ALTER TABLE zjdata.f_d_get_return_material_row ALTER COLUMN bin_num SET not null   ;




alter table zjdata.f_d_provisional_single_out
drop constraint f_d_provisional_single_out_pkey;

alter table zjdata.f_d_provisional_single_out
    add primary key (work_order_num, work_order_row_num, work_order_children_num, material_code, factory_code,
                     stock_p_code, batch_number, piece, bin_num, transfer_order_num, transfer_order_row_num,
                     ship_request_order_num, ship_request_order_row_num, pick_return_order_num,
                     pick_return_order_row_num, scrap_order_num, scrap_order_row_num);

alter table zjdata.f_d_provisional_single_out
    alter column material_desc drop not null;



alter table zjdata.p_d_base_material
alter column manuf_type type varchar(5) using manuf_type::varchar(5);


update zjdata.f_s_price_definition set "table"='empty' where "table" is null ;

alter table zjdata.f_s_price_definition
    alter column "table" set default 'empty';

alter table zjdata.f_s_price_definition
drop constraint f_s_price_definition_pk;

alter table zjdata.f_s_price_definition
    add constraint f_s_price_definition_pk
        primary key (price_factor, "table");




ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN user_name VARCHAR ( 255 ) DEFAULT 'empty' not null;
comment on column zjdata.f_d_provisional_single_out.user_name is '用户名';


ALTER TABLE zjdata.f_d_provisional_single_out ADD COLUMN voucher_row_num numeric ( 20 ) DEFAULT '0' not null;
comment on column zjdata.f_d_provisional_single_out.voucher_row_num is '盘盈亏行号';

alter table zjdata.f_d_provisional_single_out
drop constraint f_d_provisional_single_out_pkey;

alter table zjdata.f_d_provisional_single_out
    add primary key (work_order_num, work_order_row_num, work_order_children_num, material_code, factory_code,
                     stock_p_code, batch_number, piece, bin_num, transfer_order_num, transfer_order_row_num,
                     ship_request_order_num, ship_request_order_row_num, pick_return_order_num,
                     pick_return_order_row_num, scrap_order_num, scrap_order_row_num, user_name);



alter table zjdata.f_d_work_order_store_issue
    alter column piece set default 'empty';

alter table zjdata.f_d_work_order_store_issue
    alter column bin_num set default 'empty';


alter table zjdata.f_d_flow_head
alter column manuf_type type varchar(5) using manuf_type::varchar(5);

alter table zjdata.p_d_capsulation_info
alter column manuf_type type varchar(5) using manuf_type::varchar(5);

alter table zjdata.p_d_test_procedure
alter column manuf_type type varchar(5) using manuf_type::varchar(5);


alter table zjdata.f_d_provisional_single_out
drop constraint f_d_provisional_single_out_work_order_num_work_order_row_nu_key;


-------------------------------------------------------
--------------------2024-03-05 发布-------------------------
--------------------------------------------------------

ALTER TABLE "zjdata".f_d_provisional_single_out ADD COLUMN be_substituted_row_num VARCHAR ( 255 ) ;
comment on column "zjdata".f_d_provisional_single_out.be_substituted_row_num is '被替代料件行号';

ALTER TABLE "zjdata".f_d_provisional_single_out ADD COLUMN is_substitute VARCHAR ( 1 ) DEFAULT '0' not null;
alter table "zjdata"."f_d_provisional_single_out" alter column is_substitute drop DEFAULT ;
comment on column "zjdata".f_d_provisional_single_out.is_substitute is '是否替代料件';

ALTER TABLE "zjdata".f_d_provisional_single_out_cache ADD COLUMN be_substituted_row_num VARCHAR ( 255 ) ;
comment on column "zjdata".f_d_provisional_single_out_cache.be_substituted_row_num is '被替代料件行号';

ALTER TABLE "zjdata".f_d_provisional_single_out_cache ADD COLUMN is_substitute VARCHAR ( 1 ) DEFAULT '0' not null;
alter table "zjdata"."f_d_provisional_single_out_cache" alter column is_substitute drop DEFAULT ;
comment on column "zjdata".f_d_provisional_single_out_cache.is_substitute is '是否替代料件';







-- ----------------------------
-- Records of f_d_batch_property_config
-- ----------------------------



delete from zjdata.f_d_bin_property_config;
INSERT INTO "zjdata"."f_d_bin_property_config" VALUES ('ZRKRQ', '入库日期', '4', 10, 0, 1625665158675038209);
INSERT INTO "zjdata"."f_d_bin_property_config" VALUES ('ZSCRQ', '生产日期', '4', 10, 0, 1625665057680392193);
INSERT INTO "zjdata"."f_d_piece_config" VALUES ('ZGYS', '供应商', '1', 20, 0, 1625665057680392194);



-- ----------------------------
-- Records of f_d_work_order_segment_config
-- ----------------------------
delete from zjdata.f_d_work_order_segment_config;
INSERT INTO "zjdata"."f_d_work_order_segment_config" VALUES ('ZWO1', '2', '1000000000', '1999999999', 1632982230731853825, '1000000000', 1);
INSERT INTO "zjdata"."f_d_work_order_segment_config" VALUES ('ZWO2', '2', '2000000000', '2999999999', 1630079498471608322, '2000000000', 1);



-- ----------------------------
-- Records of f_d_work_order_config
-- ----------------------------
delete from zjdata.f_d_work_order_config;
INSERT INTO "zjdata"."f_d_work_order_config" VALUES ('ZWO1', '标准委外订单', 1630769077746929665);
INSERT INTO "zjdata"."f_d_work_order_config" VALUES ('ZWO2', 'turnkey委外订单', 1630773419044507649);







-- ----------------------------
-- Records of f_d_allot_config
-- ----------------------------
delete from zjdata.f_d_allot_config;
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'stock_p_code_receive', '收回库存地点', '2');
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'batch_number_receive', '收回批次号', '1');
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'piece_receive', '收回片号', '1');
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'bin_num_receive', '收回BIN号', '1');
INSERT INTO zjdata.f_d_allot_config (transfer_type, transfer_type_desc, field, field_desc, transfer_control) VALUES ('1', '库存地点转移', 'stock_statu_receive', '收货库存状态', '2');



-- ----------------------------
-- Records of f_d_move_type
-- ----------------------------
delete  from  "zjdata"."f_d_move_type";
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('101', '采购收货', '102');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('102', '采购收货-冲销', null);
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('161', '退货单退货到供应商', '162');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('162', '退货单退货到供应商-冲销', null);
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('201', '部门领料', '202');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('202', '部门退料', '201');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('311', '调拨处理', '311');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('543', '工单组件消耗', '544');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('544', '工单组件消耗-冲销', null);
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('551', '报废', '552');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('552', '报废-冲销', null);
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('601', '销售发货', '602');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('602', '销售发货-冲销/销售退货', '601');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('701', '盘盈处理', '702');
INSERT INTO zjdata.f_d_move_type (move_type, move_type_desc, opposite_move_type) VALUES ('702', '盘亏处理', '701');



-- ----------------------------
-- Records of f_d_batch_dimensionality
-- ----------------------------
delete  from  "zjdata"."f_d_batch_dimensionality";
INSERT INTO "zjdata"."f_d_batch_dimensionality" VALUES ('1');


-- ----------------------------
-- Records of f_d_segment_config
-- ----------------------------
delete from zjdata.f_d_segment_config;
INSERT INTO "zjdata"."f_d_segment_config" VALUES ('TF', '0', '99999999', 1, '1', 1);
INSERT INTO "zjdata"."f_d_segment_config" VALUES ('SP', '0', '99999999', 4, '1', 1);
INSERT INTO "zjdata"."f_d_segment_config" VALUES ('PR', '0', '99999999', 3, '1', 1);
INSERT INTO "zjdata"."f_d_segment_config" VALUES ('SR', '0', '99999999', 2, '1', 1);



-- Records of f_d_work_order_field_config
-- ----------------------------
delete from zjdata.f_d_work_order_field_config;
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'work_order_type', '11', '工单类型', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'paper_date', '11', '凭证日期', '4', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'purchase_code', '11', '采购组织', '4', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'factory_code', '11', '工厂代码', '4', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'username', '11', '单据创建人', '2', 'A3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'statu', '11', '单据状态', '2', 'B3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'del_flag', '11', '删除标识', '2', 'C3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'supplier_code', '12', '供应商编码', '4', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'work_order_num', '11', '工单号', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'manuf', '12', '制造商', '4', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'purchase_linkman', '12', '采购方联系人', '1', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'purchase_phone', '12', '采购方联系电话', '1', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'purchase_email', '12', '采购方联系邮箱', '1', 'C2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'process', '13', '委外制程', '4', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'produce_statu', '13', '生产状态', '4', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'rework', '13', '是否返工', '4', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'head_note', '14', '抬头备注', '1', 'ALL');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'button_choose_capsulation', '2', '封装信息选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'button_choose_test_routine', '2', '测试程序选择按钮', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'work_order_row_num', '2', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'del_flag', '2', '删除标识', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'is_final', '2', '是否最后产出品', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'up_layer_num', '2', '上层行号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'material_code', '2', '收货物料编码', '4', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'material_desc', '2', '收货物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'stock_p_code', '2', '收货库存地点', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'quantity_receive', '2', '预计收货数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'basic_unit', '2', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'package_way', '2', '包装方式', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'mark', '2', '芯片丝印', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'batch_number', '2', '收货批次号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'delivery_limit', '2', '交货限制', '4', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'excessive_delivery', '2', '过量交货容差', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'insufficient_delivery', '2', '交货不足容差', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'delivery_date', '2', '需求交货日期', '4', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'order_num', '2', '采购合同/订单号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'order_row_num', '2', '采购合同/订单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'quantity_convert', '2', '转工单数量（基本单位）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'encapsulation_versions', '2', '封裝信息版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'wire_rod', '2', '线材', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'bd_graph', '2', '打线图（BD图）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'bd_graph_versions', '2', '打线图版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'wire_size', '2', '焊丝尺寸（线径）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'number', '2', '印章文件编号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'slices_way', '2', '取片方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'dedicated', '2', '框架是否专用', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'sticker_way', '2', '贴膜方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'encapsulation_mode', '2', '封装形式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'test_routines', '2', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'bom_versions', '2', 'BOM版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'status', '2', 'BOM状态', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'row_note', '2', '行项目备注', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'button_choose_stock', '3', '发料批次/片/BIN挑选按钮', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'work_order_row_num', '3', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'work_order_children_num', '3', '工单子件项目号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'stock_p_code', '3', '发料库存地点', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'material_code', '3', '发料物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'material_desc', '3', '发料物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'quantity_delivery', '3', '发料数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'basic_unit', '3', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'batch_number', '3', '发料批次号', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'father_count', '3', '父件基本数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'children_count', '3', '组件用量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'work_order_row_num', '4', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'item_ num', '4', '测试程序序号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'test_routine', '4', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'test_versions', '4', '测试程序版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'combined_code', '4', '组合代码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'temperature', '4', '测试环境（温度）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'test_mode', '4', '测试方式', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '1', '1', 'explain', '4', '程序说明', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'work_order_num', '11', '工单号', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'work_order_type', '11', '工单类型', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'paper_date', '11', '凭证日期', '2', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'purchase_code', '11', '采购组织', '2', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'factory_code', '11', '工厂代码', '2', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'username', '11', '单据创建人', '2', 'A3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'statu', '11', '单据状态', '2', 'B3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'del_flag', '11', '删除标识', '2', 'C3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'supplier_code', '12', '供应商编码', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'manuf', '12', '制造商', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'purchase_linkman', '12', '采购方联系人', '2', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'purchase_phone', '12', '采购方联系电话', '2', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'purchase_email', '12', '采购方联系邮箱', '2', 'C2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'process', '13', '委外制程', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'produce_statu', '13', '生产状态', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'rework', '13', '是否返工', '2', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'head_note', '14', '抬头备注', '2', 'ALL');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'button_choose_capsulation', '2', '封装信息选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'button_choose_test_routine', '2', '测试程序选择按钮', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'work_order_row_num', '2', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'del_flag', '2', '删除标识', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'is_final', '2', '是否最后产出品', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'up_layer_num', '2', '上层行号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'material_code', '2', '收货物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'material_desc', '2', '收货物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'stock_p_code', '2', '收货库存地点', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'quantity_receive', '2', '预计收货数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'basic_unit', '2', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'package_way', '2', '包装方式', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'mark', '2', '芯片丝印', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'batch_number', '2', '收货批次号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'delivery_limit', '2', '交货限制', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'excessive_delivery', '2', '过量交货容差', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'insufficient_delivery', '2', '交货不足容差', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'delivery_date', '2', '需求交货日期', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'order_num', '2', '采购合同/订单号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'order_row_num', '2', '采购合同/订单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'quantity_convert', '2', '转工单数量（基本单位）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'encapsulation_versions', '2', '封裝信息版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'wire_rod', '2', '线材', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'bd_graph', '2', '打线图（BD图）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'bd_graph_versions', '2', '打线图版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'wire_size', '2', '焊丝尺寸（线径）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'number', '2', '印章文件编号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'slices_way', '2', '取片方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'dedicated', '2', '框架是否专用', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'sticker_way', '2', '贴膜方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'encapsulation_mode', '2', '封装形式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'test_routines', '2', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'bom_versions', '2', 'BOM版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'status', '2', 'BOM状态', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'row_note', '2', '行项目备注', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'button_choose_stock', '3', '发料批次/片/BIN挑选按钮', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'work_order_row_num', '3', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'work_order_children_num', '3', '工单子件项目号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'stock_p_code', '3', '发料库存地点', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'material_code', '3', '发料物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'material_desc', '3', '发料物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'quantity_delivery', '3', '发料数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'basic_unit', '3', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'batch_number', '3', '发料批次号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'father_count', '3', '父件基本数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'children_count', '3', '组件用量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'work_order_row_num', '4', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'item_ num', '4', '测试程序序号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'test_routine', '4', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'test_versions', '4', '测试程序版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'combined_code', '4', '组合代码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'temperature', '4', '测试环境（温度）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'test_mode', '4', '测试方式', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '3', '1', 'explain', '4', '程序说明', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'work_order_num', '11', '工单号', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'work_order_type', '11', '工单类型', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'paper_date', '11', '凭证日期', '2', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'purchase_code', '11', '采购组织', '2', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'factory_code', '11', '工厂代码', '2', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'username', '11', '单据创建人', '2', 'A3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'statu', '11', '单据状态', '2', 'B3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'del_flag', '11', '删除标识', '2', 'C3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'supplier_code', '12', '供应商编码', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'manuf', '12', '制造商', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'purchase_linkman', '12', '采购方联系人', '1', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'purchase_phone', '12', '采购方联系电话', '1', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'purchase_email', '12', '采购方联系邮箱', '1', 'C2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'process', '13', '委外制程', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'produce_statu', '13', '生产状态', '1', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'rework', '13', '是否返工', '2', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'head_note', '14', '抬头备注', '1', 'ALL');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'button_choose_capsulation', '2', '封装信息选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'button_choose_test_routine', '2', '测试程序选择按钮', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'work_order_row_num', '2', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'del_flag', '2', '删除标识', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'is_final', '2', '是否最后产出品', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'up_layer_num', '2', '上层行号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'material_code', '2', '收货物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'material_desc', '2', '收货物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'stock_p_code', '2', '收货库存地点', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'quantity_receive', '2', '预计收货数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'basic_unit', '2', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'package_way', '2', '包装方式', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'mark', '2', '芯片丝印', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'batch_number', '2', '收货批次号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'delivery_limit', '2', '交货限制', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'excessive_delivery', '2', '过量交货容差', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'insufficient_delivery', '2', '交货不足容差', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'delivery_date', '2', '需求交货日期', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'order_num', '2', '采购合同/订单号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'order_row_num', '2', '采购合同/订单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'quantity_convert', '2', '转工单数量（基本单位）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'encapsulation_versions', '2', '封裝信息版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'wire_rod', '2', '线材', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'bd_graph', '2', '打线图（BD图）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'bd_graph_versions', '2', '打线图版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'wire_size', '2', '焊丝尺寸（线径）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'number', '2', '印章文件编号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'slices_way', '2', '取片方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'dedicated', '2', '框架是否专用', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'sticker_way', '2', '贴膜方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'encapsulation_mode', '2', '封装形式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'test_routines', '2', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'bom_versions', '2', 'BOM版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'status', '2', 'BOM状态', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'row_note', '2', '行项目备注', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'button_choose_stock', '3', '发料批次/片/BIN挑选按钮', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'work_order_row_num', '3', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'work_order_children_num', '3', '工单子件项目号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'stock_p_code', '3', '发料库存地点', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'material_code', '3', '发料物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'material_desc', '3', '发料物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'quantity_delivery', '3', '发料数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'basic_unit', '3', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'batch_number', '3', '发料批次号', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'father_count', '3', '父件基本数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'children_count', '3', '组件用量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'work_order_row_num', '4', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'item_ num', '4', '测试程序序号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'test_routine', '4', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'test_versions', '4', '测试程序版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'combined_code', '4', '组合代码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'temperature', '4', '测试环境（温度）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'test_mode', '4', '测试方式', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'CP', '2', '1', 'explain', '4', '程序说明', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'work_order_type', '11', '工单类型', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'paper_date', '11', '凭证日期', '4', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'purchase_code', '11', '采购组织', '4', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'factory_code', '11', '工厂代码', '4', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'username', '11', '单据创建人', '2', 'A3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'statu', '11', '单据状态', '2', 'B3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'del_flag', '11', '删除标识', '2', 'C3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'supplier_code', '12', '供应商编码', '4', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'work_order_num', '11', '工单号', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'manuf', '12', '制造商', '4', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'purchase_linkman', '12', '采购方联系人', '1', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'purchase_phone', '12', '采购方联系电话', '1', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'purchase_email', '12', '采购方联系邮箱', '1', 'C2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'process', '13', '委外制程', '4', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'produce_statu', '13', '生产状态', '4', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'rework', '13', '是否返工', '4', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'head_note', '14', '抬头备注', '1', 'ALL');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'button_choose_capsulation', '2', '封装信息选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'button_choose_test_routine', '2', '测试程序选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'work_order_row_num', '2', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'del_flag', '2', '删除标识', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'is_final', '2', '是否最后产出品', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'up_layer_num', '2', '上层行号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'material_code', '2', '收货物料编码', '4', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'material_desc', '2', '收货物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'stock_p_code', '2', '收货库存地点', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'quantity_receive', '2', '预计收货数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'basic_unit', '2', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'package_way', '2', '包装方式', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'mark', '2', '芯片丝印', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'batch_number', '2', '收货批次号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'delivery_limit', '2', '交货限制', '4', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'excessive_delivery', '2', '过量交货容差', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'insufficient_delivery', '2', '交货不足容差', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'delivery_date', '2', '需求交货日期', '4', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'order_num', '2', '采购合同/订单号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'order_row_num', '2', '采购合同/订单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'quantity_convert', '2', '转工单数量（基本单位）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'encapsulation_versions', '2', '封裝信息版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'wire_rod', '2', '线材', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'bd_graph', '2', '打线图（BD图）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'bd_graph_versions', '2', '打线图版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'wire_size', '2', '焊丝尺寸（线径）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'number', '2', '印章文件编号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'slices_way', '2', '取片方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'dedicated', '2', '框架是否专用', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'sticker_way', '2', '贴膜方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'encapsulation_mode', '2', '封装形式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'test_routines', '2', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'bom_versions', '2', 'BOM版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'status', '2', 'BOM状态', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'row_note', '2', '行项目备注', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'button_choose_stock', '3', '发料批次/片/BIN挑选按钮', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'work_order_row_num', '3', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'work_order_children_num', '3', '工单子件项目号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'stock_p_code', '3', '发料库存地点', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'material_code', '3', '发料物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'material_desc', '3', '发料物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'quantity_delivery', '3', '发料数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'basic_unit', '3', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'batch_number', '3', '发料批次号', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'father_count', '3', '父件基本数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'children_count', '3', '组件用量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'work_order_row_num', '4', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'item_ num', '4', '测试程序序号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'test_routine', '4', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'test_versions', '4', '测试程序版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'combined_code', '4', '组合代码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'temperature', '4', '测试环境（温度）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'test_mode', '4', '测试方式', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '1', '1', 'explain', '4', '程序说明', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'work_order_num', '11', '工单号', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'work_order_type', '11', '工单类型', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'paper_date', '11', '凭证日期', '2', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'purchase_code', '11', '采购组织', '2', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'factory_code', '11', '工厂代码', '2', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'username', '11', '单据创建人', '2', 'A3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'statu', '11', '单据状态', '2', 'B3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'del_flag', '11', '删除标识', '2', 'C3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'supplier_code', '12', '供应商编码', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'manuf', '12', '制造商', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'purchase_linkman', '12', '采购方联系人', '2', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'purchase_phone', '12', '采购方联系电话', '2', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'purchase_email', '12', '采购方联系邮箱', '2', 'C2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'process', '13', '委外制程', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'produce_statu', '13', '生产状态', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'rework', '13', '是否返工', '2', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'head_note', '14', '抬头备注', '2', 'ALL');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'button_choose_capsulation', '2', '封装信息选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'button_choose_test_routine', '2', '测试程序选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'work_order_row_num', '2', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'del_flag', '2', '删除标识', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'is_final', '2', '是否最后产出品', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'up_layer_num', '2', '上层行号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'material_code', '2', '收货物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'material_desc', '2', '收货物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'stock_p_code', '2', '收货库存地点', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'quantity_receive', '2', '预计收货数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'basic_unit', '2', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'package_way', '2', '包装方式', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'mark', '2', '芯片丝印', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'batch_number', '2', '收货批次号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'delivery_limit', '2', '交货限制', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'excessive_delivery', '2', '过量交货容差', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'insufficient_delivery', '2', '交货不足容差', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'delivery_date', '2', '需求交货日期', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'order_num', '2', '采购合同/订单号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'order_row_num', '2', '采购合同/订单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'quantity_convert', '2', '转工单数量（基本单位）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'encapsulation_versions', '2', '封裝信息版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'wire_rod', '2', '线材', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'bd_graph', '2', '打线图（BD图）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'bd_graph_versions', '2', '打线图版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'wire_size', '2', '焊丝尺寸（线径）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'number', '2', '印章文件编号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'slices_way', '2', '取片方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'dedicated', '2', '框架是否专用', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'sticker_way', '2', '贴膜方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'encapsulation_mode', '2', '封装形式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'test_routines', '2', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'bom_versions', '2', 'BOM版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'status', '2', 'BOM状态', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'row_note', '2', '行项目备注', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'button_choose_stock', '3', '发料批次/片/BIN挑选按钮', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'work_order_row_num', '3', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'work_order_children_num', '3', '工单子件项目号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'stock_p_code', '3', '发料库存地点', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'material_code', '3', '发料物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'material_desc', '3', '发料物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'quantity_delivery', '3', '发料数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'basic_unit', '3', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'batch_number', '3', '发料批次号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'father_count', '3', '父件基本数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'children_count', '3', '组件用量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'work_order_row_num', '4', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'item_ num', '4', '测试程序序号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'test_routine', '4', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'test_versions', '4', '测试程序版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'combined_code', '4', '组合代码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'temperature', '4', '测试环境（温度）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'test_mode', '4', '测试方式', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '3', '1', 'explain', '4', '程序说明', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'work_order_num', '11', '工单号', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'work_order_type', '11', '工单类型', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'paper_date', '11', '凭证日期', '2', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'purchase_code', '11', '采购组织', '2', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'factory_code', '11', '工厂代码', '2', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'username', '11', '单据创建人', '2', 'A3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'statu', '11', '单据状态', '2', 'B3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'del_flag', '11', '删除标识', '2', 'C3');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'supplier_code', '12', '供应商编码', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'manuf', '12', '制造商', '2', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'purchase_linkman', '12', '采购方联系人', '1', 'A2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'purchase_phone', '12', '采购方联系电话', '1', 'B2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'purchase_email', '12', '采购方联系邮箱', '1', 'C2');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'process', '13', '委外制程', '2', 'A1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'produce_statu', '13', '生产状态', '1', 'B1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'rework', '13', '是否返工', '2', 'C1');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'head_note', '14', '抬头备注', '1', 'ALL');
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'button_choose_capsulation', '2', '封装信息选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'button_choose_test_routine', '2', '测试程序选择按钮', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'work_order_row_num', '2', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'del_flag', '2', '删除标识', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'is_final', '2', '是否最后产出品', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'up_layer_num', '2', '上层行号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'material_code', '2', '收货物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'material_desc', '2', '收货物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'stock_p_code', '2', '收货库存地点', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'quantity_receive', '2', '预计收货数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'basic_unit', '2', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'package_way', '2', '包装方式', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'mark', '2', '芯片丝印', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'batch_number', '2', '收货批次号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'delivery_limit', '2', '交货限制', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'excessive_delivery', '2', '过量交货容差', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'insufficient_delivery', '2', '交货不足容差', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'delivery_date', '2', '需求交货日期', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'order_num', '2', '采购合同/订单号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'order_row_num', '2', '采购合同/订单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'quantity_convert', '2', '转工单数量（基本单位）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'encapsulation_versions', '2', '封裝信息版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'wire_rod', '2', '线材', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'bd_graph', '2', '打线图（BD图）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'bd_graph_versions', '2', '打线图版本', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'wire_size', '2', '焊丝尺寸（线径）', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'number', '2', '印章文件编号', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'slices_way', '2', '取片方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'dedicated', '2', '框架是否专用', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'sticker_way', '2', '贴膜方式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'encapsulation_mode', '2', '封装形式', '3', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'test_routines', '2', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'bom_versions', '2', 'BOM版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'status', '2', 'BOM状态', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'row_note', '2', '行项目备注', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'button_choose_stock', '3', '发料批次/片/BIN挑选按钮', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'work_order_row_num', '3', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'work_order_children_num', '3', '工单子件项目号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'stock_p_code', '3', '发料库存地点', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'material_code', '3', '发料物料编码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'material_desc', '3', '发料物料描述', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'quantity_delivery', '3', '发料数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'basic_unit', '3', '基本单位', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'batch_number', '3', '发料批次号', '1', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'father_count', '3', '父件基本数量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'children_count', '3', '组件用量', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'work_order_row_num', '4', '工单行号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'item_ num', '4', '测试程序序号', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'test_routine', '4', '测试程序', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'test_versions', '4', '测试程序版本', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'combined_code', '4', '组合代码', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'temperature', '4', '测试环境（温度）', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'test_mode', '4', '测试方式', '2', null);
INSERT INTO zjdata.f_d_work_order_field_config (work_order_type, process, screen_mode, statu, field, location, field_desc, control, location_xy) VALUES ('ZWO1', 'BP', '2', '1', 'explain', '4', '程序说明', '2', null);


-- ----------------------------
-- Records of f_d_receipts_state
-- ----------------------------
delete from zjdata.f_d_receipts_state;
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('0', '1', '已下达', 1600300683453272066);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('1', '1', '已创建', 1594584072973766658);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('1', '2', '已发布到供应商', 1594584072973766659);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('2', '1', '已下达', 1607662479289880577);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('3', '1', '已创建', 1607662516958924801);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('3', '2', '已发布到供应商', 1610476158582525954);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('5', '1', '已创建', 1611259295373725698);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('5', '2', '已发布到供应商', 1611259411023269890);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('6', '1', '已创建', 1611259514303811585);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('6', '2', '已审批', 1611259563796598785);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('7', '1', '已创建', 1613096001576730626);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('7', '2', '已审批', 1625793224243941378);
INSERT INTO zjdata.f_d_receipts_state (paper_type, statu, statu_desc, id) VALUES ('9', '1', '已下达', 1625793360374272001);


-- ----------------------------
-- Records of f_d_work_order_production_config
-- ----------------------------
delete from zjdata.f_d_work_order_production_config;
INSERT INTO "zjdata"."f_d_work_order_production_config" VALUES ('ZWO1', 'ENG', '工程', 1597508110674300930);
INSERT INTO "zjdata"."f_d_work_order_production_config" VALUES ('ZWO2', 'MP', '量产', 1597508161572179970);

-- ----------------------------
-- Records of f_d_batch_property_config
-- ----------------------------
delete from zjdata.f_d_batch_property_config;
INSERT INTO "zjdata"."f_d_batch_property_config" VALUES ('ZRKRQ', '入库日期', '4', 10, 0, 1593134474265890818);
INSERT INTO "zjdata"."f_d_batch_property_config" VALUES ('ZSCRQ', '生产日期', '4', 10, 0, 1593134163098865665);
INSERT INTO "zjdata"."f_d_batch_property_config" VALUES ('ZGYS', '供应商', '1', 20, 0, 1593134163098865666);

delete from zjdata.f_d_piece_config;
INSERT INTO "zjdata"."f_d_piece_config" VALUES ('ZRKRQ', '入库日期', '4', 10, 0, 1625663786861551617);
INSERT INTO "zjdata"."f_d_piece_config" VALUES ('ZSCRQ', '生产日期', '4', 10, 0, 1625663918847909890);
INSERT INTO "zjdata"."f_d_piece_config" VALUES ('ZGYS', '供应商', '1', 20, 0, 1625663918847909891);

-- ----------------------------
-- Records of p_d_manufacturer_manage
-- ----------------------------
delete  from  "zjdata"."p_d_manufacturer_manage";
INSERT INTO "zjdata"."p_d_manufacturer_manage" VALUES ('1', '供应商物料', '0');
INSERT INTO "zjdata"."p_d_manufacturer_manage" VALUES ('4', 'flow', '0');
INSERT INTO "zjdata"."p_d_manufacturer_manage" VALUES ('2', '封装信息', '0');
INSERT INTO "zjdata"."p_d_manufacturer_manage" VALUES ('3', '测试信息', '0');



LOAD 'age';
SET search_path = ag_catalog, '$user', public;


CREATE VIEW "zjdata_graph"."act_id_membership" AS
SELECT
    *
FROM
    ag_catalog.cypher (
            'zjdata_graph', $$ MATCH ( ROLE : sys_role ) -[ sys_user_role ]-> ( USER : sys_user ) RETURN ROLE.roleId AS group_id_,
            USER.userId AS user_id_$$
        ) AS ( group_id_ TEXT, user_id_ TEXT );


CREATE VIEW "zjdata_graph"."act_id_group" AS
select *
from ag_catalog.cypher('zjdata_graph', $$
    MATCH (node:sys_role)
    RETURN node.roleCode as id_ ,NULL AS rev_,node.roleName as name_,'assignment' AS type_
$$) as (id_ text,rev_ text,name_ text,type_ text);


CREATE VIEW "zjdata_graph"."act_id_user" AS
select *
from ag_catalog.cypher('zjdata_graph', $$
    MATCH (node:sys_user)
    RETURN node.userName as id_ ,0 as rev_,node.nickName as FIRST_,node.email as LAST_,node.password as PWD_,'' as PICTURE_ID_
$$) as (id_ text,rev_ text,FIRST_ text,LAST_ text,PWD_ text,PICTURE_ID_ text)