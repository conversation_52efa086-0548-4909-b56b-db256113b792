-- 权限列管理系统初始化SQL
LOAD 'age';
SET search_path = ag_catalog, '$user', public;

-- 创建权限列节点标签
SELECT ag_catalog.create_vlabel('zjdata_graph', 'sys_permission_column');

-- 创建角色与权限列的关联边标签
SELECT ag_catalog.create_elabel('zjdata_graph', 'sys_role_permission_column');

-- 示例：创建一些基础权限列节点
SELECT * FROM ag_catalog.cypher('zjdata_graph', $$
CREATE(:sys_permission_column {
    columnName: 'userId',
    columnDescription: '用户ID',
    visible: true
})
CREATE(:sys_permission_column {
    columnName: 'userName',
    columnDescription: '用户名',
    visible: true
})
CREATE(:sys_permission_column {
    columnName: 'roleId',
    columnDescription: '角色ID',
    visible: true
})
CREATE(:sys_permission_column {
    columnName: 'roleName',
    columnDescription: '角色名称',
    visible: true
})
CREATE(:sys_permission_column {
    columnName: 'companyId',
    columnDescription: '公司ID',
    visible: true
})
CREATE(:sys_permission_column {
    columnName: 'factoryId',
    columnDescription: '工厂ID',
    visible: true
})
$$) as (result ag_catalog.agtype);

-- 示例：为超级管理员角色分配所有权限列
SELECT COUNT(1) FROM ag_catalog.cypher('zjdata_graph', $$
MATCH (r:sys_role), (c:sys_permission_column)
WHERE r.roleId = 1
CREATE ((r)-[:sys_role_permission_column]->(c))
RETURN r, c
$$) as (r ag_catalog.agtype, c ag_catalog.agtype);
