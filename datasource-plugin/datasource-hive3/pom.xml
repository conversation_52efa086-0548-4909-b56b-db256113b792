<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.datalink-info</groupId>
        <artifactId>datasource-plugin</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>datasource-hive3</artifactId>

    <properties>
        <!-- engine-->
        <hadoop.version>2.9.2</hadoop.version>
        <flink.version>1.13.6</flink.version>
        <iceberg.version>0.13.1</iceberg.version>
        <scala.binary.version>2.12</scala.binary.version>
        <hive.version>3.1.2</hive.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!--把当前项目所有的依赖打包到target目录下的lib文件夹下-->
            <plugin>
                <groupId>com.gitee.starblues</groupId>
                <artifactId>spring-brick-maven-packager</artifactId>
                <configuration>
                    <mode>${plugin.runMode}</mode>
                    <pluginInfo>
                        <id>fdop-datasource-hive3</id>
                        <bootstrapClass>com.datalink.fdop.drive.plugin.datasource.hive3.HiveApplication
                        </bootstrapClass>
                        <version>2.0.0</version>
                        <provider>lzz</provider>
                        <description>hive插件</description>
                        <configFileName>application.yml</configFileName>
                    </pluginInfo>
                    <prodConfig>
                        <packageType>jar</packageType>
                        <outputDirectory>${project.basedir}/../../plugins/drive-center/2.0.0</outputDirectory>
                    </prodConfig>
                    <loadMainResourcePattern>
                        <includes>
                            <include>org.apache.hadoop.hive.metastore.**</include>
                            <include>org.apache.hadoop.conf.Configuration</include>
                            <include>org.apache.iceberg.hive.HiveCatalog</include>
                            <include>org.apache.hadoop.hive.conf.HiveConf</include>
                            <include>org.apache.hadoop.hive.conf.HiveConf.**</include>
                            <include>org.springframework.jdbc.core.JdbcTemplate</include>
                            <include>org.springframework.jdbc.core.**</include>
                            <include>org.apache.hadoop.fs.**</include>
                            <include>org.apache.iceberg.**</include>
                            <include>org.apache.thrift.**</include>
                            <!--<include>javax.servlet.http.HttpServlet</include>-->
                            <include>javax.servlet.**</include>
                        </includes>
                    </loadMainResourcePattern>

                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>