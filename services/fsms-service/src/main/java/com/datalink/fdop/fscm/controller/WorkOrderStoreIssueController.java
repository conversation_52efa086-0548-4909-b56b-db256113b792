package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.WorkOrderStoreIssue;
import com.datalink.fdop.fscm.service.RepertoryService;
import com.datalink.fdop.fscm.service.WorkOrderStoreIssueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:32
 */
@RestController
@RequestMapping("/work/order/store/issue")
@Transactional
@Api(tags = "工单发料")
public class WorkOrderStoreIssueController {

    @Autowired
    private WorkOrderStoreIssueService workOrderStoreIssueService;

    @Autowired
    private RepertoryService repertoryService;



    @ApiOperation(value = "查询工单发料")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/query")
    public R query(@RequestBody WorkOrderStoreIssue workOrderStoreIssue) {
        Page<WorkOrderStoreIssue> page = PageUtils.getPage(WorkOrderStoreIssue.class);
        Page<WorkOrderStoreIssue> pageData = workOrderStoreIssueService.page(page, new QueryWrapper<WorkOrderStoreIssue>()
                .lambda()
                .eq(StringUtils.isNotEmpty(workOrderStoreIssue.getWorkOrderNum()), WorkOrderStoreIssue::getWorkOrderNum, workOrderStoreIssue.getWorkOrderNum())
                .eq(workOrderStoreIssue.getWorkOrderRowNum()!=null, WorkOrderStoreIssue::getWorkOrderRowNum, workOrderStoreIssue.getWorkOrderRowNum())
                .eq(workOrderStoreIssue.getWorkOrderChildrenNum()!=null, WorkOrderStoreIssue::getWorkOrderChildrenNum, workOrderStoreIssue.getWorkOrderChildrenNum())
        );
        pageData.getRecords().stream().forEach(r->{
            r.setStockPDescription(repertoryService.findSPDesc(r.getStockPCode()));
        });
        return R.ok(PageUtils.getPageInfo(pageData.getRecords(),(int)pageData.getTotal()));
    }






}
