package com.datalink.fdop.fscm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.fscm.api.domain.ProvisionalSingleOut;
import com.datalink.fdop.fscm.api.domain.Repertory;
import com.datalink.fdop.fscm.api.domain.WorkOrderStoreIssue;
import com.datalink.fdop.fscm.api.domain.WorkOrderTest;
import com.datalink.fdop.fscm.mapper.WorkOrderTestMapper;
import com.datalink.fdop.fscm.service.ProvisionalSingleOutService;
import com.datalink.fdop.fscm.service.RepertoryService;
import com.datalink.fdop.fscm.service.WorkOrderStoreIssueService;
import com.datalink.fdop.fscm.service.WorkOrderTestService;
import com.datalink.fdop.fscm.utils.ObjectChangeUtlis;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:33
 */
@Service
@Transactional
public class WorkOrderTestServiceImpl extends ServiceImpl<WorkOrderTestMapper, WorkOrderTest> implements WorkOrderTestService {


    @Autowired
    private RepertoryService repertoryService;
    @Autowired
    private WorkOrderStoreIssueService workOrderStoreIssueService;
    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;



    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void reduce_inventory(String workOrderNum) {
        //先查发料表的所有数据，还原库存，删掉数据
        List<WorkOrderStoreIssue> storeIssues = workOrderStoreIssueService.list(new QueryWrapper<WorkOrderStoreIssue>().lambda()
                .eq(WorkOrderStoreIssue::getWorkOrderNum, workOrderNum));
        //factory_code（工厂） stock_p_code（库存地点）material_code（发料物料编码） batch_number（发料批次号） piece（发料片号） bin_num（发料BIN号）
        storeIssues.stream().forEach(storeIssue->{
            Repertory repertory = repertoryService.getOne(new QueryWrapper<Repertory>().lambda()
                    .eq(Repertory::getFactoryCode, storeIssue.getFactoryCode())
                    .eq(Repertory::getStockPCode, storeIssue.getStockPCode())
                    .eq(Repertory::getMaterialCode, storeIssue.getMaterialCode())
                    .eq(Repertory::getBatchNumber, storeIssue.getBatchNumber())
                    .eq(Repertory::getPiece,StringUtils.isNotEmpty(storeIssue.getPiece())? storeIssue.getPiece(): Constants.DATA_DEFAULT_VALUE)
                    .eq(Repertory::getBinNum,StringUtils.isNotEmpty(storeIssue.getBinNum())? storeIssue.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                    .last("  for update  ")
            );
            if (repertory!=null) {
                repertoryService.update(Wrappers.lambdaUpdate(Repertory.class)
                        .set(Repertory::getUnrestrictedStock,repertory.getUnrestrictedStock()+storeIssue.getQuantityDelivery())
                        .set(Repertory::getProcessStock,repertory.getProcessStock()-storeIssue.getQuantityDelivery())
                        .eq(Repertory::getFactoryCode, storeIssue.getFactoryCode())
                        .eq(Repertory::getMaterialCode, storeIssue.getMaterialCode())
                        .eq(Repertory::getBatchNumber, storeIssue.getBatchNumber())
                        .eq(Repertory::getPiece,StringUtils.isNotEmpty(storeIssue.getPiece())? storeIssue.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(Repertory::getBinNum,StringUtils.isNotEmpty(storeIssue.getBinNum())? storeIssue.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                );
            }
        });


        List<ProvisionalSingleOut> list = provisionalSingleOutService.list(new QueryWrapper<ProvisionalSingleOut>().lambda()
                .eq(ProvisionalSingleOut::getWorkOrderNum, workOrderNum));

        List<WorkOrderStoreIssue> workOrderStoreIssueList= Lists.newArrayList();
        for (ProvisionalSingleOut provisionalSingleOut : list) {
            Repertory repertory = repertoryService.getOne(new QueryWrapper<Repertory>().lambda()
                    .eq(Repertory::getFactoryCode, provisionalSingleOut.getFactoryCode())
                    .eq(Repertory::getStockPCode, provisionalSingleOut.getStockPCode())
                    .eq(Repertory::getMaterialCode, provisionalSingleOut.getMaterialCode())
                    .eq(Repertory::getBatchNumber, provisionalSingleOut.getBatchNumber())
                    .eq(Repertory::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece()) ? provisionalSingleOut.getPiece() : Constants.DATA_DEFAULT_VALUE)
                    .eq(Repertory::getBinNum, StringUtils.isNotEmpty(provisionalSingleOut.getBinNum()) ? provisionalSingleOut.getBinNum() : Constants.DATA_DEFAULT_VALUE)
                    .last("  for update  ")
            );
            if (repertory != null) {
                repertoryService.update(Wrappers.lambdaUpdate(Repertory.class)
                        .set(Repertory::getUnrestrictedStock, repertory.getUnrestrictedStock() - provisionalSingleOut.getQuantityDelivery())
                        .set(Repertory::getProcessStock, repertory.getProcessStock() + provisionalSingleOut.getQuantityDelivery())
                        .eq(Repertory::getFactoryCode, provisionalSingleOut.getFactoryCode())
                        .eq(Repertory::getMaterialCode, provisionalSingleOut.getMaterialCode())
                        .eq(Repertory::getBatchNumber, provisionalSingleOut.getBatchNumber())
                        .eq(Repertory::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece()) ? provisionalSingleOut.getPiece() : Constants.DATA_DEFAULT_VALUE)
                        .eq(Repertory::getBinNum, StringUtils.isNotEmpty(provisionalSingleOut.getBinNum()) ? provisionalSingleOut.getBinNum() : Constants.DATA_DEFAULT_VALUE)
                );
            }
            List<WorkOrderStoreIssue> workOrderStoreIssues = workOrderStoreIssueService.list(new QueryWrapper<WorkOrderStoreIssue>()
                    .lambda()
                    .eq(WorkOrderStoreIssue::getWorkOrderNum, provisionalSingleOut.getWorkOrderNum())
                    .eq(WorkOrderStoreIssue::getWorkOrderRowNum, provisionalSingleOut.getWorkOrderRowNum())
                    .eq(WorkOrderStoreIssue::getWorkOrderChildrenNum, provisionalSingleOut.getWorkOrderChildrenNum())
                    .eq(WorkOrderStoreIssue::getMaterialCode, provisionalSingleOut.getMaterialCode())
                    .eq(WorkOrderStoreIssue::getStockPCode, provisionalSingleOut.getStockPCode())
                    .eq(WorkOrderStoreIssue::getBatchNumber, provisionalSingleOut.getBatchNumber())
                    .eq(WorkOrderStoreIssue::getPiece, com.datalink.fdop.common.core.utils.StringUtils.isNotEmpty(provisionalSingleOut.getPiece()) ? provisionalSingleOut.getPiece() : Constants.DATA_DEFAULT_VALUE)
                    .eq(WorkOrderStoreIssue::getBinNum, com.datalink.fdop.common.core.utils.StringUtils.isNotEmpty(provisionalSingleOut.getBinNum()) ? provisionalSingleOut.getBinNum() : Constants.DATA_DEFAULT_VALUE)
            );
            double sum1 = workOrderStoreIssues.stream().map(WorkOrderStoreIssue::getQuantityConsume).mapToDouble(m -> m).sum();

            WorkOrderStoreIssue change = ObjectChangeUtlis.change(provisionalSingleOut);
            change.setQuantityConsume(sum1);
            workOrderStoreIssueList.add(change);
        }
        //删除工单发料表得数据
        workOrderStoreIssueService.remove(new QueryWrapper<WorkOrderStoreIssue>().lambda().eq(WorkOrderStoreIssue::getWorkOrderNum, workOrderNum));
        workOrderStoreIssueService.saveBatch(workOrderStoreIssueList);
    }
}
