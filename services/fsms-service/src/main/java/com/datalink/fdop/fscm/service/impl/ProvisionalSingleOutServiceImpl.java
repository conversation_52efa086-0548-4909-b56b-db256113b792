package com.datalink.fdop.fscm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.mapper.ProvisionalSingleOutCacheMapper;
import com.datalink.fdop.fscm.mapper.ProvisionalSingleOutMapper;
import com.datalink.fdop.fscm.service.ProvisionalSingleOutCacheService;
import com.datalink.fdop.fscm.service.ProvisionalSingleOutService;
import com.datalink.fdop.fscm.service.TemplateService;
import com.datalink.fdop.fscm.service.WorkOrderStoreIssueService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:33
 */
@Service
public class ProvisionalSingleOutServiceImpl extends ServiceImpl<ProvisionalSingleOutMapper, ProvisionalSingleOut> implements ProvisionalSingleOutService {

    @Autowired
    private TemplateService templateService;
    @Autowired
    private ProvisionalSingleOutMapper provisionalSingleOutMapper;
    @Autowired
    private ProvisionalSingleOutCacheMapper provisionalSingleOutCacheMapper;
    @Autowired
    private ProvisionalSingleOutCacheService provisionalSingleOutCacheService;
    @Autowired
    private WorkOrderStoreIssueService workOrderStoreIssueService;



    @Override
    public long getProvisionalSingleOutCount(WorkOrderChild workOrderChild) {
        List<ProvisionalSingleOut> singleOutList = provisionalSingleOutMapper.selectList(new QueryWrapper<ProvisionalSingleOut>()
                .lambda()
                .select(ProvisionalSingleOut::getWorkOrderNum)
                .select(ProvisionalSingleOut::getWorkOrderRowNum)
                .select(ProvisionalSingleOut::getWorkOrderChildrenNum)
                .select(ProvisionalSingleOut::getMaterialCode)
                .select(ProvisionalSingleOut::getStockPCode)
                .select(ProvisionalSingleOut::getBatchNumber)
                .eq(ProvisionalSingleOut::getWorkOrderNum, workOrderChild.getWorkOrderNum())
                .eq(ProvisionalSingleOut::getWorkOrderRowNum, workOrderChild.getWorkOrderRowNum())
                .eq(ProvisionalSingleOut::getWorkOrderChildrenNum, workOrderChild.getWorkOrderChildrenNum())
                .groupBy(ProvisionalSingleOut::getWorkOrderNum)
                .groupBy(ProvisionalSingleOut::getWorkOrderRowNum)
                .groupBy(ProvisionalSingleOut::getWorkOrderChildrenNum)
                .groupBy(ProvisionalSingleOut::getMaterialCode)
                .groupBy(ProvisionalSingleOut::getStockPCode)
                .groupBy(ProvisionalSingleOut::getBatchNumber)
        );
        if (CollectionUtils.isNotEmpty(singleOutList)) {
            return singleOutList.size();
        }
        return 0;
    }


    @Override
    public long getProvisionalSingleOutCacheCount(ProvisionalSingleOutCache cache) {
        List<ProvisionalSingleOutCache> singleOutList = provisionalSingleOutCacheMapper.selectList(new QueryWrapper<ProvisionalSingleOutCache>()
                .lambda()
                .select(ProvisionalSingleOutCache::getWorkOrderNum)
                .select(ProvisionalSingleOutCache::getWorkOrderRowNum)
                .select(ProvisionalSingleOutCache::getWorkOrderChildrenNum)
                .select(ProvisionalSingleOutCache::getMaterialCode)
                .select(ProvisionalSingleOutCache::getStockPCode)
                .select(ProvisionalSingleOutCache::getBatchNumber)
                .eq(ProvisionalSingleOutCache::getWorkOrderNum, cache.getWorkOrderNum())
                .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, cache.getWorkOrderRowNum())
                .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum, cache.getWorkOrderChildrenNum())
                .groupBy(ProvisionalSingleOutCache::getWorkOrderNum)
                .groupBy(ProvisionalSingleOutCache::getWorkOrderRowNum)
                .groupBy(ProvisionalSingleOutCache::getWorkOrderChildrenNum)
                .groupBy(ProvisionalSingleOutCache::getMaterialCode)
                .groupBy(ProvisionalSingleOutCache::getStockPCode)
                .groupBy(ProvisionalSingleOutCache::getBatchNumber)
        );
        if (CollectionUtils.isNotEmpty(singleOutList)) {
            return singleOutList.size();
        }
        return 0;
    }
    @Override
    public List<ProvisionalSingleOut> getProvisionalSingleOutByRow(String workOrderNum, Long workOrderRowNum) {
        return provisionalSingleOutMapper.selectList(new QueryWrapper<ProvisionalSingleOut>()
                .lambda()
                .eq(ProvisionalSingleOut::getWorkOrderNum,workOrderNum)
                .eq(ProvisionalSingleOut::getWorkOrderRowNum,workOrderRowNum)
        );
    }

    @Override
    public List<ProvisionalSingleOut> getProvisionalSingleOutByChild(WorkOrderChild orderChildren) {
        return provisionalSingleOutMapper.selectList(new QueryWrapper<ProvisionalSingleOut>()
                .lambda()
                .eq(ProvisionalSingleOut::getWorkOrderNum, orderChildren.getWorkOrderNum())
                .eq(ProvisionalSingleOut::getWorkOrderRowNum, orderChildren.getWorkOrderRowNum())
                .eq(ProvisionalSingleOut::getWorkOrderChildrenNum, orderChildren.getWorkOrderChildrenNum())
        );
    }

    @Override
    public List<ProvisionalSingleOut> getProvisionalSingleOutByChildGroupBy(WorkOrderChild orderChildren) {
        return provisionalSingleOutMapper.selectList(new QueryWrapper<ProvisionalSingleOut>()
                .lambda()
                .select(ProvisionalSingleOut::getMaterialCode)
                .select(ProvisionalSingleOut::getStockPCode)
                .select(ProvisionalSingleOut::getBatchNumber)
                .eq(ProvisionalSingleOut::getWorkOrderNum, orderChildren.getWorkOrderNum())
                .eq(ProvisionalSingleOut::getWorkOrderRowNum, orderChildren.getWorkOrderRowNum())
                .eq(ProvisionalSingleOut::getWorkOrderChildrenNum, orderChildren.getWorkOrderChildrenNum())
                .groupBy(ProvisionalSingleOut::getWorkOrderNum)
                .groupBy(ProvisionalSingleOut::getWorkOrderRowNum)
                .groupBy(ProvisionalSingleOut::getWorkOrderChildrenNum)
                .groupBy(ProvisionalSingleOut::getMaterialCode)
                .groupBy(ProvisionalSingleOut::getStockPCode)
                .groupBy(ProvisionalSingleOut::getBatchNumber)
        );
    }

    @Override
    public List<ProvisionalSingleOut> getProvisionalSingleOutByRowGroupBy(String workOrderNum, Long workOrderRowNum, Long childrenNum) {
        return provisionalSingleOutMapper.getProvisionalSingleOutByRowGroupBy(workOrderNum,workOrderRowNum,childrenNum);
    }

    @Override
    public void setVal(ProvisionalSingleOut provisionalSingleOut, WorkOrderChild child, ProvisionalSingleOut oldSingleOut) {
        child.setBatchNumber(provisionalSingleOut.getBatchNumber());
        child.setQuantityDelivery(provisionalSingleOut.getQuantityDelivery());
        child.setBasicUnit(oldSingleOut.getBasicUnit());

    }

    @Override
    public void synchronizationCache(String workOrderNum, Long workOrderRowNum, Long workOrderChildrenNum) {
        List<ProvisionalSingleOutCache> cacheList = provisionalSingleOutCacheMapper.selectList(new QueryWrapper<ProvisionalSingleOutCache>()
                .lambda()
                .eq(ProvisionalSingleOutCache::getWorkOrderNum,workOrderNum)
                .eq(ProvisionalSingleOutCache::getWorkOrderRowNum,workOrderRowNum)
                .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum,workOrderChildrenNum)
        );
        if (CollectionUtils.isEmpty(cacheList)) {
            List<ProvisionalSingleOut> singleOutList = provisionalSingleOutMapper.selectList(new QueryWrapper<ProvisionalSingleOut>()
                    .lambda()
                    .eq(ProvisionalSingleOut::getWorkOrderNum, workOrderNum)
                    .eq(ProvisionalSingleOut::getWorkOrderRowNum, workOrderRowNum)
                    .eq(ProvisionalSingleOut::getWorkOrderChildrenNum, workOrderChildrenNum)
            );
            if (CollectionUtils.isNotEmpty(singleOutList)) {
                List<ProvisionalSingleOutCache> singleOutCaches= Lists.newArrayList();
                //缓存数据插入临时表
                for (ProvisionalSingleOut singleOut : singleOutList) {
                    ProvisionalSingleOutCache singleOutCache=new ProvisionalSingleOutCache();
                    singleOutCache.setWorkOrderNum(singleOut.getWorkOrderNum());
                    singleOutCache.setWorkOrderRowNum(singleOut.getWorkOrderRowNum());
                    singleOutCache.setWorkOrderChildrenNum(singleOut.getWorkOrderChildrenNum());
                    singleOutCache.setMaterialCode(singleOut.getMaterialCode());
                    singleOutCache.setMaterialDesc(singleOut.getMaterialDesc());
                    singleOutCache.setFactoryCode(singleOut.getFactoryCode());
                    singleOutCache.setStockPCode(singleOut.getStockPCode());
                    singleOutCache.setStockPDescription(singleOut.getStockPDescription());
                    singleOutCache.setBatchNumber(singleOut.getBatchNumber());
                    singleOutCache.setPiece(singleOut.getPiece());
                    singleOutCache.setBinNum(singleOut.getBinNum());
                    singleOutCache.setQuantityDelivery(singleOut.getQuantityDelivery());
                    singleOutCache.setQuantityDeliveryVersions(singleOut.getQuantityDeliveryVersions());
                    singleOutCache.setBasicUnit(singleOut.getBasicUnit());
                    singleOutCaches.add(singleOutCache);
                }
                provisionalSingleOutCacheService.saveBatch(singleOutCaches);
            }
        }
    }

    @Override
    public void synchronizationProvisional(String workOrderNum, List<Long> workOrderRowNums) {
        for (Long workOrderRowNum : workOrderRowNums) {
            //查出缓存数据
            provisionalSingleOutMapper.delete(new QueryWrapper<ProvisionalSingleOut>()
                    .lambda()
                    .eq(ProvisionalSingleOut::getWorkOrderNum, workOrderNum)
                    .eq(ProvisionalSingleOut::getWorkOrderRowNum, workOrderRowNum)
            );
            List<ProvisionalSingleOutCache> singleOutList = provisionalSingleOutCacheMapper.selectList(new QueryWrapper<ProvisionalSingleOutCache>()
                    .lambda()
                    .eq(ProvisionalSingleOutCache::getWorkOrderNum,workOrderNum)
                    .eq(ProvisionalSingleOutCache::getWorkOrderRowNum,workOrderRowNum)
            );
            if (CollectionUtils.isNotEmpty(singleOutList)) {
                //缓存数据插入临时表
                List<ProvisionalSingleOut> singleOuts= Lists.newArrayList();
                for (ProvisionalSingleOutCache singleOutCache : singleOutList) {
                    ProvisionalSingleOut provisionalSingleOut=new ProvisionalSingleOut();
                    provisionalSingleOut.setWorkOrderNum(singleOutCache.getWorkOrderNum());
                    provisionalSingleOut.setWorkOrderRowNum(singleOutCache.getWorkOrderRowNum());
                    provisionalSingleOut.setWorkOrderChildrenNum(singleOutCache.getWorkOrderChildrenNum());
                    provisionalSingleOut.setMaterialCode(singleOutCache.getMaterialCode());
                    provisionalSingleOut.setMaterialDesc(singleOutCache.getMaterialDesc());
                    provisionalSingleOut.setFactoryCode(singleOutCache.getFactoryCode());
                    provisionalSingleOut.setStockPCode(singleOutCache.getStockPCode());
                    provisionalSingleOut.setStockPDescription(singleOutCache.getStockPDescription());
                    provisionalSingleOut.setBatchNumber(singleOutCache.getBatchNumber());
                    provisionalSingleOut.setPiece(singleOutCache.getPiece());
                    provisionalSingleOut.setBinNum(singleOutCache.getBinNum());
                    provisionalSingleOut.setQuantityDelivery(singleOutCache.getQuantityDelivery());
                    provisionalSingleOut.setQuantityDeliveryVersions(singleOutCache.getQuantityDeliveryVersions());
                    provisionalSingleOut.setBasicUnit(singleOutCache.getBasicUnit());
                    singleOuts.add(provisionalSingleOut);
                }
                for (ProvisionalSingleOut singleOut : singleOuts) {
                    provisionalSingleOutMapper.insert(singleOut);
                }

            }
            //删除临时表数据
            provisionalSingleOutCacheService.remove(new QueryWrapper<ProvisionalSingleOutCache>()
                    .lambda()
                    .eq(ProvisionalSingleOutCache::getWorkOrderNum, workOrderNum)
                    .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, workOrderRowNum)
            );
        }

    }

    @Override
    public List<ProvisionalSingleOut> getSingleOutByRowGroupBy(WorkOrderRow row) {
        return provisionalSingleOutMapper.getSingleOutByRowGroupBy(row.getWorkOrderNum(),row.getWorkOrderRowNum());
    }

    @Override
    public void setPieBin(WorkOrderChild workOrderChild) {

        List<ProvisionalSingleOut> provisionalSingleOutByChild = getProvisionalSingleOutByChild(workOrderChild);
        System.out.println("子件：------"+provisionalSingleOutByChild);
        List<String> pieceList = provisionalSingleOutByChild.stream().map(ProvisionalSingleOut::getPiece).distinct().sorted().collect(Collectors.toList());
        String piece="";
        for (String s : pieceList) {
            if (StringUtils.isNotEmpty(s)) {
                piece+=s+",";
            }
        }
        String binNum = "";
        List<String> binNumList = provisionalSingleOutByChild.stream().map(ProvisionalSingleOut::getBinNum).distinct().sorted().collect(Collectors.toList());
        for (String s : binNumList) {
            if (StringUtils.isNotEmpty(s)) {
                binNum += s + ",";
            }
        }
        double sum = provisionalSingleOutByChild.stream().map(ProvisionalSingleOut::getQuantityDelivery).mapToDouble(m -> m).sum();

        if (CollectionUtils.isEmpty(provisionalSingleOutByChild)) {
            workOrderChild.setBatchNumber(null);
            workOrderChild.setStockPCode(null);
            workOrderChild.setQuantityDelivery(null);
            workOrderChild.setBasicUnit(null);
            workOrderChild.setBatchNumber(null);
            workOrderChild.setStockPDescription(null);
        }else {
            workOrderChild.setQuantityDelivery(sum);
            workOrderChild.setStockPCode(provisionalSingleOutByChild.get(0).getStockPCode());
            workOrderChild.setBatchNumber(provisionalSingleOutByChild.get(0).getBatchNumber());
            if (StringUtils.isNotEmpty(workOrderChild.getStockPCode())) {
                workOrderChild.setStockPDescription(templateService.findStockPDescription(workOrderChild.getStockPCode()));
            }
        }
        workOrderChild.setPiece(StringUtils.isNotEmpty(piece) ? piece.substring(0, piece.length() - 1).replace(",", "，") : null);
        workOrderChild.setBinNum(StringUtils.isNotEmpty(binNum) ? binNum.substring(0, binNum.length() - 1).replace(",", "，") : null);





    }

    @Override
    public void changeRefreshCache(String workOrderNum) {
        //删除缓存表里得数据
        provisionalSingleOutMapper.delete(new QueryWrapper<ProvisionalSingleOut>()
                .lambda()
                .eq(ProvisionalSingleOut::getWorkOrderNum,workOrderNum));
        //查出发料表的数据
        List<WorkOrderStoreIssue> list = workOrderStoreIssueService.list(new QueryWrapper<WorkOrderStoreIssue>()
                .lambda()
                .eq(WorkOrderStoreIssue::getWorkOrderNum, workOrderNum));

        for (WorkOrderStoreIssue workOrderStoreIssue : list) {
            ProvisionalSingleOut singleOut = new ProvisionalSingleOut();
            singleOut.setWorkOrderNum(workOrderStoreIssue.getWorkOrderNum());
            singleOut.setWorkOrderRowNum(workOrderStoreIssue.getWorkOrderRowNum());
            singleOut.setWorkOrderChildrenNum(workOrderStoreIssue.getWorkOrderChildrenNum());
            singleOut.setMaterialCode(workOrderStoreIssue.getMaterialCode());
            singleOut.setMaterialDesc(workOrderStoreIssue.getMaterialDesc());
            singleOut.setQuantityDelivery(workOrderStoreIssue.getQuantityDelivery());
            singleOut.setBatchNumber(workOrderStoreIssue.getBatchNumber());
            singleOut.setBasicUnit(workOrderStoreIssue.getBasicUnit());
            singleOut.setBinNum(workOrderStoreIssue.getBinNum());
            singleOut.setFactoryCode(workOrderStoreIssue.getFactoryCode());
            singleOut.setPiece(workOrderStoreIssue.getPiece());
            singleOut.setStockPCode(workOrderStoreIssue.getStockPCode());
            provisionalSingleOutMapper.insert(singleOut);
        }


    }

    @Override
    public void delCache(List<ProvisionalSingleOut> provisionalSingleOuts) {
        provisionalSingleOuts.stream().forEach(provisionalSingleOut -> {
            if (provisionalSingleOut!=null) {
                //删除挑片
                this.remove(new QueryWrapper<ProvisionalSingleOut>().lambda()
                        .eq(StringUtils.isNotEmpty(provisionalSingleOut.getTransferOrderNum()),ProvisionalSingleOut::getTransferOrderNum,provisionalSingleOut.getTransferOrderNum())
                        .eq(provisionalSingleOut.getTransferOrderRowNum()!=null,ProvisionalSingleOut::getTransferOrderRowNum,provisionalSingleOut.getTransferOrderRowNum())
                        .eq(StringUtils.isNotEmpty(provisionalSingleOut.getScrapOrderNum()),ProvisionalSingleOut::getScrapOrderNum,provisionalSingleOut.getScrapOrderNum())
                        .eq(provisionalSingleOut.getScrapOrderRowNum()!=null,ProvisionalSingleOut::getScrapOrderRowNum,provisionalSingleOut.getScrapOrderRowNum())
                        .eq(StringUtils.isNotEmpty(provisionalSingleOut.getShipRequestOrderNum()),ProvisionalSingleOut::getShipRequestOrderNum,provisionalSingleOut.getShipRequestOrderNum())
                        .eq(StringUtils.isNotEmpty(provisionalSingleOut.getShipRequestOrderRowNum()),ProvisionalSingleOut::getShipRequestOrderRowNum,provisionalSingleOut.getShipRequestOrderRowNum())
                        .eq(StringUtils.isNotEmpty(provisionalSingleOut.getPickReturnOrderNum()),ProvisionalSingleOut::getPickReturnOrderNum,provisionalSingleOut.getPickReturnOrderNum())
                        .eq(provisionalSingleOut.getPickReturnOrderRowNum()!=null,ProvisionalSingleOut::getPickReturnOrderRowNum,provisionalSingleOut.getPickReturnOrderRowNum())
                        .eq(StringUtils.isNotEmpty(provisionalSingleOut.getUserName()),ProvisionalSingleOut::getUserName,provisionalSingleOut.getUserName())
                );
            }
        });
    }

    @Override
    public void changeRefreshCache2(ProvisionalSingleOut provisionalSingleOut) {
        //删除缓存表里得数据
        provisionalSingleOutMapper.delete(new QueryWrapper<ProvisionalSingleOut>()
                .lambda()
                .eq(provisionalSingleOut.getTransferOrderNum()!=null,ProvisionalSingleOut::getWorkOrderNum,provisionalSingleOut.getTransferOrderNum())
                .eq(provisionalSingleOut.getShipRequestOrderNum()!=null,ProvisionalSingleOut::getWorkOrderNum,provisionalSingleOut.getTransferOrderNum())
                .eq(provisionalSingleOut.getPickReturnOrderNum()!=null,ProvisionalSingleOut::getWorkOrderNum,provisionalSingleOut.getTransferOrderNum())
                .eq(provisionalSingleOut.getScrapOrderNum()!=null,ProvisionalSingleOut::getWorkOrderNum,provisionalSingleOut.getTransferOrderNum())
        );
    }
}
