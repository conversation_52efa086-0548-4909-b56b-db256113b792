package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.ip.IpUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderPostCertificateVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderQueryShowVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderQueryVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderVo;
import com.datalink.fdop.fscm.mapper.FSCMTemplateMapper;
import com.datalink.fdop.fscm.service.*;
import com.datalink.fdop.project.api.model.vo.OrderQueryShowVo;
import com.datalink.fdop.project.api.model.vo.OrderQueryVo;
import com.datalink.fdop.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:32
 */
@RestController
@RequestMapping("/work/order/head")
@Transactional
@Api(tags = "工单抬头")
public class WorkOrderHeadController {

    @Autowired
    private WorkOrderHeadService workOrderHeadService;
    @Autowired
    private WorkOrderRowService workOrderRowService;
    @Autowired
    private WorkOrderChildService workOrderChildService;
    @Autowired
    private WorkOrderTestService workOrderTestService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;
    @Autowired
    private WorkOrderExcelService workOrderExcelService;
    @Autowired
    private FSCMTemplateMapper fscmTemplateMapper;
    @Autowired
    private ProvisionalSingleOutController provisionalSingleOutController;
    @Autowired
    private RepertoryService repertoryService;

    @ApiOperation(value = "采购订单查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody OrderQueryVo orderQueryVo,@RequestParam(required = false) String flag) {
        Page<OrderQueryShowVo> page = PageUtils.getPage(OrderQueryShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (orderQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<OrderQueryShowVo> dataPage= workOrderHeadService.queryData(page,orderQueryVo,flag);
        List<OrderQueryShowVo> records = dataPage.getRecords();
        records.stream().forEach(record->{
            List<WorkOrderRow> list = workOrderRowService.list(new QueryWrapper<WorkOrderRow>().lambda()
                    .eq(WorkOrderRow::getOrderNum, record.getOrderNum())
                    .eq(WorkOrderRow::getOrderRowNum, record.getOrderRowNum())
            );
            long count=0;
            for (WorkOrderRow workOrderRow : list) {
                count+=workOrderRow.getQuantityReceive();
            }
            record.setForwardedQuantity(count/record.getBasicUnitNum()*record.getOrderUnitNum());
        });
        if (StringUtils.isNotEmpty(flag)&&flag.equals("1")) {
            records = records.stream().filter(record ->
                    (StringUtils.isNotEmpty(record.getOrderQuantityReceive()) ? Double.valueOf(record.getOrderQuantityReceive()) : 0) - record.getForwardedQuantity() > 0)
                    .collect(Collectors.toList());
        }
        if (orderQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(orderQueryVo.getSearchVo(), records);
            dataPage.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records,(int)dataPage.getTotal()));
    }





    @ApiOperation(value = "委外工单查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("work/order/query")
    public R workOrderQuery(@RequestBody WorkOrderQueryVo workOrderQueryVo,@RequestParam( required = false) String delFlag,@RequestParam( required = false) String temporary) {
        Page<WorkOrderQueryShowVo> page = PageUtils.getPage(WorkOrderQueryShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (workOrderQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderQueryShowVo> dataPage= workOrderHeadService.queryWorkOrderData(page,workOrderQueryVo,delFlag,temporary);
        List<WorkOrderQueryShowVo> records = dataPage.getRecords();
        records.stream().forEach(record->{
            List<PostCertificateRow> list = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getWorkOrderNum, record.getWorkOrderNum())
                    .eq(PostCertificateRow::getWorkOrderRowNum, record.getWorkOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("101","102"))
            );
            double sum = list.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(postQuantity -> postQuantity).sum();
            record.setReceived(sum);
            List<PostCertificateRow> certificateRows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getWorkOrderNum, record.getWorkOrderNum())
                    .eq(PostCertificateRow::getWorkOrderRowNum, record.getWorkOrderRowNum())
                    .eq(PostCertificateRow::getMoveType, "101")
                    .eq(PostCertificateRow::getWriteOff,"0")
            );
            double sum1 = certificateRows.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(postQuantity -> postQuantity).sum();
            record.setDefectiveProducts(sum1);
            record.setShortQuantityReceived(record.getQuantityReceive()-sum-sum1);
        });
        if (workOrderQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(workOrderQueryVo.getSearchVo(), records);
            dataPage.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records,(int)dataPage.getTotal()));
    }

    @ApiOperation(value = "工单头查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/head/query")
    public R workOrderHeadQuery(@RequestBody WorkOrderHead workOrderHead) {
        Page<WorkOrderHead> page = PageUtils.getPage(WorkOrderHead.class);
        Page<WorkOrderHead> dataPage= workOrderHeadService.page(page,new QueryWrapper<WorkOrderHead>()
                .lambda()
                .eq(StringUtils.isNotEmpty(workOrderHead.getWorkOrderNum()),WorkOrderHead::getWorkOrderNum,workOrderHead.getWorkOrderNum())
                .eq(WorkOrderHead::getDelFlag,"0")
        );
        List<WorkOrderHead> records = dataPage.getRecords();

        return R.ok(PageUtils.getPageInfo(records,(int)dataPage.getTotal()));
    }
    @ApiOperation(value = "获取工单号")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/getWorkOrderNum")
    public R getWorkOrderNum(@RequestParam String workOrderType, @RequestParam(required = false) String workOrderNum) {
        //获取工单工单头id
        workOrderNum= workOrderHeadService.getWorkOrderNum(workOrderType,workOrderNum);
        return R.ok(workOrderNum);
    }

    @ApiOperation(value = "工单保存或暂存(0保存，1暂存)")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/work/order/save")
    @Transactional
    public R workOrderQuery(@RequestBody WorkOrderVo workOrderVo, @RequestParam String temporary) {
        WorkOrderHead workOrderHead = workOrderVo.getWorkOrderHead();
        workOrderHead.setDelFlag("0");
        workOrderHead.setTemporary(temporary);
        //查询工单头
        WorkOrderHead one = workOrderHeadService.getOne(new QueryWrapper<WorkOrderHead>()
                .lambda()
                .eq(WorkOrderHead::getWorkOrderNum, workOrderHead.getWorkOrderNum()));
        if (one!=null) {
            workOrderHeadService.remove(new QueryWrapper<WorkOrderHead>()
                    .lambda()
                    .eq(WorkOrderHead::getWorkOrderNum, workOrderHead.getWorkOrderNum()));
        }

        if (StringUtils.isEmpty(workOrderHead.getWorkOrderNum())) {
            throw new ServiceException("工单号不能为空！");
        }
        try {
            workOrderHeadService.save(workOrderHead);
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查订单头数据是否正确");
        }



        //工单行
        List<WorkOrderRow> workOrderRows = workOrderVo.getWorkOrderRows();
        workOrderRowService.remove(new QueryWrapper<WorkOrderRow>()
                .lambda()
                .eq(WorkOrderRow::getWorkOrderNum, workOrderHead.getWorkOrderNum()));
        workOrderChildService.remove(new QueryWrapper<WorkOrderChild>()
                .lambda()
                .eq(WorkOrderChild::getWorkOrderNum, workOrderHead.getWorkOrderNum()));
        workOrderTestService.remove(new QueryWrapper<WorkOrderTest>()
                .lambda()
                .eq(WorkOrderTest::getWorkOrderNum, workOrderHead.getWorkOrderNum()));
        //工单子件
        List<WorkOrderChild> workOrderChildList = workOrderVo.getWorkOrderChildList();
        workOrderChildList.stream().forEach(workOrderChild -> {
            if (workOrderChild.getWorkOrderNum()==null) {
                workOrderChild.setWorkOrderNum(workOrderHead.getWorkOrderNum());
            }
            String materialCode = workOrderChild.getMaterialCode();
            if (StringUtils.isNotEmpty(materialCode)) {
                String materialdesc = workOrderExcelService.findMaterialdesc(materialCode);
                String unit = fscmTemplateMapper.findUnit(materialCode);
                workOrderChild.setBasicUnit(unit);
                workOrderChild.setMaterialDesc(materialdesc);
            }
            WorkOrderChild child= workOrderChildService.getOne(new QueryWrapper<WorkOrderChild>()
                    .lambda()
                    .eq(WorkOrderChild::getWorkOrderNum, workOrderChild.getWorkOrderNum())
                    .eq(WorkOrderChild::getWorkOrderRowNum, workOrderChild.getWorkOrderRowNum())
                    .eq(WorkOrderChild::getWorkOrderChildrenNum, workOrderChild.getWorkOrderChildrenNum())
            );
            if (child!=null) {
                workOrderChildService.remove(new QueryWrapper<WorkOrderChild>()
                        .lambda()
                        .eq(WorkOrderChild::getWorkOrderNum, workOrderChild.getWorkOrderNum())
                        .eq(WorkOrderChild::getWorkOrderRowNum, workOrderChild.getWorkOrderRowNum())
                        .eq(WorkOrderChild::getWorkOrderChildrenNum, workOrderChild.getWorkOrderChildrenNum())
                );
            }
            if (workOrderChild.getWorkOrderRowNum()==null) {
                throw new ServiceException("子件工单行号不能为空！");
            }
            if (workOrderChild.getWorkOrderChildrenNum()==null) {
                throw new ServiceException("子件工单子件项目号不能为空！");
            }
            try {
                workOrderChildService.save(workOrderChild);
            }catch (Exception e){
                e.printStackTrace();
                throw new ServiceException("检查订单测试子件数据是否正确");
            }

        });
        if (temporary.equals("0")) {
            //校验
            workOrderHeadService.checkRowData(workOrderHead,workOrderRows);
        }
        workOrderRows.forEach(workOrderRow -> {
            if (StringUtils.isEmpty(workOrderRow.getWorkOrderNum())) {
                workOrderRow.setWorkOrderNum(workOrderHead.getWorkOrderNum());
            }
            String materialCode = workOrderRow.getMaterialCode();
            if (StringUtils.isNotEmpty(materialCode)) {
                String materialdesc = workOrderExcelService.findMaterialdesc(materialCode);
                String unit = fscmTemplateMapper.findUnit(materialCode);
                workOrderRow.setBasicUnit(unit);
                workOrderRow.setMaterialDesc(materialdesc);
            }
            WorkOrderRow orderRow = workOrderRowService.getOne(new QueryWrapper<WorkOrderRow>()
                    .lambda()
                    .eq(WorkOrderRow::getWorkOrderNum, workOrderRow.getWorkOrderNum())
                    .eq(WorkOrderRow::getWorkOrderRowNum, workOrderRow.getWorkOrderRowNum())
            );
            if (orderRow!=null) {
                workOrderRowService.remove(new QueryWrapper<WorkOrderRow>()
                        .lambda()
                        .eq(WorkOrderRow::getWorkOrderNum, workOrderRow.getWorkOrderNum())
                        .eq(WorkOrderRow::getWorkOrderRowNum, workOrderRow.getWorkOrderRowNum())
                );
            }

            List<WorkOrderChild> list = workOrderChildList.stream().filter(workOrderChild -> workOrderChild.getWorkOrderNum().equals(workOrderRow.getWorkOrderNum()) && workOrderChild.getWorkOrderRowNum().equals(workOrderRow.getWorkOrderRowNum())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                Double min = list.stream().map(workOrderChild -> {
                    if (temporary.equals("0")) {
                        if (workOrderChild.getQuantityDelivery()==null) {
                            throw new ServiceException("子件发货数量不能空！");
                        }
                        if (workOrderChild.getFatherCount()==null) {
                            throw new ServiceException("子件父件基本数量不能空！");
                        }
                        if (workOrderChild.getChildrenCount()==null) {
                            throw new ServiceException("子件组件用量不能空！");
                        }
                    }
                    return workOrderChild.getQuantityDelivery() * workOrderChild.getFatherCount()/ workOrderChild.getChildrenCount();
                }).min(Comparator.comparing(Double::doubleValue)).get();
                workOrderRow.setQuantityReceive(min);
            }
            if (workOrderRow.getWorkOrderRowNum()==null) {
                throw new ServiceException("行项目工单行号不能为空！");
            }
            try {
                workOrderRowService.save(workOrderRow);
            }catch (Exception e){
                e.printStackTrace();
                throw new ServiceException("检查订单行项目数据是否正确");
            }

        });



        //工单测试
        List<WorkOrderTest> workOrderTests = workOrderVo.getWorkOrderTests();
        workOrderTests.stream().forEach(workOrderTest -> {
            if (StringUtils.isEmpty(workOrderTest.getWorkOrderNum())) {
                //throw new ServiceException("测试工单号不能为空");
                workOrderTest.setWorkOrderNum(workOrderHead.getWorkOrderNum());
            }
            WorkOrderTest orderTest = workOrderTestService.getOne(new QueryWrapper<WorkOrderTest>()
                    .lambda()
                    .eq(WorkOrderTest::getWorkOrderNum, workOrderTest.getWorkOrderNum())
                    .eq(WorkOrderTest::getWorkOrderRowNum, workOrderTest.getWorkOrderRowNum())
                    .eq(WorkOrderTest::getItemNum, workOrderTest.getItemNum())
            );
            if (orderTest!=null) {
                workOrderTestService.remove(new QueryWrapper<WorkOrderTest>()
                        .lambda()
                        .eq(WorkOrderTest::getWorkOrderNum, workOrderTest.getWorkOrderNum())
                        .eq(WorkOrderTest::getWorkOrderRowNum, workOrderTest.getWorkOrderRowNum())
                        .eq(WorkOrderTest::getItemNum, workOrderTest.getItemNum()));
            }
            if (workOrderTest.getWorkOrderRowNum()==null) {
                throw new ServiceException("测试程序工单行号不能为空！");
            }
            if (workOrderTest.getItemNum()==null) {
                throw new ServiceException("测试程序序号不能为空！");
            }
            try {
                workOrderTestService.save(workOrderTest);
            }catch (Exception e){
                e.printStackTrace();
                throw new ServiceException("检查订单测试程序数据是否正确");
            }
        });
        //将保存库存表
        //if (temporary.equals("0")) {
            //加减库存
        workOrderTestService.reduce_inventory(workOrderHead.getWorkOrderNum());
        //}

        return R.ok();
    }

    @ApiOperation(value = "获取订单号")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/get/order/num")
    public R getOrderNum(@RequestParam String workOrderType,@RequestParam(required = false) String workOrderNum) {
        try {
            return R.ok(workOrderHeadService.getWorkOrderNum(workOrderType,workOrderNum));
        }catch (Exception e){
            throw new ServiceException("给号系统异常，请稍后尝试");
        }
    }


    @ApiOperation(value = "工单单张界锁")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/get/lock")
    public R getLock(HttpServletRequest request,@RequestParam String workOrderNum) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String username = loginUser.getUsername();
        if (StringUtils.isEmpty(username)) {
            throw new ServiceException("未登录");
        }
        //String ipAddr = IpUtils.getIpAddr(request);
        return workOrderHeadService.setLock(username,workOrderNum);
    }

    @ApiOperation(value = "工单单张解锁")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/del/lock")
    public R delLock(HttpServletRequest request,@RequestParam String workOrderNum) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String username = loginUser.getUsername();
        if (StringUtils.isEmpty(username)) {
            throw new ServiceException("未登录");
        }
        return workOrderHeadService.delLock(username,workOrderNum);
    }
    @ApiOperation(value = "获取ip")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/get/ip")
    public R getIp(HttpServletRequest request) {
        String ipAddr = IpUtils.getIpAddr(request);
        return R.ok(ipAddr);
    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody WorkOrderQueryVo workOrderQueryVo,@RequestParam( required = false) String delFlag,@RequestParam( required = false) String temporary) throws IOException {
        Page<WorkOrderQueryShowVo> page = PageUtils.getPage(WorkOrderQueryShowVo.class);
       // Long pageSize = page.getSize();
        if (workOrderQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderQueryShowVo> dataPage= workOrderHeadService.queryWorkOrderData(page,workOrderQueryVo, delFlag, temporary);
        List<WorkOrderQueryShowVo> records = dataPage.getRecords();
        records.forEach(record->{
            List<PostCertificateRow> list = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getWorkOrderNum, record.getWorkOrderNum())
                    .eq(PostCertificateRow::getWorkOrderRowNum, record.getWorkOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("101","102"))
            );
            double sum = list.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(postQuantity -> postQuantity).sum();
            record.setReceived(sum);
        });
        if (workOrderQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(workOrderQueryVo.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response,records,WorkOrderQueryShowVo.class,"工单行数据");
    }


    @ApiOperation(value = "显示，变更")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/getWorkInfo")
    public R getWorkInfo(@RequestParam String workOrderNum) {
        WorkOrderHead head = workOrderHeadService.getOne(new QueryWrapper<WorkOrderHead>()
                .lambda()
                .eq(WorkOrderHead::getWorkOrderNum,workOrderNum));
        //获取所有的行
        List<WorkOrderRow> rows = workOrderRowService.list(new QueryWrapper<WorkOrderRow>()
                .lambda()
                .eq(WorkOrderRow::getWorkOrderNum,workOrderNum));
        List<WorkOrderChild> childList = workOrderChildService.list(new QueryWrapper<WorkOrderChild>()
                .lambda()
                .eq(WorkOrderChild::getWorkOrderNum,workOrderNum));
        List<WorkOrderTest> testList = workOrderTestService.list(new QueryWrapper<WorkOrderTest>()
                .lambda()
                .eq(WorkOrderTest::getWorkOrderNum,workOrderNum));
        WorkOrderVo workOrderVo =new WorkOrderVo();
        workOrderVo.setWorkOrderHead(head);
        workOrderVo.setWorkOrderRows(rows);
        workOrderVo.setWorkOrderChildList(childList);
        workOrderVo.setWorkOrderTests(testList);
        R r = provisionalSingleOutController.completeSelection(workOrderVo);
        return r;
    }


    @ApiOperation(value = "工单过账检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/check")
    public R check(@RequestBody WorkOrderPostCertificateVo workOrderPostCertificateVo) {
        List<Repertory> repertories = workOrderPostCertificateVo.getRepertories();
        for (Repertory repertory : repertories) {
            Repertory serviceOne = repertoryService.getOne(new QueryWrapper<Repertory>()
                    .lambda()
                    .eq(Repertory::getFactoryCode,repertory.getFactoryCode())
                    .eq(Repertory::getStockPCode,repertory.getStockPCode())
                    .eq(Repertory::getMaterialCode,repertory.getMaterialCode())
                    .eq(Repertory::getBatchNumber,repertory.getBatchNumber())
                    .eq(Repertory::getPiece,StringUtils.isNotEmpty(repertory.getPiece())?repertory.getPiece(): Constants.DATA_DEFAULT_VALUE)
                    .eq(Repertory::getBinNum,StringUtils.isNotEmpty(repertory.getBinNum())?repertory.getBinNum(): Constants.DATA_DEFAULT_VALUE)
            );
            if (serviceOne==null||serviceOne.getProcessStock()<repertory.getQuantityUnconsumed()) {
                throw new ServiceException("此工单子件项目加工锁定库存短缺，无法消耗，请检查！");
            }
        }

        return R.ok();
    }
    @ApiOperation(value = "工单过账")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/postCertificate")
    public R postCertificate(@RequestBody WorkOrderPostCertificateVo workOrderPostCertificateVo) {
        workOrderRowService.postCertificate(workOrderPostCertificateVo);
        return R.ok();
    }
}
