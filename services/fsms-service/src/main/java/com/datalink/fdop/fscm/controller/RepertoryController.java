package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.api.model.vo.RepertoryMoveQueryVo;
import com.datalink.fdop.fscm.api.model.vo.RepertoryVo;
import com.datalink.fdop.fscm.service.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/17 14:49
 */

@RestController
@RequestMapping("/repertory")
@Transactional
public class RepertoryController {


    @Autowired
    private RepertoryService repertoryService;
    @Autowired
    private MaterialBatchPropertyService batchPropertyService;
    @Autowired
    private MaterialBinPropertyService binPropertyService;
    @Autowired
    private MaterialPiecePropertyService piecePropertyService;
    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;
    @Autowired
    private ProvisionalSingleOutCacheService cacheService;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private WorkOrderExcelService workOrderExcelService;
    @Autowired
    private WorkOrderStoreIssueService workOrderStoreIssueService;


    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) RepertoryVo repertoryVo) {
        Map<String, Object> map = Maps.newHashMap();
        Set batchHeads = Sets.newHashSet();
        Set pieceHeads = Sets.newHashSet();
        Set binHeads = Sets.newHashSet();
        Page<Repertory> page = PageUtils.getPage(Repertory.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (repertoryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<Repertory> pageData = repertoryService.page(page, new QueryWrapper<Repertory>()
                .lambda()
                .in(CollectionUtils.isNotEmpty(repertoryVo.getFactoryCode()),Repertory::getFactoryCode,repertoryVo.getFactoryCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getStockPCode()),Repertory::getStockPCode,repertoryVo.getStockPCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getStockBPCode()),Repertory::getStockBPCode,repertoryVo.getStockBPCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getMaterialCode()),Repertory::getMaterialCode,repertoryVo.getMaterialCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getBatchNumber()),Repertory::getBatchNumber,repertoryVo.getBatchNumber())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getPiece()),Repertory::getPiece,repertoryVo.getPiece())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getBinNum()),Repertory::getBinNum,repertoryVo.getBinNum())
                .orderByAsc(Repertory::getFactoryCode)
                .orderByAsc(Repertory::getStockPCode)
                .orderByAsc(Repertory::getStockBPCode)
                .orderByAsc(Repertory::getMaterialCode)
                .orderByAsc(Repertory::getBatchNumber)
                .orderByAsc(Repertory::getPiece)
                .orderByAsc(Repertory::getBinNum)
        );
        List<Repertory> records = pageData.getRecords();
        if (repertoryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(repertoryVo.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(records)) {
            for (Repertory record : records) {
                record.setMaterialDesc(repertoryService.findDesc(record.getMaterialCode()));
                record.setStockPDescription(repertoryService.findSPDesc(record.getStockPCode()));
                record.setStockBPDescription(repertoryService.findSBPDesc(record.getStockBPCode()));
                //批次属性
                List<MaterialBatchProperty> list = batchPropertyService.list(new QueryWrapper<MaterialBatchProperty>()
                        .lambda()
                        .eq(MaterialBatchProperty::getMaterialCode, record.getMaterialCode())
                        .eq(StringUtils.isNotEmpty(record.getFactoryCode()),MaterialBatchProperty::getFactoryCode, record.getFactoryCode())
                        .eq(StringUtils.isNotEmpty(record.getBatchNumber()),MaterialBatchProperty::getBatchNumber, record.getBatchNumber())
                );
                list.forEach(data->{
                    batchHeads.add("批次属性-"+data.getAttributeDesc()+"-"+data.getSerialNum());
                    record.getBatchMap().put(data.getAttributeDesc(),data.getValue());
                });
                //片属性
                List<MaterialPieceProperty> pList = piecePropertyService.list(new QueryWrapper<MaterialPieceProperty>()
                        .lambda()
                        .eq(MaterialPieceProperty::getMaterialCode, record.getMaterialCode())
                        .eq(StringUtils.isNotEmpty(record.getFactoryCode()),MaterialPieceProperty::getFactoryCode, record.getFactoryCode())
                        .eq(StringUtils.isNotEmpty(record.getBatchNumber()),MaterialPieceProperty::getBatchNumber, record.getBatchNumber())
                        .eq(StringUtils.isNotEmpty(record.getPiece()),MaterialPieceProperty::getPiece, record.getPiece())
                );
                pList.forEach(data->{
                    pieceHeads.add("片属性-"+data.getAttributeDesc()+"-"+data.getSerialNum());
                    record.getPieceMap().put(data.getAttributeDesc(),data.getValue());
                });

                //ＢＩＮ属性
                List<MaterialBinProperty> BList = binPropertyService.list(new QueryWrapper<MaterialBinProperty>()
                        .lambda()
                        .eq(MaterialBinProperty::getMaterialCode, record.getMaterialCode())
                        .eq(StringUtils.isNotEmpty(record.getFactoryCode()),MaterialBinProperty::getFactoryCode, record.getFactoryCode())
                        .eq(StringUtils.isNotEmpty(record.getBatchNumber()),MaterialBinProperty::getBatchNumber, record.getBatchNumber())
                        .eq(StringUtils.isNotEmpty(record.getPiece()),MaterialBinProperty::getPiece, record.getPiece())
                        .eq(StringUtils.isNotEmpty(record.getBinNum()),MaterialBinProperty::getBinNum, record.getBinNum())
                );
                BList.forEach(data->{
                    binHeads.add("BIN属性-"+data.getAttributeDesc()+"-"+data.getSerialNum());
                    record.getBinMap().put(data.getAttributeDesc(),data.getValue());
                });
            }
        }
        map.put("batchHeads",batchHeads);
        map.put("pieceHeads",pieceHeads);
        map.put("binHeads",binHeads);
        map.put("data",PageUtils.getPageInfo(records,(int)pageData.getTotal()));
        return R.ok(map);
    }



    @ApiOperation(value = "更新")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/updata")
    public R updata(@RequestBody List<Repertory> repertory) {
        return R.ok(repertoryService.updata(repertory));
    }




    @ApiOperation(value = "查询减去临时挑片")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query/subtract")
    public R querySubtract(@RequestBody(required = false) RepertoryVo repertoryVo,
                           @RequestParam(required = false) String workOrderNum ,
                           @RequestParam(required = false) Long workOrderRowNum,
                           @RequestParam(required = false) Long workOrderChildrenNum,
                           @RequestParam boolean dimensionFlag ,
                           @RequestParam Long pageSize,
                           @RequestParam Long pageNo) {
        Page<Repertory> page = PageUtils.getPage(Repertory.class);
        if (pageSize!=null&& pageNo!=null) {
            page.setSize(pageSize);
            page.setCurrent(pageNo);
        }
        List<Repertory> datas = repertoryService.list(new QueryWrapper<Repertory>()
                .lambda()
                .in(CollectionUtils.isNotEmpty(repertoryVo.getFactoryCode()),Repertory::getFactoryCode,repertoryVo.getFactoryCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getStockPCode()),Repertory::getStockPCode,repertoryVo.getStockPCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getStockBPCode()),Repertory::getStockBPCode,repertoryVo.getStockBPCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getMaterialCode()),Repertory::getMaterialCode,repertoryVo.getMaterialCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getBatchNumber()),Repertory::getBatchNumber,repertoryVo.getBatchNumber())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getPiece()),Repertory::getPiece,repertoryVo.getPiece())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getBinNum()),Repertory::getBinNum,repertoryVo.getBinNum())
                .orderByAsc(Repertory::getStockPCode)
                .orderByAsc(Repertory::getBatchNumber)
                .orderByAsc(Repertory::getPiece)
                .orderByAsc(Repertory::getBinNum)
        );

        datas.stream().forEach(data -> {
            List<ProvisionalSingleOut> singleOuts = provisionalSingleOutService.list(new QueryWrapper<ProvisionalSingleOut>()
                    .lambda()
                    .eq(ProvisionalSingleOut::getMaterialCode, data.getMaterialCode())
                    .eq(ProvisionalSingleOut::getFactoryCode, data.getFactoryCode())
                    .eq(ProvisionalSingleOut::getStockPCode, data.getStockPCode())
                    .eq(ProvisionalSingleOut::getBatchNumber, data.getBatchNumber())
                    .eq(StringUtils.isNotEmpty(data.getPiece()),ProvisionalSingleOut::getPiece, data.getPiece())
                    .eq(StringUtils.isNotEmpty(data.getBinNum()),ProvisionalSingleOut::getBinNum, data.getBinNum())
                    .ne(StringUtils.isNotEmpty(workOrderNum),ProvisionalSingleOut::getWorkOrderNum, workOrderNum)
                    .ne(StringUtils.isNotEmpty(workOrderNum),ProvisionalSingleOut::getWorkOrderRowNum, workOrderRowNum)
                    .ne(StringUtils.isNotEmpty(workOrderNum),ProvisionalSingleOut::getWorkOrderChildrenNum, workOrderChildrenNum)
            );
            //临时缓存表挑片数量
            double cacheSum =0.0;
            if (StringUtils.isNotEmpty(workOrderNum)) {
                List<ProvisionalSingleOutCache> cacheList = cacheService.list(new QueryWrapper<ProvisionalSingleOutCache>()
                        .lambda()
                        .eq(ProvisionalSingleOutCache::getMaterialCode, data.getMaterialCode())
                        .eq(ProvisionalSingleOutCache::getFactoryCode, data.getFactoryCode())
                        .eq(ProvisionalSingleOutCache::getStockPCode, data.getStockPCode())
                        .eq(ProvisionalSingleOutCache::getBatchNumber, data.getBatchNumber())
                        .eq(ProvisionalSingleOutCache::getPiece,StringUtils.isNotEmpty(data.getPiece())?data.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(ProvisionalSingleOutCache::getBinNum,StringUtils.isNotEmpty( data.getBinNum())? data.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                );
                cacheSum=cacheList.stream().map(ProvisionalSingleOutCache::getQuantityDelivery).mapToDouble(m->m).sum();
            }

            double sum = singleOuts.stream().map(ProvisionalSingleOut::getQuantityDelivery).mapToDouble(m -> m).sum();
            data.setUnrestrictedStock(data.getUnrestrictedStock()-sum-cacheSum);
        });
        List<Repertory> data = datas.stream().filter(d -> d.getUnrestrictedStock() > 0.0).collect(Collectors.toList());
        double sum = data.stream().map(Repertory::getUnrestrictedStock).mapToDouble(m -> m).sum();
        Map<String, Object> map = Maps.newHashMap();
        data.forEach(repertory -> {
            repertory.setStockPDescription(templateService.findStockPDescription(repertory.getStockPCode()));
            repertory.setMaterialDesc(workOrderExcelService.findMaterialdesc(repertory.getMaterialCode()));
        });


        //纬度明细
        if (dimensionFlag) {
            Map<String, Optional<Repertory>> repertorieMap = data.stream().collect(Collectors
                    .groupingBy(r->r.getBatchNumber()+"-"+r.getMaterialCode()+"-"+r.getFactoryCode()+"-"+r.getStockPCode(), Collectors.reducing((r1, r2) -> {
                        double unrestrictedStock = r1.getUnrestrictedStock() + r2.getUnrestrictedStock();
                        double freezeStock = r1.getFreezeStock() + r2.getFreezeStock();
                        double processStock = r1.getProcessStock() + r2.getProcessStock();
                        double transferStock = r1.getTransferStock() + r2.getTransferStock();
                        double pickStock = r1.getPickStock() + r2.getPickStock();
                        double scrapStock = r1.getScrapStock() + r2.getScrapStock();
                        double shipStock = r1.getShipStock() + r2.getShipStock();
                        double initialNum= r1.getInitialNum()+r2.getInitialNum();
                        return new Repertory(unrestrictedStock, freezeStock, processStock, transferStock, pickStock, scrapStock, shipStock,initialNum);
                    })));
            data = data.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(r->r.getBatchNumber()+"-"+r.getMaterialCode()+"-"+r.getFactoryCode()+"-"+r.getStockPCode()))), ArrayList::new))
                    .stream()
                    .map(repertory -> {
                        final Optional<Repertory> optionalRepertory = repertorieMap.get(repertory.getBatchNumber()+"-"+repertory.getMaterialCode()+"-"+repertory.getFactoryCode()+"-"+repertory.getStockPCode());
                        final Repertory repertory1 = optionalRepertory.get();
                        repertory.setUnrestrictedStock(repertory1.getUnrestrictedStock());
                        repertory.setFreezeStock(repertory1.getFreezeStock());
                        repertory.setProcessStock(repertory1.getProcessStock());
                        repertory.setTransferStock(repertory1.getTransferStock());
                        repertory.setPickStock(repertory1.getPickStock());
                        repertory.setScrapStock(repertory1.getScrapStock());
                        repertory.setShipStock(repertory1.getShipStock());
                        repertory.setInitialNum(repertory1.getInitialNum());
                        repertory.setBinNum(null);
                        repertory.setPiece(null);
                        return repertory;
                    })
                    .collect(Collectors.toList());
        }
        List<Repertory> list = data.stream().skip((page.getCurrent() - 1) * page.getSize()).limit(page.getSize()).collect(Collectors.toList());
        for (Repertory repertory : list) {
            String basicUnit = repertory.getBasicUnit();
            if (StringUtils.isNotEmpty(basicUnit)&& basicUnit.equals("EA")) {
                repertory.setAssistUnit("PCS");
            }
            if (StringUtils.isNotEmpty(basicUnit)&& basicUnit.equals("PCS")) {
                repertory.setAssistUnit("EA");
            }
            MaterialPieceProperty pieceProperty = piecePropertyService.getOne(new QueryWrapper<MaterialPieceProperty>().lambda()
                    .eq(MaterialPieceProperty::getMaterialCode, repertory.getMaterialCode())
                    .eq(MaterialPieceProperty::getFactoryCode, repertory.getFactoryCode())
                    .eq(StringUtils.isNotEmpty(repertory.getBatchNumber()), MaterialPieceProperty::getBatchNumber, repertory.getBatchNumber())
                    .eq(StringUtils.isNotEmpty(repertory.getPiece()), MaterialPieceProperty::getPiece, repertory.getPiece())
                    .eq(MaterialPieceProperty::getAttributeName, "GROSSDIE")
                    .eq(MaterialPieceProperty::getSerialNum, "1")
            );
            if (pieceProperty!=null) {
                String value = pieceProperty.getValue();
                if (StringUtils.isNotEmpty(value)&& "PCS".equals(value)) {
                    repertory.setUnrestrictedStockUnit("EA");
                    repertory.setFreezeStockUnit("EA");
                    repertory.setProcessStockUnit("EA");
                    repertory.setTransferStockUnit("EA");
                    repertory.setPickStockUnit("EA");
                }
                if (StringUtils.isNotEmpty(value)&& "EA".equals(value)) {
                    repertory.setUnrestrictedStockUnit("PCS");
                    repertory.setFreezeStockUnit("PCS");
                    repertory.setProcessStockUnit("PCS");
                    repertory.setTransferStockUnit("PCS");
                    repertory.setPickStockUnit("PCS");
                }
            }

        }
        map.put("data",PageUtils.getPageInfo(list,data.size()));
        map.put("count",sum);
        return R.ok(map);
    }

    @ApiOperation(value = "变更查询加上发料减去临时挑片")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query/change")
    public R queryChange(@RequestBody(required = false) RepertoryVo repertoryVo,
                         @RequestParam String workOrderNum ,
                         @RequestParam Long workOrderRowNum,
                         @RequestParam Long workOrderChildrenNum,
                         @RequestParam boolean dimensionFlag ,
                         @RequestParam Long pageSize,
                         @RequestParam Long pageNo) {
        Page<Repertory> page = PageUtils.getPage(Repertory.class);
        if (pageSize!=null&& pageNo!=null) {
            page.setSize(pageSize);
            page.setCurrent(pageNo);
        }
        List<Repertory> datas = repertoryService.list(new QueryWrapper<Repertory>()
                .lambda()
                .in(CollectionUtils.isNotEmpty(repertoryVo.getFactoryCode()),Repertory::getFactoryCode,     repertoryVo.getFactoryCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getStockPCode()),Repertory::getStockPCode,       repertoryVo.getStockPCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getStockBPCode()),Repertory::getStockBPCode,     repertoryVo.getStockBPCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getMaterialCode()),Repertory::getMaterialCode,   repertoryVo.getMaterialCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getBatchNumber()),Repertory::getBatchNumber,     repertoryVo.getBatchNumber())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getPiece()),Repertory::getPiece,                 repertoryVo.getPiece())
                    .in(CollectionUtils.isNotEmpty(repertoryVo.getBinNum()),Repertory::getBinNum,           repertoryVo.getBinNum())
                .orderByAsc(Repertory::getStockPCode)
                .orderByAsc(Repertory::getBatchNumber)
                .orderByAsc(Repertory::getPiece)
                .orderByAsc(Repertory::getBinNum)
        );

        datas.stream().forEach(data -> {
            //发料表
            List<WorkOrderStoreIssue> storeIssues = workOrderStoreIssueService.list(
                    new QueryWrapper<WorkOrderStoreIssue>()
                            .lambda()
                            .eq(WorkOrderStoreIssue::getMaterialCode, data.getMaterialCode())
                            .eq(WorkOrderStoreIssue::getFactoryCode, data.getFactoryCode())
                            .eq(WorkOrderStoreIssue::getStockPCode, data.getStockPCode())
                            .eq(WorkOrderStoreIssue::getBatchNumber, data.getBatchNumber())
                            .eq(WorkOrderStoreIssue::getPiece, StringUtils.isNotEmpty(data.getPiece())?data.getPiece():Constants.DATA_DEFAULT_VALUE)
                            .eq(WorkOrderStoreIssue::getBinNum, StringUtils.isNotEmpty(data.getBinNum())?data.getBinNum():Constants.DATA_DEFAULT_VALUE)
                            .eq(WorkOrderStoreIssue::getWorkOrderNum, workOrderNum)
                            .eq(WorkOrderStoreIssue::getWorkOrderRowNum, workOrderRowNum)
                            .eq(WorkOrderStoreIssue::getWorkOrderChildrenNum, workOrderChildrenNum)
            );
            //临时挑片表
            List<ProvisionalSingleOut> singleOuts = provisionalSingleOutService.list(new QueryWrapper<ProvisionalSingleOut>()
                    .lambda()
                    .eq(ProvisionalSingleOut::getMaterialCode, data.getMaterialCode())
                    .eq(ProvisionalSingleOut::getFactoryCode, data.getFactoryCode())
                    .eq(ProvisionalSingleOut::getStockPCode, data.getStockPCode())
                    .eq(ProvisionalSingleOut::getBatchNumber, data.getBatchNumber())
                    .eq(StringUtils.isNotEmpty( data.getPiece()),ProvisionalSingleOut::getPiece, data.getPiece())
                    .eq(StringUtils.isNotEmpty(data.getBinNum()),ProvisionalSingleOut::getBinNum, data.getBinNum())
                    .ne(ProvisionalSingleOut::getWorkOrderNum, workOrderNum)
                    .ne(ProvisionalSingleOut::getWorkOrderRowNum, workOrderRowNum)
                    .ne(ProvisionalSingleOut::getWorkOrderChildrenNum, workOrderChildrenNum)
            );
            //临时缓存表挑片数量
            List<ProvisionalSingleOutCache> cacheList = cacheService.list(new QueryWrapper<ProvisionalSingleOutCache>()
                    .lambda()
                    .eq(ProvisionalSingleOutCache::getMaterialCode, data.getMaterialCode())
                    .eq(ProvisionalSingleOutCache::getFactoryCode, data.getFactoryCode())
                    .eq(ProvisionalSingleOutCache::getStockPCode, data.getStockPCode())
                    .eq(ProvisionalSingleOutCache::getBatchNumber, data.getBatchNumber())
                    .eq(ProvisionalSingleOutCache::getPiece,StringUtils.isNotEmpty( data.getPiece())? data.getPiece():Constants.DATA_DEFAULT_VALUE)
                    .eq(ProvisionalSingleOutCache::getBinNum, StringUtils.isNotEmpty(data.getBinNum())?data.getBinNum():Constants.DATA_DEFAULT_VALUE)
            );
            double cacheSum = cacheList.stream().map(ProvisionalSingleOutCache::getQuantityDelivery).mapToDouble(m->m).sum();
            double sum = singleOuts.stream().map(ProvisionalSingleOut::getQuantityDelivery).mapToDouble(m -> m).sum();
            double  storeIssuesSum= storeIssues.stream().map(WorkOrderStoreIssue::getQuantityDelivery).mapToDouble(m -> m).sum();
            data.setUnrestrictedStock(data.getUnrestrictedStock()+storeIssuesSum-sum-cacheSum);
        });
        List<Repertory> data = datas.stream().filter(d -> d.getUnrestrictedStock() > 0.0).collect(Collectors.toList());
        double sum = data.stream().map(Repertory::getUnrestrictedStock).mapToDouble(m -> m).sum();
        Map<String, Object> map = Maps.newHashMap();
        data.forEach(repertory -> {
            repertory.setStockPDescription(templateService.findStockPDescription(repertory.getStockPCode()));
            repertory.setMaterialDesc(workOrderExcelService.findMaterialdesc(repertory.getMaterialCode()));
        });
        if (repertoryVo.getSearchVo()!=null) {
            data = SearchUtils.getByEntityFilter(repertoryVo.getSearchVo(), data);
        }

        //纬度明细
        if (dimensionFlag) {
            Map<String, Optional<Repertory>> repertorieMap = data.stream().collect(Collectors
                    .groupingBy(r->r.getBatchNumber()+"-"+r.getMaterialCode()+"-"+r.getFactoryCode()+"-"+r.getStockPCode(), Collectors.reducing((r1, r2) -> {
                        double unrestrictedStock = r1.getUnrestrictedStock() + r2.getUnrestrictedStock();
                        double freezeStock = r1.getFreezeStock() + r2.getFreezeStock();
                        double processStock = r1.getProcessStock() + r2.getProcessStock();
                        double transferStock = r1.getTransferStock() + r2.getTransferStock();
                        double pickStock = r1.getPickStock() + r2.getPickStock();
                        double scrapStock = r1.getScrapStock() + r2.getScrapStock();
                        double shipStock = r1.getShipStock() + r2.getShipStock();
                        double initialNum= r1.getInitialNum()+r2.getInitialNum();
                        return new Repertory(unrestrictedStock, freezeStock, processStock, transferStock, pickStock, scrapStock, shipStock,initialNum);
                    })));
            data = data.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(r->r.getBatchNumber()+"-"+r.getMaterialCode()+"-"+r.getFactoryCode()+"-"+r.getStockPCode()))), ArrayList::new))
                    .stream()
                    .map(repertory -> {
                        final Optional<Repertory> optionalRepertory = repertorieMap.get(repertory.getBatchNumber()+"-"+repertory.getMaterialCode()+"-"+repertory.getFactoryCode()+"-"+repertory.getStockPCode());
                        final Repertory repertory1 = optionalRepertory.get();
                        repertory.setUnrestrictedStock(repertory1.getUnrestrictedStock());
                        repertory.setFreezeStock(repertory1.getFreezeStock());
                        repertory.setProcessStock(repertory1.getProcessStock());
                        repertory.setTransferStock(repertory1.getTransferStock());
                        repertory.setPickStock(repertory1.getPickStock());
                        repertory.setScrapStock(repertory1.getScrapStock());
                        repertory.setShipStock(repertory1.getShipStock());
                        repertory.setInitialNum(repertory1.getInitialNum());
                        repertory.setBinNum(null);
                        repertory.setPiece(null);
                        return repertory;
                    })
                    .collect(Collectors.toList());
        }

        List<Repertory> list = data.stream().skip((page.getCurrent() - 1) * page.getSize()).limit(page.getSize()).collect(Collectors.toList());
        for (Repertory repertory : list) {
            String basicUnit = repertory.getBasicUnit();
            if (StringUtils.isNotEmpty(basicUnit)&& basicUnit.equals("EA")) {
                repertory.setAssistUnit("PCS");
            }
            if (StringUtils.isNotEmpty(basicUnit)&& basicUnit.equals("PCS")) {
                repertory.setAssistUnit("EA");
            }
            MaterialPieceProperty pieceProperty = piecePropertyService.getOne(new QueryWrapper<MaterialPieceProperty>().lambda()
                    .eq(MaterialPieceProperty::getMaterialCode, repertory.getMaterialCode())
                    .eq(MaterialPieceProperty::getFactoryCode, repertory.getFactoryCode())
                    .eq(StringUtils.isNotEmpty(repertory.getBatchNumber()), MaterialPieceProperty::getBatchNumber, repertory.getBatchNumber())
                    .eq(StringUtils.isNotEmpty(repertory.getPiece()), MaterialPieceProperty::getPiece, repertory.getPiece())
                    .eq(MaterialPieceProperty::getAttributeName, "GROSSDIE")
                    .eq(MaterialPieceProperty::getSerialNum, "1")
            );
            if (pieceProperty!=null) {
                String value = pieceProperty.getValue();
                if (StringUtils.isNotEmpty(value)&& "PCS".equals(value)) {
                    repertory.setUnrestrictedStockUnit("EA");
                    repertory.setFreezeStockUnit("EA");
                    repertory.setProcessStockUnit("EA");
                    repertory.setTransferStockUnit("EA");
                    repertory.setPickStockUnit("EA");
                }
                if (StringUtils.isNotEmpty(value)&& "EA".equals(value)) {
                    repertory.setUnrestrictedStockUnit("PCS");
                    repertory.setFreezeStockUnit("PCS");
                    repertory.setProcessStockUnit("PCS");
                    repertory.setTransferStockUnit("PCS");
                    repertory.setPickStockUnit("PCS");
                }
            }

        }
        map.put("data",PageUtils.getPageInfo(list,data.size()));
        map.put("count",sum);
        return R.ok(map);
    }


    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody(required = false) RepertoryVo repertoryVo) throws IOException {

        Page<Repertory> page = PageUtils.getPage(Repertory.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (repertoryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<Repertory> pageData = repertoryService.page(page, new QueryWrapper<Repertory>()
                .lambda()
                .in(CollectionUtils.isNotEmpty(repertoryVo.getFactoryCode()),Repertory::getFactoryCode,repertoryVo.getFactoryCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getStockPCode()),Repertory::getStockPCode,repertoryVo.getStockPCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getStockBPCode()),Repertory::getStockBPCode,repertoryVo.getStockBPCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getMaterialCode()),Repertory::getMaterialCode,repertoryVo.getMaterialCode())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getBatchNumber()),Repertory::getBatchNumber,repertoryVo.getBatchNumber())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getPiece()),Repertory::getPiece,repertoryVo.getPiece())
                .in(CollectionUtils.isNotEmpty(repertoryVo.getBinNum()),Repertory::getBinNum,repertoryVo.getBinNum())
                .orderByAsc(Repertory::getStockPCode)
                .orderByAsc(Repertory::getBatchNumber)
                .orderByAsc(Repertory::getPiece)
                .orderByAsc(Repertory::getBinNum)
        );
        List<Repertory> records = pageData.getRecords();
        for (Repertory record : records) {
            record.setMaterialDesc(repertoryService.findDesc(record.getMaterialCode()));
            record.setStockPDescription(repertoryService.findSPDesc(record.getStockPCode()));
            record.setStockBPDescription(repertoryService.findSBPDesc(record.getStockBPCode()));
        }
        if (repertoryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(repertoryVo.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        ExcelUtils.export3Excel(response,records, Repertory.class,"实时库存");
    }

    @ApiOperation(value = "库存移动查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query/move")
    public R queryMove(@RequestBody(required = false) RepertoryMoveQueryVo repertoryMoveQueryVo) {
        Page<RepertoryMove> page = PageUtils.getPage(RepertoryMove.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (repertoryMoveQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<RepertoryMove> pageData = repertoryService.queryMove(page,repertoryMoveQueryVo);
        List<RepertoryMove> records = pageData.getRecords();
        if (repertoryMoveQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(repertoryMoveQueryVo.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records,(int) pageData.getTotal()));
    }


    @ApiOperation(value = "库存移动导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query/move/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) RepertoryMoveQueryVo repertoryMoveQueryVo) throws IOException {
        Page<RepertoryMove> page = PageUtils.getPage(RepertoryMove.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (repertoryMoveQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<RepertoryMove> pageData = repertoryService.queryMove(page,repertoryMoveQueryVo);
        List<RepertoryMove> records = pageData.getRecords();
        if (repertoryMoveQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(repertoryMoveQueryVo.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        ExcelUtils.export3Excel(response,records, RepertoryMove.class,"库存移动");
    }


}
