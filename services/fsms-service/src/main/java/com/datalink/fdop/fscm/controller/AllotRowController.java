package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.AllotRow;
import com.datalink.fdop.fscm.service.AllotRowService;
import com.datalink.fdop.fscm.service.TemplateService;
import com.datalink.fdop.fscm.service.WorkOrderExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/13 14:00
 */
@RestController
@RequestMapping("/allot/row")
@Transactional
@Api(tags = "调拨行")
public class AllotRowController {


    @Autowired
    private AllotRowService allotRowService;
    @Autowired
    private WorkOrderExcelService workOrderExcelService;
    @Autowired
    private TemplateService templateService;


    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R query(@RequestBody List<AllotRow> allotRows) {
        for (AllotRow allotRow : allotRows) {
            allotRowService.remove(new QueryWrapper<AllotRow>()
                    .lambda()
                    .eq(AllotRow::getTransferOrderNum ,allotRow.getTransferOrderNum())
                    .eq(AllotRow::getTransferOrderRowNum ,allotRow.getTransferOrderRowNum())
            );
        }
        return R.ok();
    }

    @ApiOperation(value = "根据调拨单号获取行")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/getRow")
    public R getRow(@RequestParam  String transferOrderNum) {
        List<AllotRow> list = allotRowService.list(new QueryWrapper<AllotRow>().lambda()
                .eq(AllotRow::getTransferOrderNum, transferOrderNum));
        for (AllotRow allotRow : list) {
            allotRow.setMaterialdesc(workOrderExcelService.findMaterialdesc(allotRow.getMaterialCode()));
            allotRow.setStockPDescriptionShip(templateService.findStockPDescription(allotRow.getStockPCodeShip()));
            allotRow.setStockPDescriptionReceive(templateService.findStockPDescription(allotRow.getStockPCodeReceive()));
        }
        return R.ok(list);
    }
}
