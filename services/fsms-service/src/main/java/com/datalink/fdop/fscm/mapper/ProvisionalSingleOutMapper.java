package com.datalink.fdop.fscm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.fscm.api.domain.ProvisionalSingleOut;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:32
 */
@Transactional
public interface ProvisionalSingleOutMapper extends BaseMapper<ProvisionalSingleOut> {



    @Select("SELECT\n" +
            "\twork_order_num,\n" +
            "\twork_order_row_num,\n" +
            "\twork_order_children_num,\n" +
            "\tmaterial_code,\n" +
            "\tstock_p_code,\n" +
            "\tbatch_number,\n" +
            "\t\"sum\" ( quantity_delivery ) quantity_delivery \n" +
            "FROM\n" +
            "\t zjdata.f_d_provisional_single_out b \n" +
            "WHERE\n" +
            "\twork_order_num=#{workOrderNum} \n" +
            "\tAND work_order_row_num=#{workOrderRowNum} \n" +
            " and work_order_children_num =#{workOrderChildrenNum} "+
            "GROUP BY\n" +
            "\twork_order_num,\n" +
            "\twork_order_row_num,\n" +
            "\twork_order_children_num,\n" +
            "\tmaterial_code,\n" +
            "\tstock_p_code,\n" +
            "\tbatch_number \n" +
            "ORDER BY\n" +
            "\tquantity_delivery")
    List<ProvisionalSingleOut> getProvisionalSingleOutByRowGroupBy(@Param("workOrderNum") String workOrderNum, @Param("workOrderRowNum") Long workOrderRowNum,@Param("workOrderChildrenNum") Long workOrderChildrenNum);



    @Select("SELECT\n" +
            "\twork_order_num,\n" +
            "\twork_order_row_num,\n" +
            "\twork_order_children_num,\n" +
            "\tmaterial_code,\n" +
            "\tstock_p_code,\n" +
            "\tbatch_number,\n" +
            "\t\"sum\" ( quantity_delivery ) quantity_delivery \n" +
            "FROM\n" +
            "\t zjdata.f_d_provisional_single_out b \n" +
            "WHERE\n" +
            "\twork_order_num=#{workOrderNum} \n" +
            "\tAND work_order_row_num=#{workOrderRowNum} \n" +
            "GROUP BY\n" +
            "\twork_order_num,\n" +
            "\twork_order_row_num,\n" +
            "\twork_order_children_num,\n" +
            "\tmaterial_code,\n" +
            "\tstock_p_code,\n" +
            "\tbatch_number \n" +
            "ORDER BY\n" +
            "\tquantity_delivery")
    List<ProvisionalSingleOut> getSingleOutByRowGroupBy(@Param("workOrderNum") String workOrderNum, @Param("workOrderRowNum")Long workOrderRowNum);
}
