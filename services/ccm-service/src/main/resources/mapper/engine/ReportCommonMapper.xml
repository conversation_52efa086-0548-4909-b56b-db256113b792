<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.ReportCommonMapper">

    <select id="listCostCenterType" resultType="java.lang.String">
        select cost_center_type from ods.ods_ccms_ccm_cost_center
        where cost_center_type is not null
        <if test="companyId != null and companyId != ''">
            and company_id in
            <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="yearMonthFrom != null and yearMonthFrom != ''">
            and DATE_FORMAT(date_from, '%Y%m') &lt;= #{yearMonthFrom}
        </if>
        <if test="yearMonthTo != null and yearMonthTo != ''">
            and DATE_FORMAT(date_to, '%Y%m') &gt;= #{yearMonthTo}
        </if>
        group by cost_center_type
        order by cost_center_type
    </select>

    <select id="listCostCenterId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select cost_center_id as "key", cost_center_desc as "value"
        from ods.ods_ccms_ccm_cost_center
        where cost_center_id is not null
        <if test="companyId != null and companyId != ''">
            and company_id in
            <foreach item="item" collection="companyId.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="yearMonthFrom != null and yearMonthFrom != ''">
            and DATE_FORMAT(date_from, '%Y%m') &lt;= #{yearMonthFrom}
        </if>
        <if test="yearMonthTo != null and yearMonthTo != ''">
            and DATE_FORMAT(date_to, '%Y%m') &gt;= #{yearMonthTo}
        </if>
        group by cost_center_id, cost_center_desc
        order by cost_center_id
    </select>

    <select id="listCostElementId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select cost_element_id as "key", cost_element_desc as "value"
        from ods.ods_ccms_ccm_cost_element
        where cost_element_id is not null
        group by cost_element_id, cost_element_desc
        order by cost_element_id
    </select>

    <select id="listCostStructureId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select cost_structure_id as "key", cost_structure_desc as "value"
        from ods.ods_ccms_ccm_cost_structure_head
        where cost_structure_id is not null
        group by cost_structure_id, cost_structure_desc
        order by cost_structure_id
    </select>

    <select id="listWorkArea" resultType="java.lang.String">
        select work_area
        from ods.ods_ccms_ccm_capacity
        where work_area is not null
        group by work_area
        order by work_area
    </select>

    <select id="listEquipGroupId" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        select equip_group_id as "key", equip_group_desc as "value"
        from ods.ods_ccms_ccm_capacity
        where equip_group_id is not null
        group by equip_group_id, equip_group_desc
        order by equip_group_id
    </select>

    <select id="listLotCategory" resultType="java.lang.String">
        select lot_category
        from ods.ods_mes_m011_lot_code
        where lot_category is not null
        group by lot_category
        order by lot_category
    </select>

    <select id="listLotType" resultType="java.lang.String">
        select lot_type
        from ods.ods_mes_m011_lot_code
        where lot_type is not null
        group by lot_type
        order by lot_type
    </select>
</mapper>

