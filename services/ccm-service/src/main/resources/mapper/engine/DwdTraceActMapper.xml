<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdTraceActMapper">

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.DwdTraceAct">
        SELECT *
        FROM dwd.dwd_trace_act node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" dateFrom != null and dateFrom != '' and dateTo != null and dateTo != ''">
                and node.date between #{dateFrom} and #{dateTo}
            </if>
        </where>
        ORDER BY node.plant_id , node.factory_id , node.date ,
        node.timestamp ,node.base_lot_id ,node.lot_id ,
        node.history_step_no ,node.step_no ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.DwdTraceAct">
        SELECT *
        FROM dwd.dwd_trace_act node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id = #{verId}
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productId != null and productId != ''">
                and node.product_id in
                <foreach item="item" collection="productId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" dateFrom != null and dateFrom != '' and dateTo != null and dateTo != ''">
                and node.date between #{dateFrom} and #{dateTo}
            </if>
        </where>
        ORDER BY node.plant_id , node.factory_id , node.date ,
        node.timestamp ,node.base_lot_id ,node.lot_id ,
        node.history_step_no ,node.step_no ${sort}
    </select>

</mapper>

