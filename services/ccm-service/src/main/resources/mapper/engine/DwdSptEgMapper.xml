<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdSptEgMapper">

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.DwdSptEg">
        SELECT *
        FROM dwd.dwd_spt_eg node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId !=''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="equipGroupId != null and equipGroupId != ''">
                and node.equip_group_id in
                <foreach item="item" collection="equipGroupId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.plant_id, node.equip_group_id
                 ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.DwdSptEg">
        SELECT *
        FROM dwd.dwd_spt_eg node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId !=''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="equipGroupId != null and equipGroupId != ''">
                and node.equip_group_id in
                <foreach item="item" collection="equipGroupId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.plant_id, node.equip_group_id
        ${sort}
    </select>

</mapper>

