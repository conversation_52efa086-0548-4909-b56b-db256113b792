<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwdRbomMapper">

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.DwdRbom">
        SELECT *,node.class AS class_type
        FROM dwd.dwd_rbom node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="recipeId != null and recipeId != ''">
                and node.recipe_id in
                <foreach item="item" collection="recipeId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.plant_id, node.recipe_id,
        node.hierarchy,node.hierarchy_inner_seq,
        node.raw_material_id
        ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.DwdRbom">
        SELECT *,node.class AS class_type
        FROM dwd.dwd_rbom node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="plantId != null and plantId != ''">
                and node.plant_id in
                <foreach item="item" collection="plantId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="recipeId != null and recipeId != ''">
                and node.recipe_id in
                <foreach item="item" collection="recipeId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY node.plant_id, node.recipe_id,
        node.hierarchy,node.hierarchy_inner_seq,
        node.raw_material_id
        ${sort}
    </select>

    <select id="listAll" resultType="com.datalink.fdop.base.api.domain.DwdRbom">
        SELECT *, node.class AS class_type
        FROM dwd.dwd_rbom node
        ORDER BY node.plant_id, node.recipe_id,
                 node.hierarchy, node.hierarchy_inner_seq,
                 node.raw_material_id
    </select>


</mapper>

