package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.base.service.CostStructureHeadService;
import com.datalink.fdop.base.service.FactoryService;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhFinalRecoverWip;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhFinalRecoverWipService;
import com.datalink.fdop.engine.utils.DynamicExcelExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwhFinalRecoverWip")
@Api(tags = "定版视图-计算在制成本")
public class DwhFinalRecoverWipController extends BaseController {

    @Autowired
    private DwhFinalRecoverWipService dwhFinalRecoverWipService;

    @Autowired
    private CostStructureHeadService costStructureHeadService;

    @Autowired
    private FactoryService factoryService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "定版视图-计算在制成本")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params , "plantId");
        String date = MapUtils.getAsString(params , "date");
        PageDataInfo<DwhFinalRecoverWip> overview = dwhFinalRecoverWipService.overview(verId,plantId, date, sort, queryParam.getSearchVo());
        String controlAreaId = factoryService.getControlAreaIdByPlantId(plantId);
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicColumnList(controlAreaId);
        return R.ok(MetaUtils.getMetadata(DwhFinalRecoverWip.class, overview, dynamicColumns));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "定版视图-计算在制成本", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params , "plantId");
        String date = MapUtils.getAsString(params , "date");
        List<DwhFinalRecoverWip> list = dwhFinalRecoverWipService.selectNoPage(verId,plantId, date, sort, queryParam.getSearchVo());
        String controlAreaId = factoryService.getControlAreaIdByPlantId(plantId);
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicColumnList(controlAreaId);
        DynamicExcelExportUtil<DwhFinalRecoverWip> excelUtil = new DynamicExcelExportUtil<>(DwhFinalRecoverWip.class);
        excelUtil.exportExcelWithDynamicColumns(response, list, dynamicColumns, "在制成本管理-定版视图数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalRecoverWip> util = new ExcelUtil<>(DwhFinalRecoverWip.class);
        util.importTemplateExcel(response, "在制成本管理-定版视图模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_final_recover_wip", null));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwh.dwh_final_recover_wip", null));
    }

}