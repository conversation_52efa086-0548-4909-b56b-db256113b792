package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.SearchVoUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhTerpAllocationCostA;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhTerpAllocationCostAService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwhTerpAllocationCostA")
@Api(tags = "交互视图-分摊管理")
public class DwhTerpAllocationCostAController extends BaseController {

    @Autowired
    private DwhTerpAllocationCostAService dwhTerpAllocationCostAService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "交互视图-分摊管理")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");

        PageDataInfo<DwhTerpAllocationCostA> overview = dwhTerpAllocationCostAService.overview(verId, controlAreaId, yearMonthFrom, yearMonthTo, allocationType, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhTerpAllocationCostA.class, overview));
    }

    @ApiOperation(value = "导出定向分摊数据")
    @Log(title = "交互视图-分摊管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void exportData(HttpServletResponse response,
                           @RequestParam(required = false, defaultValue = "ASC") String sort,
                           @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        List<DwhTerpAllocationCostA> list = dwhTerpAllocationCostAService.selectNoPage(verId,controlAreaId,yearMonthFrom,
                yearMonthTo,allocationType,sort, queryParam.getSearchVo());
        ExcelUtil<DwhTerpAllocationCostA> util = new ExcelUtil<>(DwhTerpAllocationCostA.class);
        util.exportExcel(response, list, "交互视图-定向分摊管理数据");
    }

    @ApiOperation(value = "导出定向分摊数据")
    @Log(title = "交互视图-分摊管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTargetedNewAllocationData")
    public void exportTargetedNewAllocationData(HttpServletResponse response) {
        List<DwhTerpAllocationCostA> list = dwhTerpAllocationCostAService.lambdaQuery().in(DwhTerpAllocationCostA::getAllocationType, "assign_eid", "assign_pid").list();
        ExcelUtil<DwhTerpAllocationCostA> util = new ExcelUtil<>(DwhTerpAllocationCostA.class);
        util.exportExcel(response, list, "交互视图-定向分摊管理数据");
    }

    @ApiOperation(value = "导出费用分摊数据")
    @Log(title = "交互视图-分摊管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCostNewAllocationData")
    public void exportCostNewAllocationData(HttpServletResponse response) {
        List<DwhTerpAllocationCostA> list = dwhTerpAllocationCostAService.lambdaQuery().eq(DwhTerpAllocationCostA::getAllocationType, "assign_non").list();
        ExcelUtil<DwhTerpAllocationCostA> util = new ExcelUtil<>(DwhTerpAllocationCostA.class);
        util.exportExcel(response, list, "交互视图-定向分摊管理数据");
    }

    @ApiOperation(value = "管理范围（SelectVo）")
    @GetMapping("/manageScope")
    public R<List<SelectVo>> manageScope(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listManageScope("dwh.dwh_terp_allocation_cost_a", searchVo));
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_terp_allocation_cost_a", searchVo));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listCompanyId("dwh.dwh_terp_allocation_cost_a", searchVo));
    }

}