package com.datalink.fdop;

import com.datalink.fdop.base.api.RemoteCostStructureHeadService;
import com.datalink.fdop.common.security.annotation.EnableCustomConfig;
import com.datalink.fdop.common.security.annotation.EnableRyFeignClients;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteTaskService;
import com.datalink.fdop.param.api.RemoteParamService;
import org.apache.dolphinscheduler.fdop.api.RemoteProjectService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@EnableCustomConfig
@EnableRyFeignClients(basePackageClasses = {
        RemoteTaskService.class,
        RemoteProjectService.class,
        RemoteDriveService.class,
        RemoteParamService.class,
        RemoteCostStructureHeadService.class
})
@SpringBootApplication
public class CcmApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(CcmApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  CCM服务启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "  _____ ______ __  __ _____ \n" +
                " / ____|  ____|  \\/  |_   _|\n" +
                "| (___ | |__  | \\  / | | |  \n" +
                " \\___ \\|  __| | |\\/| | | |  \n" +
                " ____) | |____| |  | |_| |_ \n" +
                "|_____/|______|_|  |_|_____|\n");
    }
}
