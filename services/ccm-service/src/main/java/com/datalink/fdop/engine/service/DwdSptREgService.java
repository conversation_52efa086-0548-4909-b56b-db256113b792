package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.DwdSptREg;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdSptREgService extends IService<DwdSptREg> {

    PageDataInfo<DwdSptREg> overview(String plantId, String equipGroupId, String sort, SearchVo searchVo);

    List<DwdSptREg> selectNoPage(String plantId, String equipGroupId, String sort, SearchVo searchVo);
}
