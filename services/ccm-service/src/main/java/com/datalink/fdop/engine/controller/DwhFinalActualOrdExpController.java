package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhFinalActualOrdExp;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhFinalActualOrdExpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwhFinalActualOrdExp")
@Api(tags = "定版视图-重估账记-工单成本部分")
public class DwhFinalActualOrdExpController extends BaseController {

    @Autowired
    private DwhFinalActualOrdExpService dwhFinalActualOrdExpService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "定版视图-重估账记-工单成本部分")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String yearMonth = MapUtils.getAsString(params, "yearMonth");
        String verId = MapUtils.getAsString(params, "verId");
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        PageDataInfo<DwhFinalActualOrdExp> overview = dwhFinalActualOrdExpService.overview(yearMonth, verId, controlAreaId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhFinalActualOrdExp.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "定版视图-重估账记-工单成本部分", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String yearMonth = MapUtils.getAsString(params, "yearMonth");
        String verId = MapUtils.getAsString(params, "verId");
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        List<DwhFinalActualOrdExp> list = dwhFinalActualOrdExpService.selectNoPage(yearMonth, verId, controlAreaId, sort, queryParam.getSearchVo());
        ExcelUtil<DwhFinalActualOrdExp> util = new ExcelUtil<>(DwhFinalActualOrdExp.class);
        util.exportExcel(response, list, "定版视图-重估账记-工单成本部分数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalActualOrdExp> util = new ExcelUtil<>(DwhFinalActualOrdExp.class);
        util.importTemplateExcel(response, "定版视图-重估账记-工单成本部分模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_final_actual_ord_exp", null));
    }

    @ApiOperation(value = "管理范围（SelectVo）")
    @GetMapping("/listControlAreaId")
    public R<List<SelectVo>> listControlAreaId() {
        return R.ok(dataDictionaryService.listManageScope("dwh.dwh_final_actual_ord_exp", null));
    }
}