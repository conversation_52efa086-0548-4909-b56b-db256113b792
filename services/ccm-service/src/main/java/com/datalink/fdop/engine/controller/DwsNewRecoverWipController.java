package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.base.service.CostStructureHeadService;
import com.datalink.fdop.base.service.FactoryService;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwsNewRecoverWip;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwsNewRecoverWipService;
import com.datalink.fdop.engine.utils.DynamicExcelExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/base/dwsNewRecoverWip")
@Api(tags = "逻辑视图-计算在制成本")
public class DwsNewRecoverWipController extends BaseController {

    @Autowired
    private DwsNewRecoverWipService dwsNewRecoverWipService;

    @Autowired
    private CostStructureHeadService costStructureHeadService;

    @Autowired
    private FactoryService factoryService;

    @Autowired
    private DataDictionaryService dataDictionaryService;


    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "逻辑视图-计算在制成本")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        String date = MapUtils.getAsString(params, "date");
        PageDataInfo<DwsNewRecoverWip> overview = dwsNewRecoverWipService.overview(verId,plantId, date, sort, queryParam.getSearchVo());
        String controlAreaId = factoryService.getControlAreaIdByPlantId(plantId);
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicColumnList(controlAreaId);
        return R.ok(MetaUtils.getMetadata(DwsNewRecoverWip.class, overview, dynamicColumns));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "逻辑视图-计算在制成本", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        String date = MapUtils.getAsString(params, "date");
        List<DwsNewRecoverWip> list = dwsNewRecoverWipService.selectNoPage(verId, plantId, date, sort, queryParam.getSearchVo());
        String controlAreaId = factoryService.getControlAreaIdByPlantId(plantId);
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicColumnList(controlAreaId);
        DynamicExcelExportUtil<DwsNewRecoverWip> excelUtil = new DynamicExcelExportUtil<>(DwsNewRecoverWip.class);
        excelUtil.exportExcelWithDynamicColumns(response, list, dynamicColumns, "逻辑视图-在制成本数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsNewRecoverWip> util = new ExcelUtil<>(DwsNewRecoverWip.class);
        util.importTemplateExcel(response, "逻辑视图-在制成本模板");
    }

    @ApiOperation(value = "工厂代码")
    @GetMapping("/plantId")
    public R<List<String>> plantId() {
        return R.ok(dwsNewRecoverWipService.lambdaQuery()
                .select(DwsNewRecoverWip::getPlantId)
                .groupBy(DwsNewRecoverWip::getPlantId).list()
                .stream().filter(dwsNewRecoverWip ->dwsNewRecoverWip !=null && dwsNewRecoverWip.getPlantId() != null)
                .map(DwsNewRecoverWip::getPlantId).collect(Collectors.toList()));
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dws.dws_new_recover_wip", null));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dws.dws_new_recover_wip", null));
    }

}