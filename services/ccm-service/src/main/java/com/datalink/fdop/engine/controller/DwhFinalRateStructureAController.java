package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhFinalRateStructureA;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhFinalRateStructureAService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/base/dwhFinalRateStructureA")
@Api(tags = "实际成本引擎-定版视图-组件费率")
public class DwhFinalRateStructureAController extends BaseController {

    @Autowired
    private DwhFinalRateStructureAService dwhFinalRateStructureAService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "queryParam", value = "查询参数", required = false, dataTypeClass = QueryParam.class, paramType = "body"),
    })
    @Log(title = "定版视图-组件费率")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String costCenterType = MapUtils.getAsString(params, "costCenterType");

        PageDataInfo<DwhFinalRateStructureA> overview = dwhFinalRateStructureAService.overview(
                controlAreaId, verId, yearMonthFrom, yearMonthTo, costCenterType, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhFinalRateStructureA.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "定版视图-组件费率", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String costCenterType = MapUtils.getAsString(params, "costCenterType");
        List<DwhFinalRateStructureA> list = dwhFinalRateStructureAService.selectNoPage(controlAreaId, verId, yearMonthFrom, yearMonthTo, costCenterType,sort, queryParam.getSearchVo());
        ExcelUtil<DwhFinalRateStructureA> util = new ExcelUtil<>(DwhFinalRateStructureA.class);
        util.exportExcel(response, list, "定版视图-组件费率数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalRateStructureA> util = new ExcelUtil<>(DwhFinalRateStructureA.class);
        util.importTemplateExcel(response, "定版视图-组件费率模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_final_rate_structure_a", null));
    }

    @ApiOperation(value = "管理范围（SelectVo）")
    @GetMapping("/manageScope")
    public R<List<SelectVo>> manageScope() {
        return R.ok(dataDictionaryService.listManageScope("dwh.dwh_final_rate_structure_a", null));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId() {
        return R.ok(dataDictionaryService.listCompanyId("dwh.dwh_final_rate_structure_a", null));
    }

    @ApiOperation(value = "成本中心类型（下拉框）")
    @GetMapping("/costCenterType")
    public R<List<String>> costCenterType() {
        return R.ok(dwhFinalRateStructureAService.lambdaQuery()
                .select(DwhFinalRateStructureA::getCostCenterType)
                .groupBy(DwhFinalRateStructureA::getCostCenterType)
                .orderByAsc(DwhFinalRateStructureA::getCostCenterType).list().stream()
                .filter(dwhFinalRateStructureA -> dwhFinalRateStructureA != null && dwhFinalRateStructureA.getCostCenterType() != null)
                .map(DwhFinalRateStructureA::getCostCenterType).collect(Collectors.toList()));
    }

}