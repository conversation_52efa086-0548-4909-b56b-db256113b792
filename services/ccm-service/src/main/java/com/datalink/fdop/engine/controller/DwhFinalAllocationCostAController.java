package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.SearchVoUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhFinalAllocationCostA;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhFinalAllocationCostAService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwhFinalAllocationCostA")
@Api(tags = "实际成本引擎-定向分摊-定版视图")
public class DwhFinalAllocationCostAController extends BaseController {

    @Autowired
    private DwhFinalAllocationCostAService dwhFinalAllocationCostAService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("查询定向分摊-定版视图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "查询定向分摊-定版视图")
    @PostMapping(value = "/selectTargetedFinalAllocation")
    public Object selectTargetedFinalAllocation(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        PageDataInfo<DwhFinalAllocationCostA> overview = dwhFinalAllocationCostAService.selectTargetedFinalAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhFinalAllocationCostA.class, overview));
    }

    @ApiOperation("查询费用分摊-定版视图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "查询费用分摊-定版视图")
    @PostMapping(value = "/selectCostFinalAllocation")
    public Object selectCostFinalAllocation(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        PageDataInfo<DwhFinalAllocationCostA> overview = dwhFinalAllocationCostAService.selectCostFinalAllocation(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, allocationType, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhFinalAllocationCostA.class, overview));
    }

    @ApiOperation(value = "导出定向分摊-定版视图数据")
    @Log(title = "导出定向分摊-定版视图数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void exportData(HttpServletResponse response, @RequestParam(required = false, defaultValue = "ASC") String sort,
                           @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        List<DwhFinalAllocationCostA> list = dwhFinalAllocationCostAService.selectNoPage(controlAreaId, verId, companyId,
                yearMonthFrom, yearMonthTo, allocationType, sort, queryParam.getSearchVo());
        ExcelUtil<DwhFinalAllocationCostA> util = new ExcelUtil<>(DwhFinalAllocationCostA.class);
        util.exportExcel(response, list, "最终分摊成本表数据");
    }

    @ApiOperation(value = "导出定向分摊-定版视图数据")
    @Log(title = "导出定向分摊-定版视图数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTargetedFinalAllocationData")
    public void exportTargetedFinalAllocationData(HttpServletResponse response) {
        List<DwhFinalAllocationCostA> list = dwhFinalAllocationCostAService.lambdaQuery().in(DwhFinalAllocationCostA::getAllocationType, "assign_eid", "assign_pid").list();
        ExcelUtil<DwhFinalAllocationCostA> util = new ExcelUtil<>(DwhFinalAllocationCostA.class);
        util.exportExcel(response, list, "最终分摊成本表数据");
    }

    @ApiOperation(value = "导出费用分摊-定版视图数据")
    @Log(title = "导出费用分摊-定版视图数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCostFinalAllocationData")
    public void exportCostFinalAllocationData(HttpServletResponse response) {
        List<DwhFinalAllocationCostA> list = dwhFinalAllocationCostAService.lambdaQuery().eq(DwhFinalAllocationCostA::getAllocationType, "assign_non").list();
        ExcelUtil<DwhFinalAllocationCostA> util = new ExcelUtil<>(DwhFinalAllocationCostA.class);
        util.exportExcel(response, list, "最终分摊成本表数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalAllocationCostA> util = new ExcelUtil<>(DwhFinalAllocationCostA.class);
        util.importTemplateExcel(response, "最终分摊成本表模板");
    }

    @ApiOperation(value = "管理范围（SelectVo）")
    @GetMapping("/manageScope")
    public R<List<SelectVo>> manageScope(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listManageScope("dwh.dwh_final_allocation_cost_a", searchVo));
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_final_allocation_cost_a", searchVo));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listCompanyId("dwh.dwh_final_allocation_cost_a", "node.cost_object_fr_company_id", searchVo));
    }

    @ApiOperation(value = "校验非定向分摊当月是否执行已完成过记账")
    @GetMapping("/checkAllocationCost")
    public R<Boolean> checkAllocationCost(@RequestParam(required = false, value = "controlAreaId") String controlAreaId,
                                          @RequestParam(required = false, value = "verId") String verId,
                                          @RequestParam(required = false, value = "yearMonthFrom") String yearMonthFrom,
                                          @RequestParam(required = false, value = "yearMonthTo") String yearMonthTo,
                                          @RequestParam(required = false, value = "allocationMethodId") String allocationMethodId) {
        return R.ok(dwhFinalAllocationCostAService.checkAllocationCost(controlAreaId, verId, yearMonthFrom, yearMonthTo, allocationMethodId));
    }

    @ApiOperation(value = "校验定向分摊当月是否执行已完成过记账")
    @GetMapping("/checkTargetedAllocationCost")
    public R<Boolean> checkTargetedAllocationCost(
            @RequestParam(required = false, value = "companyId") String companyId,
            @RequestParam(required = false, value = "yearMonthFrom") String yearMonthFrom,
            @RequestParam(required = false, value = "yearMonthTo") String yearMonthTo,
            @RequestParam(required = false, value = "allocationType") String allocationType) {
        return R.ok(dwhFinalAllocationCostAService.checkTargetedAllocationCost(companyId, yearMonthFrom, yearMonthTo, allocationType));
    }

}