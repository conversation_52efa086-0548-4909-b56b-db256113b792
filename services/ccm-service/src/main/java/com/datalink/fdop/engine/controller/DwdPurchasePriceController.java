package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwdPurchasePrice;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdPurchasePriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdPurchasePrice")
@Api(tags = "CCMS-采购价格信息")
public class DwdPurchasePriceController extends BaseController {

    @Autowired
    private DwdPurchasePriceService dwdPurchasePriceService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "采购价格信息")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialId = MapUtils.getAsString(params, "materialId");
        String plantId = MapUtils.getAsString(params, "plantId");
        PageDataInfo<DwdPurchasePrice> overview = dwdPurchasePriceService.overview(materialId, plantId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdPurchasePrice.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "采购价格信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialId = MapUtils.getAsString(params, "materialId");
        String plantId = MapUtils.getAsString(params, "plantId");

        List<DwdPurchasePrice> list = dwdPurchasePriceService.selectNoPage(materialId, plantId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdPurchasePrice> util = new ExcelUtil<>(DwdPurchasePrice.class);
        util.exportExcel(response, list, "信息基础：采购");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdPurchasePrice> util = new ExcelUtil<>(DwdPurchasePrice.class);
        util.importTemplateExcel(response, "信息基础：采购");
    }


    @ApiOperation(value = "查询工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_purchase_price", null));
    }

    @ApiOperation(value = "物料编码（SelectVo）")
    @GetMapping(value = "/listMaterialId")
    public R<List<SelectVo>> selectMaterialId() {
        return R.ok(dwdPurchasePriceService.lambdaQuery()
                .select(DwdPurchasePrice::getMaterialId, DwdPurchasePrice::getMaterialDesc)
                .groupBy(DwdPurchasePrice::getMaterialId, DwdPurchasePrice::getMaterialDesc)
                .orderByAsc(DwdPurchasePrice::getMaterialId)
                .list().stream()
                .filter(dwdPurchasePrice -> dwdPurchasePrice != null && dwdPurchasePrice.getMaterialId() != null)
                .map(dwdPurchasePrice -> new SelectVo(dwdPurchasePrice.getMaterialId(), dwdPurchasePrice.getMaterialDesc()))
                .collect(Collectors.toList()));
    }

}
