package com.datalink.fdop.base.controller;

import com.datalink.fdop.base.api.domain.EvaluateElement;
import com.datalink.fdop.base.service.EvaluateElementService;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/base/evaluateElement")
@Api(tags = "fccm-要素API")
@Transactional(rollbackFor = Exception.class)
public class EvaluateElementController extends BaseController {

    @Autowired
    private EvaluateElementService evaluateElementService;

    @ApiOperation(value = "新增要素")
    @Log(title = "评估要素", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R add(@Validated @RequestBody EvaluateElement element) {
        return R.toResult(evaluateElementService.insert(element));
    }

    @ApiOperation(value = "修改要素")
    @Log(title = "评估要素", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public R edit(@Validated @RequestBody EvaluateElement element) {
        return R.toResult(evaluateElementService.updateByKey(element));
    }

    @ApiOperation(value = "删除要素")
    @Log(title = "评估要素", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    public R remove(@RequestBody List<EvaluateElement> list) {
        return R.toResult(evaluateElementService.deleteByList(list));
    }

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "评估要素")
    @PostMapping(value = "/overview")
    public R<PageDataInfo> overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) SearchVo searchVo
    ) {
        return R.ok(evaluateElementService.overview(sort, searchVo));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "评估要素", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<EvaluateElement> list = evaluateElementService.list();
        ExcelUtil<EvaluateElement> util = new ExcelUtil<>(EvaluateElement.class);
        util.exportExcel(response, list, "公式数据");
    }

    @ApiOperation(value = "导入数据")
    @Log(title = "评估要素", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R importData(MultipartFile file, @RequestParam(value = "updateSupport", required = false) Boolean updateSupport) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        if (updateSupport == null) {
            updateSupport = false;
        }
        ExcelUtil<EvaluateElement> util = new ExcelUtil<>(EvaluateElement.class);
        List<EvaluateElement> factorList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = evaluateElementService.importData(factorList, updateSupport, operName);
        return R.ok(message);
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<EvaluateElement> util = new ExcelUtil<>(EvaluateElement.class);
        util.importTemplateExcel(response, "公式数据");
    }

    @ApiOperation(value = "批量启用/禁用")
    @Log(title = "评估要素",businessType = BusinessType.UPDATE)
    @PostMapping("/batchStatus")
    public R batchStatus(@RequestParam(value = "enable") Boolean enable, @RequestBody List<EvaluateElement> keyList) {
        return R.ok(evaluateElementService.updateBatchStatus(enable, keyList));
    }

    @ApiOperation(value = "评估分组（下拉框）")
    @GetMapping("/selectEvaluateAreaGroup")
    public R<List<String>> selectEvaluateAreaGroup() {
        return R.ok(evaluateElementService.selectEvaluateAreaGroup());
    }

    @ApiOperation(value = "细分评估类（下拉框）")
    @GetMapping("/selectEvaluateSort")
    public R<List<String>> selectEvaluateSort() {
        return R.ok(evaluateElementService.selectEvaluateSort());
    }

}
