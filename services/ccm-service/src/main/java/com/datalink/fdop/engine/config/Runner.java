package com.datalink.fdop.engine.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.datasource.dynamic.DynamicDataSource;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.engine.aop.TrinoApiPoint;
import com.datalink.fdop.tenant.api.domain.dto.DataSourceInfo;
import com.datalink.fdop.tenant.api.enums.DataSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023-02-15 15:34
 */
@Component
public class Runner implements ApplicationRunner {

    private Logger logger = LoggerFactory.getLogger(Runner.class);

    @Autowired
    private RemoteDriveService remoteDriveService;

    private static final long RETRY_INTERVAL = 10000;

    @Override
    public void run(ApplicationArguments args) {
        int retryCount = 0;
        boolean success = false;
        
        while (!success) {
            try {
                if (retryCount > 0) {
                    logger.info("正在进行第{}次重试初始化{}...", retryCount, TrinoApiPoint.DATA_SOURCE_FCCM);
                    Thread.sleep(RETRY_INTERVAL);
                }
                
                R<DataSource> ccms = remoteDriveService.queryDataSource("CCMS");
                DataSource data = ccms.getData();
                String dataSourceBasicInfo = data.getDataSourceBasicInfo();
                JSONObject jdbcInfo = JSON.parseObject(dataSourceBasicInfo);
                JSONObject connectConfig = JSON.parseObject(jdbcInfo.getString("param"));
                DataSourceInfo dataSourceInfo = new DataSourceInfo();
                dataSourceInfo.setDataSourceType(DataSourceType.TRINO);
                dataSourceInfo.setDriver(jdbcInfo.getString("driverClassName"));
                dataSourceInfo.setUrl(jdbcInfo.getString("jdbcUrl"));
                dataSourceInfo.setUsername(jdbcInfo.getString("username"));
                dataSourceInfo.setPassword(jdbcInfo.getString("password"));
                dataSourceInfo.setInitialSize(connectConfig.getIntValue("initialSize"));
                dataSourceInfo.setMinIdle(connectConfig.getIntValue("minIdle"));
                dataSourceInfo.setMaxActive(connectConfig.getIntValue("maxActive"));
                dataSourceInfo.setMaxWait(connectConfig.getIntValue("maxWait"));
                DynamicDataSource.getInstance().addDataSource(TrinoApiPoint.DATA_SOURCE_FCCM, dataSourceInfo);
                
                success = true;
                logger.info("{}数据源初始化成功", TrinoApiPoint.DATA_SOURCE_FCCM);
            } catch (Exception e) {
                retryCount++;
                logger.error("初始化{}失败(第{}次尝试):{}", TrinoApiPoint.DATA_SOURCE_FCCM, retryCount, e.getMessage());
                DynamicDataSource.getInstance().deleteDataSource(TrinoApiPoint.DATA_SOURCE_FCCM);
            }
        }
    }

}

