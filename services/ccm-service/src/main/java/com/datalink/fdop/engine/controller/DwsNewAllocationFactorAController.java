package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.SearchVoUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationFactorA;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwsNewAllocationFactorAService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/base/dwsNewAllocationFactorA")
@Api(tags = "实际成本引擎-逻辑视图-因子统计")
public class DwsNewAllocationFactorAController extends BaseController {

    @Autowired
    private DwsNewAllocationFactorAService dwsNewAllocationFactorAService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "逻辑视图-因子统计")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        String costCenterType = MapUtils.getAsString(params, "costCenterType");

        PageDataInfo<DwsNewAllocationFactorA> overview = dwsNewAllocationFactorAService.overview(
                controlAreaId, verId, companyId, yearMonthFrom, yearMonthTo, costCenterType, allocationType, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwsNewAllocationFactorA.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "逻辑视图-因子统计", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(
            HttpServletResponse response, @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String controlAreaId = MapUtils.getAsString(params, "controlAreaId");
        String verId = MapUtils.getAsString(params, "verId");
        String companyId = MapUtils.getAsString(params, "companyId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        String allocationType = MapUtils.getAsString(params, "allocationType");
        String costCenterType = MapUtils.getAsString(params, "costCenterType");
        List<DwsNewAllocationFactorA> list = dwsNewAllocationFactorAService.selectNoPage(controlAreaId,verId,companyId,yearMonthFrom, yearMonthTo,
                allocationType, costCenterType,sort, queryParam.getSearchVo());
        ExcelUtil<DwsNewAllocationFactorA> util = new ExcelUtil<>(DwsNewAllocationFactorA.class);
        util.exportExcel(response, list, "逻辑视图-因子统计数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsNewAllocationFactorA> util = new ExcelUtil<>(DwsNewAllocationFactorA.class);
        util.importTemplateExcel(response, "逻辑视图-因子统计模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listVerId("dws.dws_new_allocation_factor_a", searchVo));
    }

    @ApiOperation(value = "管理范围（SelectVo）")
    @GetMapping("/manageScope")
    public R<List<SelectVo>> manageScope(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listManageScope("dws.dws_new_allocation_factor_a", searchVo));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listPlantId("dws.dws_new_allocation_factor_a", searchVo));
    }

    @ApiOperation(value = "公司代码（SelectVo）")
    @GetMapping("/companyId")
    public R<List<SelectVo>> companyId(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listCompanyId("dws.dws_new_allocation_factor_a", searchVo));
    }

    @ApiOperation(value = "成本中心（SelectVo）")
    @GetMapping("/costCenter")
    public R<List<SelectVo>> costCenter(@RequestParam(required = false, value = "allocationType") String allocationType) {
        SearchVo searchVo = null;
        if (StringUtils.isNotEmpty(allocationType)) {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("allocation_type", allocationType);
            searchVo = SearchVoUtils.buildSearchVoAndConditions(conditions);
        }
        return R.ok(dataDictionaryService.listCostCenterId("dws.dws_new_allocation_factor_a", searchVo));
    }

    @ApiOperation(value = "成本中心类型")
    @GetMapping("/costCenterType")
    public R<List<String>> costCenterType(@RequestParam(required = false, value = "allocationType") String allocationType) {
        return R.ok(dwsNewAllocationFactorAService.lambdaQuery()
                .eq(DwsNewAllocationFactorA::getAllocationType, allocationType)
                .select(DwsNewAllocationFactorA::getCostCenterType)
                .groupBy(DwsNewAllocationFactorA::getCostCenterType).list()
                .stream().filter(dwsNewAllocationFactorA -> dwsNewAllocationFactorA != null && dwsNewAllocationFactorA.getCostCenterType() != null)
                .map(DwsNewAllocationFactorA::getCostCenterType).collect(Collectors.toList()));
    }

}