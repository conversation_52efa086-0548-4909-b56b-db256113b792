package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.DwdCbom;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdCbomService extends IService<DwdCbom> {

    PageDataInfo<DwdCbom> overview(String plantId, String productId, String sort, SearchVo searchVo);

    List<DwdCbom> selectNoPage(String plantId, String productId, String sort, SearchVo searchVo);
}