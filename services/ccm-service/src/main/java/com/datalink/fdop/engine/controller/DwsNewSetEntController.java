package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.base.service.CostStructureHeadService;
import com.datalink.fdop.base.service.FactoryService;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwsNewSetEnt;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwsNewSetEntService;
import com.datalink.fdop.engine.utils.DynamicExcelExportUtil;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwsNewSetEnt")
@Api(tags = "约当分配结算-逻辑视图-事件维")
public class DwsNewSetEntController extends BaseController {

    @Autowired
    private DwsNewSetEntService dwsNewSetEntService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @Autowired
    private CostStructureHeadService costStructureHeadService;

    @Autowired
    private FactoryService factoryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "约当分配结算-逻辑视图-事件维")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String yearMonth = MapUtils.getAsString(params, "yearMonth");
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        PageDataInfo<DwsNewSetEnt> overview = dwsNewSetEntService.overview(yearMonth, verId, plantId, sort, queryParam.getSearchVo());
        String controlAreaId = factoryService.getControlAreaIdByPlantId(plantId);
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicColumnList(controlAreaId);
        return R.ok(MetaUtils.getMetadata(DwsNewSetEnt.class, overview,dynamicColumns));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "约当分配结算-逻辑视图-事件维", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String yearMonth = MapUtils.getAsString(params, "yearMonth");
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        List<DwsNewSetEnt> list = dwsNewSetEntService.selectNoPage(yearMonth, verId, plantId, sort, queryParam.getSearchVo());
        String controlAreaId = factoryService.getControlAreaIdByPlantId(plantId);
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicColumnList(controlAreaId);
        DynamicExcelExportUtil<DwsNewSetEnt> excelUtil = new DynamicExcelExportUtil<>(DwsNewSetEnt.class);
        excelUtil.exportExcelWithDynamicColumns(response, list, dynamicColumns, "约当分配结算-逻辑视图-事件维数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsNewSetEnt> util = new ExcelUtil<>(DwsNewSetEnt.class);
        util.importTemplateExcel(response, "约当分配结算-逻辑视图-事件维模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dws.dws_new_set_ent", null));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dws.dws_new_set_ent", null));
    }
}