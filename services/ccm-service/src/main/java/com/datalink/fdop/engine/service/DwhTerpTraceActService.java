package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.DwhTerpTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwhTerpTraceActService extends IService<DwhTerpTraceAct> {

    PageDataInfo<DwhTerpTraceAct> overview(String plantId, String dateFrom, String dateTo, String sort, SearchVo searchVo);

    List<DwhTerpTraceAct> selectNoPage(String plantId,
                                       String dateFrom,
                                       String dateTo,
                                       String sort,
                                       SearchVo searchVo);
}
