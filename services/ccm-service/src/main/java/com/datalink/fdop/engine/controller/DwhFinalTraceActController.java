package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.domain.DwhFinalTraceAct;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhFinalTraceActService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwhFinalTraceAct")
@Api(tags = "CCMS-dwh_final_trace_act")
public class DwhFinalTraceActController extends BaseController {

    @Autowired
    private DwhFinalTraceActService dwhFinalTraceActService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "报工作业履历")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String workOrder = MapUtils.getAsString(params, "workOrder");
        String plantId = MapUtils.getAsString(params, "plantId");
        String productId = MapUtils.getAsString(params, "productId");
        String dateFrom = MapUtils.getAsString(params, "dateFrom");
        String dateTo = MapUtils.getAsString(params, "dateTo");
        PageDataInfo<DwhFinalTraceAct> overview = dwhFinalTraceActService.overview(workOrder, plantId, productId, dateFrom, dateTo,sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhFinalTraceAct.class,overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "报工作业履历", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String workOrder = MapUtils.getAsString(params, "workOrder");
        String plantId = MapUtils.getAsString(params, "plantId");
        String productId = MapUtils.getAsString(params, "productId");
        String dateFrom = MapUtils.getAsString(params, "dateFrom");
        String dateTo = MapUtils.getAsString(params, "dateTo");
        List<DwhFinalTraceAct> list = dwhFinalTraceActService.selectNoPage(workOrder, plantId, productId, dateFrom,
                dateTo, sort, queryParam.getSearchVo());
        ExcelUtil<DwhFinalTraceAct> util = new ExcelUtil<>(DwhFinalTraceAct.class);
        util.exportExcel(response, list, "报工整合管理-定版视图数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalTraceAct> util = new ExcelUtil<>(DwhFinalTraceAct.class);
        util.importTemplateExcel(response, "报工整合管理-定版视图模板");
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwh.dwh_final_trace_act", null));
    }

    @ApiOperation(value = "工作工单（下拉框）")
    @GetMapping("/listWorkOrder")
    public R<List<String>> listWorkOrder() {
        return R.ok(dwhFinalTraceActService.lambdaQuery()
                .select(DwhFinalTraceAct::getWorkOrder)
                .groupBy(DwhFinalTraceAct::getWorkOrder)
                .orderByAsc(DwhFinalTraceAct::getWorkOrder).list().stream()
                .filter(dwhFinalTraceAct -> dwhFinalTraceAct != null && dwhFinalTraceAct.getWorkOrder() != null)
                .map(DwhFinalTraceAct::getWorkOrder).collect(Collectors.toList()));
    }

}
