package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.domain.DwdWph;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdWphService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdWph")
@Api(tags = "CCMS-WPH信息")
public class DwdWphController extends BaseController {

    @Autowired
    private DwdWphService dwdWphService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @Log(title = "WPH信息")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params , "plantId");
        String equipGroupId = MapUtils.getAsString(params , "equipGroupId");
        PageDataInfo<DwdWph> overview = dwdWphService.overview(plantId, equipGroupId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdWph.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "WPH信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params , "plantId");
        String equipGroupId = MapUtils.getAsString(params , "equipGroupId");
        List<DwdWph> list = dwdWphService.selectNoPage(plantId, equipGroupId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdWph> util = new ExcelUtil<>(DwdWph.class);
        util.exportExcel(response, list, "WPH信息");
    }

    @ApiOperation(value = "导出模板")
    @Log(title = "WPH信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdWph> util = new ExcelUtil<>(DwdWph.class);
        util.importTemplateExcel(response, "WPH信息");
    }

    @ApiOperation(value = "查询工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_wph", null));
    }

    @ApiOperation(value = "查询设备组（SelectVo）")
    @GetMapping("/listEquipGroupId")
    public R<List<SelectVo>> listEquipGroupId() {
        return R.ok(dwdWphService
                .lambdaQuery()
                .select(DwdWph::getEquipGroupId, DwdWph::getEquipGroupDesc)
                .groupBy(DwdWph::getEquipGroupId, DwdWph::getEquipGroupDesc)
                .orderByAsc(DwdWph::getEquipGroupId).list().stream()
                .filter(dwdWph -> dwdWph != null && dwdWph.getEquipGroupId() != null)
                .map(dwdWph -> new SelectVo(dwdWph.getEquipGroupId(), dwdWph.getEquipGroupDesc()))
                .collect(Collectors.toList()));
    }

}
