package com.datalink.fdop.base.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.Company;
import com.datalink.fdop.base.api.domain.ControlArea;
import com.datalink.fdop.base.mapper.CompanyMapper;
import com.datalink.fdop.base.mapper.FactoryMapper;
import com.datalink.fdop.base.service.CompanyService;
import com.datalink.fdop.base.service.ControlAreaService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.bean.BeanValidators;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Validator;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-09-05 11:48
 */
@Service
public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, Company> implements CompanyService {

    private static final Logger log = LoggerFactory.getLogger(CompanyServiceImpl.class);

    @Autowired
    protected Validator validator;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private FactoryMapper factoryMapper;

    @Autowired
    private ControlAreaService controlAreaService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertCompany(Company company) {
        company.setCreateTime(new Date());
        company.setCreateBy(SecurityUtils.getUsername());
        return save(company);
    }

    @Override
    public String checkCompanyIdUnique(String companyCode) {
        int count = companyMapper.checkCompanyIdUnique(companyCode);
        if (count == 1) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCompany(Company company) {
        company.setUpdateBy(SecurityUtils.getUsername());
        company.setUpdateTime(new Date());
        return updateById(company);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteCompanyByIds(List<String> companyIds) {
        factoryMapper.updateCompanyByDeleteCompanys(companyIds);
        return companyMapper.deleteCompanyByIds(companyIds);
    }

    @Override
    public PageDataInfo<Company> selectCompanyList(Company company, String sort) {
        // 获取分页参数
        Page<Company> page = PageUtils.getPage(Company.class);
        Page<Company> ipage = companyMapper.selectCompanyList(page, company, sort);
        return PageUtils.getPageInfo(ipage.getRecords(), (int) ipage.getTotal());
    }

    @Override
    public String importCompany(List<Company> companyList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(companyList) || companyList.size() == 0) {
            throw new ServiceException("导入公司数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (Company company : companyList) {
            try {
                if (StringUtils.isEmpty(company.getCurrency())) {
                    failureNum++;
                    failureMsg.append("\n" + failureNum + "、货币为空 ");
                } else if (companyMapper.checkCompanyIdUnique(company.getCompanyId()) == 0) {
                    BeanValidators.validateWithException(validator, company);
                    company.setCreateBy(operName);
                    if (company.getControlAreaId() != null) {
                        ControlArea controlArea = controlAreaService.selectById(company.getControlAreaId());
                        if (controlArea == null) {
                            company.setControlAreaId(null);
                        }
                    }
                    this.insertCompany(company);
                    successNum++;
                    successMsg.append("\n" + successNum + "、公司 " + company.getCompanyId() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, company);
                    company.setUpdateBy(operName);
                    if (company.getControlAreaId() != null) {
                        ControlArea controlArea = controlAreaService.selectById(company.getControlAreaId());
                        if (controlArea == null) {
                            company.setControlAreaId(null);
                        }
                    }
                    this.updateCompany(company);
                    successNum++;
                    successMsg.append("\n" + successNum + "、公司 " + company.getCompanyId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("\n" + failureNum + "、公司 " + company.getCompanyId() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "\n" + failureNum + "、公司 " + company.getCompanyId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！成功导入 " + successNum + " 条数据， 数据如下：" + successMsg + "， \n共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public PageDataInfo<Company> query(SearchVo searchVo, String sort) {
        String a = "";
        if (searchVo != null) {
            a = SearchUtils.parseSearchCondition("a", searchVo);
            a = a.replaceAll("a.control_area_desc", "controlArea.control_area_id");
        }
        Page<Company> page = PageUtils.getPage(Company.class);
        Page<Company> ipage = companyMapper.query(page, a, sort);
        return PageUtils.getPageInfo(ipage.getRecords(), (int) ipage.getTotal());
    }

    @Override
    public boolean updateBatchStatus(List<Company> statusList) {
        return companyMapper.updateBatchStatus(statusList);
    }

    @Override
    public List<Company> queryAll() {
        return companyMapper.selectList(new QueryWrapper<Company>().eq("enable", true));
    }

    @Override
    public List<SelectVo> listCompanyId(String orgTableName) {
        return companyMapper.listCompanyId(orgTableName);
    }

}
