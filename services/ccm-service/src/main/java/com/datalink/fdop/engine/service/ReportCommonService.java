package com.datalink.fdop.engine.service;

import com.datalink.fdop.common.core.domain.SelectVo;

import java.util.List;

public interface ReportCommonService {


    List<String> listCostCenterType(String companyId, String yearMonthFrom, String yearMonthTo);

    List<SelectVo> listCostCenterId(String companyId, String yearMonthFrom, String yearMonthTo);

    List<SelectVo> listCostElementId();

    List<SelectVo> listCostStructureId();

    List<String> listWorkArea();

    List<SelectVo> listEquipGroupId();

    List<String> listLotCategory();

    List<String> listLotType();
}
