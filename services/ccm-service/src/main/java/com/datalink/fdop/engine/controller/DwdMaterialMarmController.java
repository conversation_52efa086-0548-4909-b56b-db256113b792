package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwdMaterialMarm;
import com.datalink.fdop.engine.service.DwdMaterialMarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdMaterialMarm")
@Api(tags = "CCMS-物料换算比例")
public class DwdMaterialMarmController extends BaseController {

    @Autowired
    private DwdMaterialMarmService dwdMaterialMarmService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "物料换算比例")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialId = MapUtils.getAsString(params, "materialId");
        PageDataInfo<DwdMaterialMarm> overview = dwdMaterialMarmService.overview(materialId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdMaterialMarm.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "物料换算比例", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialId = MapUtils.getAsString(params, "materialId");
        List<DwdMaterialMarm> list = dwdMaterialMarmService.selectNoPage(materialId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdMaterialMarm> util = new ExcelUtil<>(DwdMaterialMarm.class);
        util.exportExcel(response, list, "换算比例");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdMaterialMarm> util = new ExcelUtil<>(DwdMaterialMarm.class);
        util.importTemplateExcel(response, "换算比例");
    }

    @ApiOperation("物料编码（SelectVo）")
    @GetMapping(value = "/listMaterialId")
    public R<List<SelectVo>> selectMaterialId() {
        return R.ok(dwdMaterialMarmService.lambdaQuery()
                .select(DwdMaterialMarm::getMaterialId, DwdMaterialMarm::getMaterialDesc)
                .groupBy(DwdMaterialMarm::getMaterialId, DwdMaterialMarm::getMaterialDesc)
                .orderByAsc(DwdMaterialMarm::getMaterialId).list().stream()
                .filter(dwdMaterialMarm -> dwdMaterialMarm != null && dwdMaterialMarm.getMaterialId() != null)
                .map(dwdMaterialMarm -> new SelectVo(dwdMaterialMarm.getMaterialId(), dwdMaterialMarm.getMaterialDesc()))
                .collect(Collectors.toList()));
    }


}
