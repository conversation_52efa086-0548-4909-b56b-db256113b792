package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.domain.ProductVo;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.base.service.CostStructureHeadService;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhFinalStdCostProd;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdMaterialMaraService;
import com.datalink.fdop.engine.service.DwhFinalStdCostProdService;
import com.datalink.fdop.engine.utils.DynamicExcelExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwhFinalStdCostProd")
@Api(tags = "CCMS-产品别主视图-定版视图")
public class DwhFinalStdCostProdController extends BaseController {

    @Autowired
    private DwhFinalStdCostProdService dwhFinalStdCostProdService;

    @Autowired
    private CostStructureHeadService costStructureHeadService;

    @Autowired
    private DwdMaterialMaraService dwdMaterialMaraService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "最终标准成本-产品成本管理")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params , "plantId");
        String verId = MapUtils.getAsString(params , "verId");
        String controlAreaId = MapUtils.getAsString(params , "controlAreaId");
        String productId = MapUtils.getAsString(params , "productId");
        Integer batchQty = MapUtils.getAsInteger(params , "batchQty");
        String baseYearMonth = MapUtils.getAsString(params , "baseYearMonth");
        String dateFrom = MapUtils.getAsString(params , "dateFrom");
        String dateTo = MapUtils.getAsString(params , "dateTo");

        PageDataInfo<DwhFinalStdCostProd> overview = dwhFinalStdCostProdService.overview(plantId, verId, controlAreaId, productId, batchQty, baseYearMonth, dateFrom, dateTo, sort, queryParam.getSearchVo());
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicColumnList(controlAreaId);
        return R.ok(MetaUtils.getMetadata(DwhFinalStdCostProd.class, overview, dynamicColumns));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "最终标准成本-产品成本管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params , "plantId");
        String verId = MapUtils.getAsString(params , "verId");
        String controlAreaId = MapUtils.getAsString(params , "controlAreaId");
        String productId = MapUtils.getAsString(params , "productId");
        Integer batchQty = MapUtils.getAsInteger(params , "batchQty");
        String baseYearMonth = MapUtils.getAsString(params , "baseYearMonth");
        String dateFrom = MapUtils.getAsString(params , "dateFrom");
        String dateTo = MapUtils.getAsString(params , "dateTo");
        List<DwhFinalStdCostProd> list = dwhFinalStdCostProdService.selectNoPage(plantId, verId, controlAreaId,productId,
                batchQty, baseYearMonth, dateFrom, dateTo, sort, queryParam.getSearchVo());
        List<DynamicColumn> dynamicColumns = costStructureHeadService.selectDynamicColumnList(controlAreaId);
        DynamicExcelExportUtil<DwhFinalStdCostProd> excelUtil = new DynamicExcelExportUtil<>(DwhFinalStdCostProd.class);
        excelUtil.exportExcelWithDynamicColumns(response, list, dynamicColumns, "主视图");

    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalStdCostProd> util = new ExcelUtil<>(DwhFinalStdCostProd.class);
        util.importTemplateExcel(response, "主视图");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_final_std_cost_prod", null));
    }

    @ApiOperation(value = "管理范围（SelectVo）")
    @GetMapping("/manageScope")
    public R<List<SelectVo>> manageScope() {
        return R.ok(dataDictionaryService.listManageScope("dwh.dwh_final_std_cost_prod", null));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwh.dwh_final_std_cost_prod", null));
    }

    @ApiOperation("查询产品编码（选择列表）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "查询产品编码（选择列表）")
    @GetMapping(value = "/selectProductList")
    public Object selectProductList(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false, value = "productId") String productId,
            @RequestParam(required = false, value = "productDesc") String productDesc,
            @RequestParam(required = false, value = "productCimId") String productCimId,
            @RequestBody(required = false) SearchVo searchVo) {
        PageDataInfo<ProductVo> overview = dwdMaterialMaraService.selectProductListByTableName(productId, productDesc, productCimId, sort, searchVo, "dwh.dwh_final_std_cost_prod");
        return R.ok(MetaUtils.getMetadata(ProductVo.class, overview));
    }

}