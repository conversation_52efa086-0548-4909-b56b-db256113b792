package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.domain.DwdSptREg;
import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdSptREgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdSptREg")
@Api(tags = "CCMS-WPH管理（RECIPE_ID）")
public class DwdSptREgController extends BaseController {

    @Autowired
    private DwdSptREgService dwdSptREgService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "WPH管理（RECIPE_ID）")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String equipGroupId = MapUtils.getAsString(params, "equipGroupId");
        PageDataInfo<DwdSptREg> overview = dwdSptREgService.overview(plantId, equipGroupId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdSptREg.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "WPH管理（RECIPE_ID）", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String plantId = MapUtils.getAsString(params, "plantId");
        String equipGroupId = MapUtils.getAsString(params, "equipGroupId");
        List<DwdSptREg> list = dwdSptREgService.selectNoPage(plantId, equipGroupId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdSptREg> util = new ExcelUtil<>(DwdSptREg.class);
        util.exportExcel(response, list, "RECIPE均值");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdSptREg> util = new ExcelUtil<>(DwdSptREg.class);
        util.importTemplateExcel(response, "RECIPE均值");
    }

    @ApiOperation(value = "查询工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_spt_r_eg", null));
    }

    @ApiOperation(value = "查询设备组（SelectVo）")
    @GetMapping("/listEquipGroupId")
    public R<List<SelectVo>> listEquipGroupId() {
        return R.ok(dwdSptREgService
                .lambdaQuery()
                .select(DwdSptREg::getEquipGroupId, DwdSptREg::getEquipGroupDesc)
                .groupBy(DwdSptREg::getEquipGroupId, DwdSptREg::getEquipGroupDesc)
                .orderByAsc(DwdSptREg::getEquipGroupId).list().stream()
                .filter(dwdSptREg -> dwdSptREg != null && dwdSptREg.getEquipGroupId() != null)
                .map(dwdSptREg -> new SelectVo(dwdSptREg.getEquipGroupId(), dwdSptREg.getEquipGroupDesc()))
                .collect(Collectors.toList()));
    }

}
