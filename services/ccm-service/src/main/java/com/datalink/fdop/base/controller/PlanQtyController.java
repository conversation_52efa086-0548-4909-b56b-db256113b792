package com.datalink.fdop.base.controller;

import com.datalink.fdop.base.api.domain.PlanQty;
import com.datalink.fdop.base.service.PlanQtyService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/planQty")
@Api(tags = "fccm-计划作业量API")
public class PlanQtyController extends BaseController {

    @Autowired
    private PlanQtyService planQtyService;

    @ApiOperation(value = "新增计划作业量")
    @Log(title = "计划作业量管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody PlanQty planQty) {

        if (UserConstants.NOT_UNIQUE.equals(planQtyService.checkUnique(planQty))) {
            return R.fail("新增计划作业量失败，计划作业量已存在");
        }
        planQty.setCreateTime(new Date());
        planQty.setCreateBy(SecurityUtils.getUsername());
        if (planQty.getDelFlag() == null) {
            planQty.setDelFlag(false);
        }
        if (planQty.getEnable() == null) {
            planQty.setEnable(true);
        }
        return R.toResult(planQtyService.save(planQty));
    }

    @ApiOperation(value = "修改计划作业量")
    @Log(title = "计划作业量管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@Validated @RequestBody PlanQty planQty) {
        if (UserConstants.UNIQUE.equals(planQtyService.checkUnique(planQty))) {
            return R.fail("修改计划作业量失败，计划作业量不存在");
        }
        planQty.setUpdateBy(SecurityUtils.getUsername());
        planQty.setUpdateTime(new Date());
        return R.toResult(planQtyService.updateByKey(planQty));
    }

    @ApiOperation(value = "删除计划作业量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planQtyIds", value = "计划作业量ID集合", required = true, allowMultiple = true, dataType = "Long", paramType = "body", example = "[1,2]"),
    })
    @Log(title = "计划作业量管理", businessType = BusinessType.DELETE)
    @DeleteMapping()
    public R remove(@Validated @RequestBody List<PlanQty> planQtieList) {
        return R.toResult(planQtyService.deleteByIds(planQtieList));
    }

    @ApiOperation(value = "获取计划作业量列表")
    @PostMapping("/list")
    public R<PageDataInfo> list(@RequestBody(required = false) PlanQty planQty) {
        return R.ok(planQtyService.list(planQty));
    }

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "计划作业量管理")
    @PostMapping(value = "/overview")
    public R<PageDataInfo> overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) SearchVo searchVo
    ) {
        return R.ok(planQtyService.overview(sort, searchVo));
    }

    @ApiOperation(value = "导出计划作业量")
    @Log(title = "计划作业量管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody(required = false) PlanQty planQty) {
        List<PlanQty> list = planQtyService.list(planQty).getTotalList();
        ExcelUtil<PlanQty> util = new ExcelUtil<>(PlanQty.class);
        util.exportExcel(response, list, "计划作业量数据");
    }

    @ApiOperation(value = "导入计划作业量")
    @Log(title = "计划作业量管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R importData(MultipartFile file, @RequestParam(value = "updateSupport", required = false) Boolean updateSupport) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        if (updateSupport == null) {
            updateSupport = false;
        }
        ExcelUtil<PlanQty> util = new ExcelUtil<>(PlanQty.class);
        List<PlanQty> list = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = planQtyService.importData(list, updateSupport, operName);
        return R.ok(message);
    }

    @ApiOperation(value = "导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PlanQty> util = new ExcelUtil<>(PlanQty.class);
        util.importTemplateExcel(response, "计划作业量数据");
    }

}
