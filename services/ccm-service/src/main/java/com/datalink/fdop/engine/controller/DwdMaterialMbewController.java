package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwdMaterialMbew;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdMaterialMbewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdMaterialMbew")
@Api(tags = "CCMS-物料评估信息")
public class DwdMaterialMbewController extends BaseController {

    @Autowired
    private DwdMaterialMbewService dwdMaterialMbewService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "物料评估信息")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialType = MapUtils.getAsString(params, "materialType");
        String materialGroup = MapUtils.getAsString(params, "materialGroup");
        String materialId = MapUtils.getAsString(params, "materialId");
        String evaluateAreaId = MapUtils.getAsString(queryParam.getParams(), "evaluateAreaId");

        PageDataInfo<DwdMaterialMbew> overview = dwdMaterialMbewService.overview(materialType, materialGroup, materialId, evaluateAreaId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdMaterialMbew.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "物料评估信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialType = MapUtils.getAsString(params, "materialType");
        String materialGroup = MapUtils.getAsString(params, "materialGroup");
        String materialId = MapUtils.getAsString(params, "materialId");
        String evaluateAreaId = MapUtils.getAsString(queryParam.getParams(), "evaluateAreaId");

        List<DwdMaterialMbew> list = dwdMaterialMbewService.selectNoPage(materialType, materialGroup, materialId, evaluateAreaId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdMaterialMbew> util = new ExcelUtil<>(DwdMaterialMbew.class);
        util.exportExcel(response, list, "价值信息");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdMaterialMbew> util = new ExcelUtil<>(DwdMaterialMbew.class);
        util.importTemplateExcel(response, "价值信息");
    }

    @ApiOperation(value = "物料类型（SelectVo）")
    @GetMapping(value = "/selectMaterialType")
    public R<List<SelectVo>> selectMaterialType() {
        return R.ok(dwdMaterialMbewService.lambdaQuery()
                .select(DwdMaterialMbew::getMaterialType, DwdMaterialMbew::getMaterialTypeDesc)
                .groupBy(DwdMaterialMbew::getMaterialType, DwdMaterialMbew::getMaterialTypeDesc)
                .orderByAsc(DwdMaterialMbew::getMaterialType)
                .list().stream()
                .filter(dwdMaterialMbew -> dwdMaterialMbew != null && dwdMaterialMbew.getMaterialType() != null)
                .map(dwdMaterialMbew -> new SelectVo(dwdMaterialMbew.getMaterialType(), dwdMaterialMbew.getMaterialTypeDesc()))
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "物料组（SelectVo）")
    @GetMapping(value = "/selectMaterialGroup")
    public R<List<SelectVo>> selectMaterialGroup() {
        return R.ok(dwdMaterialMbewService.lambdaQuery()
                .select(DwdMaterialMbew::getMaterialGroup, DwdMaterialMbew::getMaterialGroupDesc)
                .groupBy(DwdMaterialMbew::getMaterialGroup, DwdMaterialMbew::getMaterialGroupDesc)
                .orderByAsc(DwdMaterialMbew::getMaterialGroup)
                .list().stream()
                .filter(dwdMaterialMbew -> dwdMaterialMbew != null && dwdMaterialMbew.getMaterialGroup() != null)
                .map(dwdMaterialMbew -> new SelectVo(dwdMaterialMbew.getMaterialGroup(), dwdMaterialMbew.getMaterialGroupDesc()))
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "物料编码（SelectVo）")
    @GetMapping(value = "/listMaterialId")
    public R<List<SelectVo>> selectMaterialId() {
        return R.ok(dwdMaterialMbewService.lambdaQuery()
                .select(DwdMaterialMbew::getMaterialId, DwdMaterialMbew::getMaterialDesc)
                .groupBy(DwdMaterialMbew::getMaterialId, DwdMaterialMbew::getMaterialDesc)
                .orderByAsc(DwdMaterialMbew::getMaterialId)
                .list().stream()
                .filter(dwdMaterialMbew -> dwdMaterialMbew != null && dwdMaterialMbew.getMaterialId() != null)
                .map(dwdMaterialMbew -> new SelectVo(dwdMaterialMbew.getMaterialId(), dwdMaterialMbew.getMaterialDesc()))
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "评估范围（SelectVo）")
    @GetMapping(value = "/selectEvaluateAreaId")
    public R<List<SelectVo>> selectEvaluateAreaId() {
        return R.ok(dataDictionaryService.selectEvaluateAreaId("dwd.dwd_material_mbew", null));
    }

}
