package com.datalink.fdop.base.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.Teep;
import com.datalink.fdop.base.mapper.FactoryMapper;
import com.datalink.fdop.base.mapper.TeepMapper;
import com.datalink.fdop.base.service.TeepService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TeepServiceImpl extends ServiceImpl<TeepMapper, Teep> implements TeepService {

    @Autowired
    private TeepMapper teepMapper;

    @Autowired
    private FactoryMapper factoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(Teep teep) {
        if (UserConstants.NOT_UNIQUE.equals(checkIdUnique(teep.getEquipId()))) {
            throw new ServiceException("新增作业类型'" + "'失败，作业类型已存在");
        }
        teepMapper.deleteById(teep.getEquipId());
        teep.setCreateTime(new Date());
        teep.setCreateBy(SecurityUtils.getUsername());
        if (teep.getEnable() == null) {
            teep.setEnable(true);
        }
        return save(teep);
    }

    @Override
    public String checkIdUnique(String equipId) {
        int count = teepMapper.checkIdUnique(equipId);
        if (count == 1) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    public String checkIdExist(String equipId) {
        int count = teepMapper.checkIdExist(equipId);
        if (count == 1) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByList(List<Teep> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请指定要删除的数据");
        }
        for (Teep teep : list) {
            if (UserConstants.UNIQUE.equals(checkIdExist(teep.getEquipId()))) {
                teep.setCreateTime(new Date());
                teep.setCreateBy(SecurityUtils.getUsername());
                if (teep.getEnable() == null) {
                    teep.setEnable(true);
                }
                save(teep);
            }
        }
        return teepMapper.deleteBykeys(list.stream().map(Teep::getEquipId).collect(Collectors.toList()));
    }

    @Override
    public String importData(List<Teep> list, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (Teep teep : list) {
            try {
                if (teep.getEquipId() == null) {
                    failureNum++;
                    failureMsg.append("\n" + failureNum + "、机台ID为空 ");
                } else if (teepMapper.checkIdUnique(teep.getEquipId()) == 0) {
                    teep.setCreateBy(operName);
                    teep.setCreateTime(new Date());
                    teepMapper.deleteById(teep.getEquipId());
                    this.save(teep);
                    successNum++;
                    successMsg.append("\n" + successNum + "、TEEP管理 " + teep.getTeep() + " 导入成功");
                } else if (isUpdateSupport) {
                    this.updateByKey(teep);
                    successNum++;
                    successMsg.append("\n" + successNum + "、TEEP管理 " + teep.getTeep() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("\n" + failureNum + "、TEEP管理 " + teep.getTeep() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "\n" + failureNum + "、TEEP管理 " + teep.getTeep() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！成功导入 " + successNum + " 条数据， 数据如下：" + successMsg + "， \n共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public PageDataInfo<Teep> overview(String sort, SearchVo searchVo) {
        Page<Teep> page = PageUtils.getPage(Teep.class);
        String a = "";
        if (searchVo != null) {
            a = SearchUtils.parseSqlSearchCondition("a", searchVo);
            a = a.replaceAll("a.plant_desc", "b.plant_desc");
        }
        IPage<Teep> iPage = teepMapper.selectAll(page, sort, a);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateByKey(Teep teep) {
        if (UserConstants.NOT_UNIQUE.equals(checkIdExist(teep.getEquipId()))) {
            return teepMapper.updateByKey(teep);
        }
        teep.setCreateTime(new Date());
        teep.setCreateBy(SecurityUtils.getUsername());
        if (teep.getEnable() == null) {
            teep.setEnable(true);
        }
        teepMapper.deleteById(teep.getEquipId());
        save(teep);
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateBatchStatus(Boolean enable, List<Teep> statusList) {
        if (CollectionUtils.isEmpty(statusList)) {
            throw new ServiceException("请指定要修改的数据");
        }
        for (Teep teep : statusList) {
            if (UserConstants.UNIQUE.equals(checkIdExist(teep.getEquipId()))) {
                teep.setCreateTime(new Date());
                teep.setCreateBy(SecurityUtils.getUsername());
                save(teep);
            }
        }
        return teepMapper.updateBatchStatus(enable, statusList);
    }

    @Override
    public PageDataInfo<Teep> queryOrigList(String sort, SearchVo searchVo) {
        Page<Teep> page = PageUtils.getPage(Teep.class);
        IPage<Teep> iPage = teepMapper.queryOrigList(page, sort,searchVo);
        return PageUtils.getPageInfo(new ArrayList<>(iPage.getRecords()), (int) iPage.getTotal());
    }

    @Override
    public PageDataInfo<Teep> queryList(String sort,SearchVo searchVo) {
        Page<Teep> page = PageUtils.getPage(Teep.class);
        IPage<Teep> iPage = teepMapper.queryList(page, sort,searchVo);
        return PageUtils.getPageInfo(new ArrayList<>(iPage.getRecords()), (int) iPage.getTotal());
    }

    @Override
    public boolean batchDelete(List<Teep> list) {
        return teepMapper.batchDelete(list);
    }


}
