package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.base.service.CostStructureHeadService;
import com.datalink.fdop.base.service.FactoryService;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwsNewAllocationFactorP;
import com.datalink.fdop.engine.api.domain.DwsNewCostOrd;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwsNewCostOrdService;
import com.datalink.fdop.engine.utils.DynamicExcelExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/base/dwsNewCostOrd")
@Api(tags = "结算管理-逻辑视图-工单成本")
public class DwsNewCostOrdController extends BaseController {

    @Autowired
    private DwsNewCostOrdService dwsNewCostOrdService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "结算管理-逻辑视图-工单成本")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        String yearMonth = MapUtils.getAsString(params, "yearMonth");
        PageDataInfo<DwsNewCostOrd> overview = dwsNewCostOrdService.overview(verId, plantId, yearMonth, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwsNewCostOrd.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "结算管理-逻辑视图-工单成本", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        String yearMonth = MapUtils.getAsString(params, "yearMonth");
        List<DwsNewCostOrd> list = dwsNewCostOrdService.selectNoPage(verId, plantId, yearMonth,
                sort, queryParam.getSearchVo());
        ExcelUtil<DwsNewCostOrd> util = new ExcelUtil<>(DwsNewCostOrd.class);
        util.exportExcel(response, list, "结算管理-逻辑视图-工单成本数据");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwsNewCostOrd> util = new ExcelUtil<>(DwsNewCostOrd.class);
        util.importTemplateExcel(response, "结算管理-逻辑视图-工单成本模板");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dws.dws_new_cost_ord", null));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dws.dws_new_cost_ord", null));
    }
}