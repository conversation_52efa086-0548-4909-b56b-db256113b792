package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwdPrice;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdPriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdPrice")
@Api(tags = "CCMS-价格评估")
public class DwdPriceController extends BaseController {

    @Autowired
    private DwdPriceService dwdPriceService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "价格评估")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialId = MapUtils.getAsString(params, "materialId");
        String plantId = MapUtils.getAsString(params, "plantId");
        PageDataInfo<DwdPrice> overview = dwdPriceService.overview(materialId, plantId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdPrice.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "价格评估", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialId = MapUtils.getAsString(params, "materialId");
        String plantId = MapUtils.getAsString(params, "plantId");

        List<DwdPrice> list = dwdPriceService.selectNoPage(materialId, plantId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdPrice> util = new ExcelUtil<>(DwdPrice.class);
        util.exportExcel(response, list, "评估信息");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdPrice> util = new ExcelUtil<>(DwdPrice.class);
        util.importTemplateExcel(response, "评估信息");
    }

    @ApiOperation(value = "查询工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_price", null));
    }

    @ApiOperation("物料代码（SelectVo）")
    @GetMapping("/listMaterialId")
    public R<List<SelectVo>> listMaterialId() {
        return R.ok(dwdPriceService.lambdaQuery()
                .select(DwdPrice::getMaterialId, DwdPrice::getMaterialDesc)
                .groupBy(DwdPrice::getMaterialId, DwdPrice::getMaterialDesc)
                .orderByAsc(DwdPrice::getMaterialId).list().stream()
                .filter(dwdPrice -> dwdPrice != null && dwdPrice.getMaterialId() != null)
                .map(dwdPrice -> new SelectVo(dwdPrice.getMaterialId(), dwdPrice.getMaterialDesc())).collect(Collectors.toList()));
    }

}
