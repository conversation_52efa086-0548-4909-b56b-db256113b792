package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.DwhFinalTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwhFinalTraceActService extends IService<DwhFinalTraceAct> {

    PageDataInfo<DwhFinalTraceAct> overview(
            String workOrder, String plantId,String productId,
            String  dateFrom,String  dateTo,
            String sort, SearchVo searchVo);

    List<DwhFinalTraceAct> selectNoPage(String workOrder,
                                        String plantId,
                                        String productId,
                                        String dateFrom,
                                        String dateTo,
                                        String sort,
                                        SearchVo searchVo);
}
