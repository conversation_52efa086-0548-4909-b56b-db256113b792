package com.datalink.fdop.base.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.base.api.domain.CostStructureItem;
import com.datalink.fdop.base.mapper.CostStructureHeadMapper;
import com.datalink.fdop.base.mapper.CostStructureItemMapper;
import com.datalink.fdop.base.service.CostStructureItemService;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CostStructureItemServiceImpl extends ServiceImpl<CostStructureItemMapper, CostStructureItem> implements CostStructureItemService {

    @Autowired
    private CostStructureItemMapper costStructureItemMapper;

    @Autowired
    private CostStructureHeadMapper costStructureHeadMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(CostStructureItem costStructureItem) {
        if (UserConstants.NOT_UNIQUE.equals(checkIdUnique(costStructureItem.getControlAreaId(), costStructureItem.getCostStructureId(),
                DateUtils.dateTime(costStructureItem.getDateFrom()), costStructureItem.getEvaluateAreaId()))) {
            throw new ServiceException("新增成本组件行表'" + costStructureItem.getCostStructureId() + "'失败，成本组件行表已存在");
        }
        if (costStructureHeadMapper.selectByKey(costStructureItem.getControlAreaId(), costStructureItem.getCostStructureId(), DateUtils.dateTime(costStructureItem.getDateFrom())) == null) {
            throw new ServiceException("成本组件头表不存在");
        }
        costStructureItem.setCreateTime(new Date());
        costStructureItem.setCreateBy(SecurityUtils.getUsername());
        if (costStructureItem.getDelFlag() == null) {
            costStructureItem.setDelFlag(false);
        }
        if (costStructureItem.getEnable() == null) {
            costStructureItem.setEnable(true);
        }
        return save(costStructureItem);
    }

    @Override
    public String checkIdUnique(String controlAreaId, String costStructureId, String date, String evaluateAreaId) {
        int count = costStructureItemMapper.checkIdUnique(controlAreaId, costStructureId, date, evaluateAreaId);
        if (count == 1) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByList(List<CostStructureItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请指定要删除的数据");
        }
        int i = 0;
        for (CostStructureItem costStructureItem : list) {
            i = i + costStructureItemMapper.deleteBykey(costStructureItem.getControlAreaId(), costStructureItem.getCostStructureId(),
                    DateUtils.dateTime(costStructureItem.getDateFrom()), costStructureItem.getEvaluateAreaId());
        }
        return i;
    }

    @Override
    public PageDataInfo<CostStructureItem> list(CostStructureItem costStructureItem) {
        // 获取分页参数
        Page<CostStructureItem> page = PageUtils.getPage(CostStructureItem.class);
        IPage<CostStructureItem> iPage = costStructureItemMapper.list(page, costStructureItem);
        List<CostStructureItem> records = iPage.getRecords();
        return PageUtils.getPageInfo(records.stream().collect(Collectors.toList()), (int) iPage.getTotal());
    }

    @Override
    public PageDataInfo<CostStructureItem> overview(String menuId, String sort, SearchVo searchVo) {
        Page<CostStructureItem> page = PageUtils.getPage(CostStructureItem.class);
        String a = "";
        if (searchVo != null) {
            a = SearchUtils.parseSearchCondition("a", searchVo);
            a = a.replaceAll("a.control_area_desc", "controlArea.control_area_desc");
        }
        IPage<CostStructureItem> iPage = costStructureItemMapper.selectAll(page, sort, a);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<CostStructureItem> getAll() {
        return costStructureItemMapper.getAll();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateByKey(CostStructureItem costStructureItem) {
        if (costStructureHeadMapper.selectByKey(costStructureItem.getControlAreaId(), costStructureItem.getCostStructureId(), DateUtils.dateTime(costStructureItem.getDateFrom())) == null) {
            throw new ServiceException("成本组件头表不存在");
        }
        return costStructureItemMapper.updateByKey(costStructureItem);
    }

    @Override
    public CostStructureItem query(CostStructureItem costStructureItem) {
        return costStructureItemMapper.query(costStructureItem.getControlAreaId(), costStructureItem.getCostStructureId(), DateUtils.dateTime(costStructureItem.getDateFrom()));
    }

}
