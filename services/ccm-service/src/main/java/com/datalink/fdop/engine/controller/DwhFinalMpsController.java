package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwhFinalMps;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwhFinalMpsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwhFinalMps")
@Api(tags = "CCMS-MPS整合管理-定版视图")
public class DwhFinalMpsController extends BaseController {

    @Autowired
    private DwhFinalMpsService dwhFinalMpsService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "queryParam", value = "高级搜索及过滤条件", required = false, dataTypeClass = QueryParam.class, paramType = "body"),
    })
    @Log(title = "定版视图")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        PageDataInfo<DwhFinalMps> overview = dwhFinalMpsService.overview(verId, plantId, yearMonthFrom, yearMonthTo, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwhFinalMps.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "定版视图", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String verId = MapUtils.getAsString(params, "verId");
        String plantId = MapUtils.getAsString(params, "plantId");
        String yearMonthFrom = MapUtils.getAsString(params, "yearMonthFrom");
        String yearMonthTo = MapUtils.getAsString(params, "yearMonthTo");
        List<DwhFinalMps> list = dwhFinalMpsService.selectNoPage(verId, plantId,yearMonthFrom,yearMonthTo, sort, queryParam.getSearchVo());
        ExcelUtil<DwhFinalMps> util = new ExcelUtil<>(DwhFinalMps.class);
        util.exportExcel(response, list, "MPS数据管理-定版视图");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwhFinalMps> util = new ExcelUtil<>(DwhFinalMps.class);
        util.importTemplateExcel(response, "MPS数据管理-定版视图");
    }

    @ApiOperation(value = "数据集（SelectVo）")
    @GetMapping("/listVerId")
    public R<List<SelectVo>> listVerId() {
        return R.ok(dataDictionaryService.listVerId("dwh.dwh_final_mps", null));
    }

    @ApiOperation(value = "工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwh.dwh_final_mps", null));
    }

}
