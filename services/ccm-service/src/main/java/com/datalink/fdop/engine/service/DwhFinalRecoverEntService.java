package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalRecoverEnt;

import java.util.List;

public interface DwhFinalRecoverEntService extends IService<DwhFinalRecoverEnt> {

    PageDataInfo<DwhFinalRecoverEnt> overview(String plantId, String yearMonth, String sort, SearchVo searchVo);

    List<DwhFinalRecoverEnt> selectNoPage(String plantId, String yearMonth, String sort, SearchVo searchVo); // 新增方法

}