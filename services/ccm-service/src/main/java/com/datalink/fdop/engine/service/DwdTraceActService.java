package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.base.api.domain.DwdTraceAct;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;

import java.util.List;

public interface DwdTraceActService extends IService<DwdTraceAct> {

    PageDataInfo<DwdTraceAct> overview(String verId, String plantId,String productId,
                                       String  dateFrom,String  dateTo,
                                       String sort, SearchVo searchVo);

    List<DwdTraceAct> selectNoPage(String verId, String plantId,
                                   String productId, String dateFrom,
                                   String dateTo, String sort, SearchVo searchVo);
}
