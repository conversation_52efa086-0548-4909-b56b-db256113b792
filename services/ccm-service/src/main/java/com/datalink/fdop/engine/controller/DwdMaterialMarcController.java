package com.datalink.fdop.engine.controller;

import com.datalink.fdop.base.api.model.QueryParam;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.MapUtils;
import com.datalink.fdop.common.core.utils.MetaUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.engine.api.domain.DwdMaterialMarc;
import com.datalink.fdop.engine.service.DataDictionaryService;
import com.datalink.fdop.engine.service.DwdMaterialMarcService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-25 14:43
 */
@RestController
@RequestMapping("/base/dwdMaterialMarc")
@Api(tags = "CCMS-物料工厂信息")
public class DwdMaterialMarcController extends BaseController {

    @Autowired
    private DwdMaterialMarcService dwdMaterialMarcService;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("总览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "排序", required = false, dataType = "String", paramType = "query", defaultValue = "ASC"),
            @ApiImplicitParam(name = "searchVo", value = "高级搜索", required = false, dataTypeClass = SearchVo.class, paramType = "body"),
    })
    @Log(title = "物料工厂信息")
    @PostMapping(value = "/overview")
    public Object overview(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialId = MapUtils.getAsString(params, "materialId");
        String plantId = MapUtils.getAsString(params, "plantId");
        PageDataInfo<DwdMaterialMarc> overview = dwdMaterialMarcService.overview(materialId, plantId, sort, queryParam.getSearchVo());
        return R.ok(MetaUtils.getMetadata(DwdMaterialMarc.class, overview));
    }

    @ApiOperation(value = "导出数据")
    @Log(title = "物料工厂信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "ASC") String sort,
                       @RequestBody(required = false) QueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new QueryParam();
        }
        Map<String, Object> params = queryParam.getParams();
        String materialId = MapUtils.getAsString(params, "materialId");
        String plantId = MapUtils.getAsString(params, "plantId");
        List<DwdMaterialMarc> list = dwdMaterialMarcService.selectNoPage(materialId, plantId, sort, queryParam.getSearchVo());
        ExcelUtil<DwdMaterialMarc> util = new ExcelUtil<>(DwdMaterialMarc.class);
        util.exportExcel(response, list, "工厂信息");
    }

    @ApiOperation(value = "导出模板")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DwdMaterialMarc> util = new ExcelUtil<>(DwdMaterialMarc.class);
        util.importTemplateExcel(response, "工厂信息");
    }

    @ApiOperation(value = "查询工厂代码（SelectVo）")
    @GetMapping("/listPlantId")
    public R<List<SelectVo>> listPlantId() {
        return R.ok(dataDictionaryService.listPlantId("dwd.dwd_material_marc", null));
    }

    @ApiOperation("物料编码（SelectVo）")
    @GetMapping(value = "/listMaterialId")
    public R<List<SelectVo>> selectMaterialId() {
        return R.ok(dwdMaterialMarcService.lambdaQuery()
                .select(DwdMaterialMarc::getMaterialId, DwdMaterialMarc::getMaterialDesc)
                .groupBy(DwdMaterialMarc::getMaterialId, DwdMaterialMarc::getMaterialDesc)
                .orderByAsc(DwdMaterialMarc::getMaterialId).list().stream()
                .filter(dwdMaterialMarc -> dwdMaterialMarc != null && dwdMaterialMarc.getMaterialId() != null)
                .map(dwdMaterialMarc -> new SelectVo(dwdMaterialMarc.getMaterialId(), dwdMaterialMarc.getMaterialDesc()))
                .collect(Collectors.toList()));
    }

}
