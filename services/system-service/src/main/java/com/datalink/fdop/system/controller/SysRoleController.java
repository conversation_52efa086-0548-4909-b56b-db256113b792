package com.datalink.fdop.system.controller;

import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.annotation.RequiresPermissions;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.system.api.domain.SysPermissionColumn;
import com.datalink.fdop.system.api.domain.SysRole;
import com.datalink.fdop.system.api.domain.SysUser;
import com.datalink.fdop.system.api.domain.dto.RolePermissionColumnRequest;
import com.datalink.fdop.system.domain.SysUserRole;
import com.datalink.fdop.system.service.ISysRoleService;
import com.datalink.fdop.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/role")
@Api(tags = "角色API")
public class SysRoleController extends BaseController {

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysUserService userService;

    @ApiOperation(value = "获取角色列表")
   // @RequiresPermissions("system:role:list")
    @PostMapping("/list")
    public R<PageDataInfo<SysRole>> list(@RequestBody(required = false) SysRole role) {
        return R.ok(roleService.selectRoleList(role));
    }

    @ApiOperation(value = "导出角色")
    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
   // @RequiresPermissions("system:role:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRole role) {
        List<SysRole> list = roleService.selectRoleList(role).getTotalList();
        ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
        util.exportExcel(response, list, "角色数据");
    }

    @ApiOperation(value = "根据角色编号获取详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", dataType = "Long", required = true, paramType = "path"),
    })
   // @RequiresPermissions("system:role:query")
    @GetMapping(value = "/{roleId}")
    public R getInfo(@PathVariable("roleId") Long roleId) {
        return R.ok(roleService.selectRoleById(roleId));
    }

    @ApiOperation(value = "新增角色")
    //@RequiresPermissions("system:role:add")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody SysRole role) {
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return R.fail("新增角色'" + role.getRoleCode() + "'失败，角色编码已存在");
        }
        role.setCreateBy(SecurityUtils.getUsername());
        role.setDelFlag("0");
        return R.toResult(roleService.insertRole(role));
    }

    @ApiOperation(value = "修改保存角色")
   // @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@Validated @RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return R.fail("修改角色'" + role.getRoleCode() + "'失败，角色编码已存在");
        }
        role.setUpdateBy(SecurityUtils.getUsername());
        return R.toResult(roleService.updateRole(role));
    }

    @ApiOperation(value = "状态修改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "角色状态(0=正常,1=停用)", dataType = "String", paramType = "query"),
    })
    //@RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R changeStatus(@RequestParam("roleId") Long roleId, @RequestParam("status") String status) {
        SysRole role = new SysRole(roleId);
        roleService.checkRoleAllowed(role);
        role.setStatus(status);
        role.setUpdateBy(SecurityUtils.getUsername());
        return R.toResult(roleService.updateRoleStatus(role));
    }

    @ApiOperation(value = "删除角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleIds", value = "角色ID集合", required = true, allowMultiple = true, dataType = "Long", paramType = "body", example = "[1,2]"),
    })
    //@RequiresPermissions("system:role:remove")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping()
    public R remove(@RequestBody List<Long> roleIds) {
        return R.toResult(roleService.deleteRoleByIds(roleIds));
    }

    @ApiOperation(value = "获取角色选择框列表")
    //RequiresPermissions("system:role:query")
    @GetMapping("/optionselect")
    public R optionselect() {
        return R.ok(roleService.selectRoleAll());
    }

    @ApiOperation(value = "查询已分配用户角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "用户名", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "phonenumber", value = "手机号", dataType = "String", paramType = "query"),
    })
    @RequiresPermissions("system:role:list")
    @PostMapping("/authUser/allocatedList")
    public R<PageDataInfo> allocatedList(
            @RequestParam("roleId") Long roleId,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "phonenumber", required = false) String phonenumber
    ) {
        startPage();
        SysUser user = new SysUser();
        user.setRoleId(roleId);
        user.setUserName(userName);
        user.setPhonenumber(phonenumber);
        List<SysUser> list = userService.selectAllocatedList(user);
        return R.ok(getPageInfo(list));
    }

    @ApiOperation(value = "查询未分配用户角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "用户名", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "phonenumber", value = "手机号", dataType = "String", paramType = "query"),
    })
    //@RequiresPermissions("system:role:list")
    @PostMapping("/authUser/unallocatedList")
    public R<PageDataInfo> unallocatedList(
            @RequestParam("roleId") Long roleId,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "phonenumber", required = false) String phonenumber
    ) {
        startPage();
        SysUser user = new SysUser();
        user.setRoleId(roleId);
        user.setUserName(userName);
        user.setPhonenumber(phonenumber);
        List<SysUser> list = userService.selectUnallocatedList(user);
        return R.ok(getPageInfo(list));
    }

    @ApiOperation(value = "取消授权用户")
    //@RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
    public R cancelAuthUser(@RequestBody SysUserRole userRole) {
        return R.toResult(roleService.deleteAuthUser(userRole));
    }

    @ApiOperation(value = "批量选择用户授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "userIds", value = "用户id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body"),
    })
   // @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public R selectAuthUserAll(@RequestParam("roleId") Long roleId, @RequestBody List<Long> userIds) {
        return R.toResult(roleService.insertAuthUsers(roleId, userIds));
    }

    @ApiOperation(value = "批量取消授权用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "userIds", value = "用户id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "query"),
    })
    //@RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
    public R cancelAuthUserAll(@RequestParam("roleId") Long roleId, @RequestBody List<Long> userIds) {
        return R.toResult(roleService.deleteAuthUsers(roleId, userIds));
    }

    @ApiOperation(value = "角色授权菜单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "menuIds", value = "菜单id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body"),
    })
    //@RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authMenu/selectAll")
    public R selectAuthMenuAll(@RequestParam("roleId") Long roleId, @RequestBody List<Long> menuIds) {
        return R.toResult(roleService.insertMenuAuth(roleId, menuIds));
    }

    @ApiOperation(value = "获取角色权限列")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID，不传则返回所有权限列", required = false, dataType = "Long", paramType = "query"),
    })
    @RequiresPermissions("获取角色权限列")
    @GetMapping("/permissionColumns")
    public R getRolePermissionColumns(@RequestParam(required = false) Long roleId) {
        List<SysPermissionColumn> columns =
            roleService.getRolePermissionColumnsWithStatus(roleId);
        return R.ok(columns);
    }

    @ApiOperation(value = "为角色分配权限列")
    @RequiresPermissions("为角色分配权限列")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PostMapping("/assignPermissionColumns")
    public R assignPermissionColumns(@RequestBody RolePermissionColumnRequest request) {
        roleService.assignPermissionColumnsToRole(request.getRoleId(), request.getColumnNames());
        return R.ok("权限列分配成功");
    }

    @ApiOperation(value = "获取用户可访问的权限列")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path"),
    })
    @RequiresPermissions("获取用户可访问的权限列")
    @GetMapping("/accessibleColumns/{userId}")
    public R getAccessibleColumns(@PathVariable Long userId) {
        List<String> columns = roleService.getAccessibleColumnsByUserId(userId);
        return R.ok(columns);
    }

}