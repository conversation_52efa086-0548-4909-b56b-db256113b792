package com.datalink.fdop.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.UserConstants;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.age.SqlUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datascope.annotation.DataScope;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.system.api.domain.SysPermissionColumn;
import com.datalink.fdop.system.api.domain.SysRole;
import com.datalink.fdop.system.domain.SysRoleMenu;
import com.datalink.fdop.system.domain.SysUserRole;
import com.datalink.fdop.system.mapper.SysRoleDeptMapper;
import com.datalink.fdop.system.mapper.SysRoleMapper;
import com.datalink.fdop.system.mapper.SysRoleMenuMapper;
import com.datalink.fdop.system.mapper.SysUserRoleMapper;
import com.datalink.fdop.system.service.ISysMenuService;
import com.datalink.fdop.system.service.ISysRoleService;
import com.datalink.fdop.system.utils.DomainAgeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl implements ISysRoleService {
    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysRoleDeptMapper roleDeptMapper;

    @Autowired
    private ISysMenuService sysMenuService;

    @Autowired
    private com.datalink.fdop.system.mapper.SysRolePermissionColumnMapper rolePermissionColumnMapper;

    @Autowired
    private com.datalink.fdop.system.service.IPermissionColumnService permissionColumnService;

    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    @Override
    @DataScope(deptAlias = "d")
    public PageDataInfo<SysRole> selectRoleList(SysRole role) {
        // 获取分页参数
        Page<VlabelItem> page = PageUtils.getPage(VlabelItem.class);
        IPage<VlabelItem<SysRole>> vlabelItemIPage = roleMapper.selectRoleList(page, role);

        return PageUtils.getPageInfo(vlabelItemIPage.getRecords().stream().map(VlabelItem::getProperties).collect(Collectors.toList()), (int) vlabelItemIPage.getTotal());
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRolesByUserId(Long userId) {
        List<VlabelItem<SysRole>> vlabelItems = roleMapper.selectRolePermissionByUserId(userId);
        List<SysRole> userRoles = vlabelItems.stream().map(map -> map.getProperties()).collect(Collectors.toList());
        return userRoles;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<VlabelItem<SysRole>> vlabelItems = roleMapper.selectRolePermissionByUserId(userId);
        List<SysRole> perms = vlabelItems.stream().map(map -> map.getProperties()).collect(Collectors.toList());
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms) {
            if (StringUtils.isNotNull(perm)) {
                permsSet.addAll(Arrays.asList(perm.getRoleCode().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll() {
        // 获取所有
        Page<VlabelItem> page = new Page<>(1, Integer.MAX_VALUE);
        SysRole sysRole = new SysRole();
        sysRole.setStatus("0");
        IPage<VlabelItem<SysRole>> vlabelItemIPage = roleMapper.selectRoleList(page, sysRole);
        return vlabelItemIPage.getRecords().stream().map(VlabelItem::getProperties).collect(Collectors.toList());
    }

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    @Override
    public List<Long> selectRoleListByUserId(Long userId) {
        return roleMapper.selectRoleListByUserId(userId);
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRole selectRoleById(Long roleId) {
        return roleMapper.selectRoleById(roleId).getProperties();
    }

    @Override
    public SysRole selectRoleByCode(String roleCode) {
        VlabelItem<SysRole> sysRoleVlabelItem = roleMapper.selectRoleByCode(roleCode);
        if (sysRoleVlabelItem == null) {
            return null;
        }
        return sysRoleVlabelItem.getProperties();
    }

    @Override
    public List<SysRole> selectRoleByCodes(List<String> roleCodes) {
        List<VlabelItem<SysRole>> sysRoleVlabelItem = roleMapper.selectRoleByCodes(roleCodes);
        return sysRoleVlabelItem.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public String checkRoleNameUnique(SysRole role) {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        VlabelItem<SysRole> vlabelItem = roleMapper.checkRoleCodeUnique(role.getRoleCode());
        if (vlabelItem == null) {
            return UserConstants.UNIQUE;
        }
        SysRole info = vlabelItem.getProperties();
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    @Override
    public void checkRoleAllowed(SysRole role) {
        if (StringUtils.isNotNull(role.getRoleId()) && role.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员角色");
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public int countUserRoleByRoleId(Long roleId) {
        return userRoleMapper.countUserRoleByRoleId(roleId);
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRole(SysRole role) {
        role.setRoleId(IdWorker.getId());
        // 新增角色信息
        roleMapper.insertRole(DomainAgeUtils.getRoleAgeString(role));
        return insertRoleMenu(role);
    }

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRole(SysRole role) {
        // 修改角色信息
        roleMapper.updateRole(role);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        return insertRoleMenu(role);
    }

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public int updateRoleStatus(SysRole role) {
        return roleMapper.updateRole(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int insertRoleMenu(SysRole role) {
        int rows = 1;
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
        if (ArrayUtils.isEmpty(role.getMenuIds())) {
            return rows;
        }
        for (Long menuId : role.getMenuIds()) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (list.size() > 0) {
            rows = roleMenuMapper.batchRoleMenu(Arrays.asList(role.getRoleId()), SqlUtils.getInSql(Arrays.asList(role.getMenuIds())));
        }
        return rows;
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param role 角色对象
     */
    // public int insertRoleDept(SysRole role) {
    //     int rows = 1;
    //     // 新增角色与部门（数据权限）管理
    //     List<SysRoleDept> list = new ArrayList<SysRoleDept>();
    //     for (Long deptId : role.getDeptIds()) {
    //         SysRoleDept rd = new SysRoleDept();
    //         rd.setRoleId(role.getRoleId());
    //         rd.setDeptId(deptId);
    //         list.add(rd);
    //     }
    //     if (list.size() > 0) {
    //         rows = roleDeptMapper.batchRoleDept(list);
    //     }
    //     return rows;
    // }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleById(Long roleId) {
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        // 删除角色与部门关联
        // roleDeptMapper.deleteRoleDeptByRoleId(roleId);
        return roleMapper.deleteRoleById(roleId);
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleByIds(List<Long> roleIds) {
        for (Long roleId : roleIds) {
            checkRoleAllowed(new SysRole(roleId));
            SysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleCode()));
            }
        }
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenu(roleIds);
        // 删除角色与部门关联
        // roleDeptMapper.deleteRoleDept(roleIds);
        return roleMapper.deleteRoleByIds(roleIds);
    }

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAuthUser(SysUserRole userRole) {
        return userRoleMapper.deleteUserRoleInfo(userRole);
    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAuthUsers(Long roleId, List<Long> userIds) {
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<SysUserRole>();
        for (Long userId : userIds) {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        return userRoleMapper.batchUserRole(list);
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAuthUsers(Long roleId, List<Long> userIds) {
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<SysUserRole>();
        for (Long userId : userIds) {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        return userRoleMapper.batchDeleteUserRole(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertMenuAuth(Long roleId, List<Long> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return roleMenuMapper.batchDeleteRoleMenu(Arrays.asList(roleId), null);
        }
        // 新增角色与菜单的关系
        // List<Long> updateMenuIds = new ArrayList<>();
        // for (Long menuId : menuIds) {
        //     Set<Long> childMenuIds = sysMenuService.getChildMenuIdsByMenuId(menuId);
        //     if (CollectionUtils.isEmpty(childMenuIds)) {
        //         updateMenuIds.add(menuId);
        //     }
        //     updateMenuIds.addAll(childMenuIds);
        // }
        // if (CollectionUtils.isEmpty(updateMenuIds)) {
        //     throw new ServiceException("菜单不存在");
        // }
        // 先删除角色和菜单的关系
        roleMenuMapper.batchDeleteRoleMenu(Arrays.asList(roleId), null);
        // 再添加角色和菜单的关系
        return roleMenuMapper.batchRoleMenu(Arrays.asList(roleId), SqlUtils.getInSql(menuIds));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignPermissionColumnsToRole(Long roleId, List<String> columnNames) {
        // 1. 先删除角色现有的权限列关联
        rolePermissionColumnMapper.deleteRolePermissionColumns(roleId);

        // 2. 如果有新的权限列，则创建关联
        if (columnNames != null && !columnNames.isEmpty()) {
            return rolePermissionColumnMapper.batchAssignRolePermissionColumns(roleId, columnNames);
        }

        return 0;
    }

    @Override
    public List<SysPermissionColumn> getRolePermissionColumnsWithStatus(Long roleId) {
        // 直接调用Service，roleId为null时返回所有权限列，不为null时返回该角色的权限列
        return permissionColumnService.getPermissionColumnsByRoleId(roleId);
    }

    @Override
    public List<String> getAccessibleColumnsByUserId(Long userId) {
        return rolePermissionColumnMapper.selectPermissionColumnsByUserId(userId);
    }
}
