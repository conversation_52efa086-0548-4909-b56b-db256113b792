package com.datalink.fdop.system.service;

import com.datalink.fdop.system.api.domain.SysPermissionColumn;
import com.datalink.fdop.system.api.domain.dto.PermissionColumnDTO;

import java.util.List;

/**
 * 权限列服务接口
 */
public interface IPermissionColumnService {
    
    /**
     * 扫描实体获取权限列
     */
    List<PermissionColumnDTO> scanPermissionColumns();
    
    /**
     * 根据模块获取权限列
     */
    List<PermissionColumnDTO> getPermissionColumnsByModule(String module);
    
    /**
     * 同步权限列到数据库
     */
    int syncPermissionColumnsToDatabase();
    
    /**
     * 获取所有权限列
     */
    List<SysPermissionColumn> getAllPermissionColumns();
    
    /**
     * 更新权限列显示状态
     */
    int updatePermissionColumnVisible(String columnName, Boolean visible);
    
    /**
     * 根据角色ID获取权限列
     */
    List<SysPermissionColumn> getPermissionColumnsByRoleId(Long roleId);
}
