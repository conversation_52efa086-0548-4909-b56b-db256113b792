package com.datalink.fdop.system.controller;

import com.datalink.fdop.common.core.web.domain.R;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.annotation.RequiresPermissions;
import com.datalink.fdop.system.api.domain.SysPermissionColumn;
import com.datalink.fdop.system.api.domain.dto.PermissionColumnDTO;
import com.datalink.fdop.system.service.IPermissionColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限列控制器
 */
@RestController
@RequestMapping("/system/permissionColumn")
@Api(tags = "权限列管理")
public class PermissionColumnController {
    
    @Autowired
    private IPermissionColumnService permissionColumnService;
    
    @GetMapping("/scan")
    @ApiOperation("扫描实体获取权限列")
    @RequiresPermissions("system:permissionColumn:scan")
    public R<List<PermissionColumnDTO>> scan(
            @ApiParam("模块名称") @RequestParam(required = false) String module) {
        List<PermissionColumnDTO> list;
        if (module != null && !module.trim().isEmpty()) {
            list = permissionColumnService.getPermissionColumnsByModule(module);
        } else {
            list = permissionColumnService.scanPermissionColumns();
        }
        return R.ok(list);
    }
    
    @PostMapping("/sync")
    @ApiOperation("同步权限列到数据库")
    @RequiresPermissions("system:permissionColumn:sync")
    @Log(title = "权限列管理", businessType = BusinessType.INSERT)
    public R sync() {
        int count = permissionColumnService.syncPermissionColumnsToDatabase();
        return R.ok("同步完成，共同步 " + count + " 个权限列");
    }
    
    @GetMapping("/list")
    @ApiOperation("获取所有权限列")
    @RequiresPermissions("system:permissionColumn:list")
    public R<List<SysPermissionColumn>> list() {
        List<SysPermissionColumn> list = permissionColumnService.getAllPermissionColumns();
        return R.ok(list);
    }
    
    @PutMapping("/visible")
    @ApiOperation("更新权限列显示状态")
    @RequiresPermissions("system:permissionColumn:edit")
    @Log(title = "权限列管理", businessType = BusinessType.UPDATE)
    public R updateVisible(
            @ApiParam("列名") @RequestParam String columnName, 
            @ApiParam("是否显示") @RequestParam Boolean visible) {
        permissionColumnService.updatePermissionColumnVisible(columnName, visible);
        return R.ok("显示状态更新成功");
    }
}
