package com.datalink.fdop.system.mapper;

import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 角色权限列关联Mapper
 */
public interface SysRolePermissionColumnMapper {
    
    /**
     * 批量分配角色权限列
     */
    int batchAssignRolePermissionColumns(@Param("roleId") Long roleId, @Param("columnNames") List<String> columnNames);
    
    /**
     * 删除角色的所有权限列
     */
    int deleteRolePermissionColumns(@Param("roleId") Long roleId);
    
    /**
     * 根据用户ID查询可访问的权限列
     */
    List<String> selectPermissionColumnsByUserId(@Param("userId") Long userId);
}
