package com.datalink.fdop.system.mapper;

import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.system.api.domain.SysPermissionColumn;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限列Mapper
 */
public interface SysPermissionColumnMapper {
    
    /**
     * 查询所有权限列
     */
    List<VlabelItem<SysPermissionColumn>> selectAllPermissionColumns();
    
    /**
     * 根据角色ID查询权限列
     */
    List<VlabelItem<SysPermissionColumn>> selectPermissionColumnsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 批量插入权限列
     */
    int batchInsertPermissionColumns(@Param("columns") List<SysPermissionColumn> columns);
    
    /**
     * 更新权限列显示状态
     */
    int updatePermissionColumnVisible(@Param("columnName") String columnName, @Param("visible") Boolean visible);
    
    /**
     * 根据列名查询权限列
     */
    VlabelItem<SysPermissionColumn> selectPermissionColumnByName(@Param("columnName") String columnName);
    
    /**
     * 清空所有权限列
     */
    int deleteAllPermissionColumns();
}
