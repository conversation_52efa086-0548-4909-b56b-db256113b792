package com.datalink.fdop.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.common.security.annotation.PermissionEntity;
import com.datalink.fdop.system.api.domain.SysPermissionColumn;
import com.datalink.fdop.system.api.domain.dto.PermissionColumnDTO;
import com.datalink.fdop.system.mapper.SysPermissionColumnMapper;
import com.datalink.fdop.system.service.IPermissionColumnService;
import io.swagger.annotations.ApiModelProperty;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限列服务实现
 */
@Service
public class PermissionColumnServiceImpl implements IPermissionColumnService {
    
    private static final Logger log = LoggerFactory.getLogger(PermissionColumnServiceImpl.class);
    
    // CCMS实体所在包路径
    private static final String CCMS_PACKAGE = "com.datalink.fdop.engine.api.domain";
    
    @Autowired
    private SysPermissionColumnMapper permissionColumnMapper;
    
    @Override
    public List<PermissionColumnDTO> scanPermissionColumns() {
        // 用Map存储字段名和描述，自动去重
        Map<String, String> fieldDescMap = new HashMap<>();
        
        try {
            // 使用Reflections扫描包
            Reflections reflections = new Reflections(CCMS_PACKAGE);
            Set<Class<?>> annotatedClasses = reflections.getTypesAnnotatedWith(PermissionEntity.class);
            
            for (Class<?> clazz : annotatedClasses) {
                // 获取类的所有字段
                Field[] fields = clazz.getDeclaredFields();
                
                for (Field field : fields) {
                    // 过滤掉静态字段和常量
                    if (Modifier.isStatic(field.getModifiers()) || 
                        Modifier.isFinal(field.getModifiers())) {
                        continue;
                    }
                    
                    // 获取@ApiModelProperty注解
                    ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
                    if (apiModelProperty != null) {
                        String fieldName = field.getName();
                        String fieldDescription = apiModelProperty.value();
                        
                        // 如果已存在该字段名，保留第一个找到的描述
                        fieldDescMap.putIfAbsent(fieldName, fieldDescription);
                    }
                }
            }
            
            log.info("扫描到 {} 个唯一字段", fieldDescMap.size());
            
        } catch (Exception e) {
            log.error("扫描权限列失败", e);
        }
        
        // 转换为DTO列表并排序
        return fieldDescMap.entrySet().stream()
                .map(entry -> new PermissionColumnDTO(
                    entry.getKey(), 
                    entry.getValue()
                ))
                .sorted(Comparator.comparing(PermissionColumnDTO::getColumnName))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<PermissionColumnDTO> getPermissionColumnsByModule(String module) {
        if (module == null || module.trim().isEmpty()) {
            return scanPermissionColumns();
        }
        
        Map<String, String> fieldDescMap = new HashMap<>();
        
        try {
            Reflections reflections = new Reflections(CCMS_PACKAGE);
            Set<Class<?>> annotatedClasses = reflections.getTypesAnnotatedWith(PermissionEntity.class);
            
            for (Class<?> clazz : annotatedClasses) {
                PermissionEntity annotation = clazz.getAnnotation(PermissionEntity.class);
                
                // 只处理指定模块的实体
                if (!module.equals(annotation.module())) {
                    continue;
                }
                
                Field[] fields = clazz.getDeclaredFields();
                
                for (Field field : fields) {
                    if (Modifier.isStatic(field.getModifiers()) || 
                        Modifier.isFinal(field.getModifiers())) {
                        continue;
                    }
                    
                    // 获取@ApiModelProperty注解
                    ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
                    if (apiModelProperty != null) {
                        String fieldName = field.getName();
                        String fieldDescription = apiModelProperty.value();
                        
                        fieldDescMap.putIfAbsent(fieldName, fieldDescription);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("按模块扫描权限列失败", e);
        }
        
        return fieldDescMap.entrySet().stream()
                .map(entry -> new PermissionColumnDTO(
                    entry.getKey(), 
                    entry.getValue()
                ))
                .sorted(Comparator.comparing(PermissionColumnDTO::getColumnName))
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncPermissionColumnsToDatabase() {
        // 1. 扫描实体获取权限列
        List<PermissionColumnDTO> scannedColumns = scanPermissionColumns();
        
        // 2. 清空现有权限列
        permissionColumnMapper.deleteAllPermissionColumns();
        
        // 3. 转换为实体并批量插入
        List<SysPermissionColumn> columns = scannedColumns.stream()
                .map(dto -> new SysPermissionColumn(dto.getColumnName(), dto.getColumnDescription()))
                .collect(Collectors.toList());
        
        if (!columns.isEmpty()) {
            return permissionColumnMapper.batchInsertPermissionColumns(columns);
        }
        
        return 0;
    }
    
    @Override
    public List<SysPermissionColumn> getAllPermissionColumns() {
        List<VlabelItem<SysPermissionColumn>> vlabelItems = permissionColumnMapper.selectAllPermissionColumns();
        return vlabelItems.stream()
                .map(VlabelItem::getProperties)
                .collect(Collectors.toList());
    }
    
    @Override
    public int updatePermissionColumnVisible(String columnName, Boolean visible) {
        return permissionColumnMapper.updatePermissionColumnVisible(columnName, visible);
    }
    
    @Override
    public List<SysPermissionColumn> getPermissionColumnsByRoleId(Long roleId) {
        // roleId为null时返回所有权限列，不为null时返回该角色的权限列
        List<VlabelItem<SysPermissionColumn>> vlabelItems = permissionColumnMapper.selectPermissionColumnsByRoleId(roleId);
        return vlabelItems.stream()
                .map(VlabelItem::getProperties)
                .collect(Collectors.toList());
    }
}
