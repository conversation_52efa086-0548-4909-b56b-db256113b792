package com.datalink.fdop.system.service.impl;

import java.util.HashSet;
import java.util.Set;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.permissions.api.RemotePermissionLogicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.datalink.fdop.system.api.domain.SysUser;
import com.datalink.fdop.system.service.ISysMenuService;
import com.datalink.fdop.system.service.ISysPermissionService;
import com.datalink.fdop.system.service.ISysRoleService;

@Service
public class SysPermissionServiceImpl implements ISysPermissionService {
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private RemotePermissionLogicService remotePermissionLogicService;

    /**
     * 获取角色数据权限
     *
     * @param userId 用户Id
     * @return 角色权限信息
     */
    @Override
    public Set<String> getRolePermission(Long userId) {
        Set<String> roles = new HashSet<String>();
        // 管理员拥有所有权限
        if (checkPermission(userId)) {
            roles.add("admin");
        } else {
            roles.addAll(roleService.selectRolePermissionByUserId(userId));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     *
     * @param userId 用户Id
     * @return 菜单权限信息
     */
    @Override
    public Set<String> getMenuPermission(Long userId) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (checkPermission(userId)) {
            perms.add("*:*:*");
        } else {
            perms.addAll(menuService.selectMenuPermsByUserId(userId));
        }
        return perms;
    }

    /**
     * 检查是否有超级管理员权限 超级管理员用户/超级管理员角色
     */
    private Boolean checkPermission(Long userId) {
        R<Boolean> r = remotePermissionLogicService.checkPermissionByUserId(userId);
        boolean checkPermission;
        if (r.getCode() == R.SUCCESS) {
            checkPermission = r.getData();
        } else {
            throw new RuntimeException(r.getMsg());
        }
        return checkPermission;
    }
}
