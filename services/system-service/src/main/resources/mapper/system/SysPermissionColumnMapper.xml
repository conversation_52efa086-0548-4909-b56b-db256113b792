<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.system.mapper.SysPermissionColumnMapper">

    <resultMap type="com.datalink.fdop.common.mybatis.model.VlabelItem" id="vlabelItem">
        <result property="id" column="id"/>
        <result property="properties" column="properties"
                javaType="com.datalink.fdop.system.api.domain.SysPermissionColumn"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="selectAllPermissionColumns" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (c:sys_permission_column)
            RETURN id(c), properties(c)
            ORDER BY c.columnName
        $$) as (id ag_catalog.agtype, properties ag_catalog.agtype)
    </select>

    <select id="selectPermissionColumnsByRoleId" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (r:sys_role)-[:sys_role_permission_column]->(c:sys_permission_column)
            WHERE r.roleId = ${roleId}
            RETURN id(c), properties(c)
            ORDER BY c.columnName
        $$) as (id ag_catalog.agtype, properties ag_catalog.agtype)
    </select>

    <select id="updatePermissionColumnVisible" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (c:sys_permission_column)
            WHERE c.columnName = '${columnName}'
            SET c.visible = ${visible}
            RETURN c
        $$) as (c ag_catalog.agtype)
    </select>

    <select id="selectPermissionColumnByName" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (c:sys_permission_column)
            WHERE c.columnName = '${columnName}'
            RETURN id(c), properties(c)
            LIMIT 1
        $$) as (id ag_catalog.agtype, properties ag_catalog.agtype)
    </select>

    <select id="batchInsertPermissionColumns" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        <foreach collection="columns" item="column" separator=" ">
            CREATE(:sys_permission_column {
                columnName: '${column.columnName}',
                columnDescription: '${column.columnDescription}',
                visible: ${column.visible}
            })
        </foreach>
        RETURN 1
        $$) as (result ag_catalog.agtype)
    </select>

    <select id="deleteAllPermissionColumns" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (c:sys_permission_column)
            DELETE c
            RETURN count(c)
        $$) as (count ag_catalog.agtype)
    </select>

</mapper>
