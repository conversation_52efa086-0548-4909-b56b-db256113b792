<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.system.mapper.SysRolePermissionColumnMapper">

    <select id="batchAssignRolePermissionColumns" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (r:sys_role), (c:sys_permission_column)
        WHERE r.roleId = ${roleId} AND c.columnName IN [
        <foreach collection="columnNames" item="columnName" separator=",">
            '${columnName}'
        </foreach>
        ]
        CREATE ((r)-[:sys_role_permission_column]->(c))
        RETURN r, c
        $$) as (r ag_catalog.agtype, c ag_catalog.agtype)
    </select>

    <select id="deleteRolePermissionColumns" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (r:sys_role)-[rel:sys_role_permission_column]->(c:sys_permission_column)
        WHERE r.roleId = ${roleId}
        DELETE rel
        RETURN count(rel)
        $$) as (count ag_catalog.agtype)
    </select>

    <select id="selectPermissionColumnsByUserId" resultType="string">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (u:sys_user)-[:sys_user_role]->(r:sys_role)-[:sys_role_permission_column]->(c:sys_permission_column)
            WHERE u.userId = ${userId} AND c.visible = true
            RETURN DISTINCT c.columnName
        $$) as (columnName ag_catalog.agtype)
    </select>

</mapper>
