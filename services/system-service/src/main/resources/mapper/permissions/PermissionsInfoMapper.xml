<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.permissions.mapper.PermissionsInfoMapper">

    <sql id="permissionsInfo">
        <choose>
            <when test="${permissionsInfoType} == 'PAGE'">
                b_p_page
            </when>
            <when test="${permissionsInfoType} == 'COMPONENT'">
                b_p_component
            </when>
            <when test="${permissionsInfoType} == 'BUTTON'">
                b_p_button
            </when>
            <when test="${permissionsInfoType} == 'INTERFACE'">
                b_p_interface
            </when>
            <when test="${permissionsInfoType} == 'MAPPER'">
                b_p_mapper
            </when>
            <when test="${permissionsInfoType} == 'FIELD'">
                b_p_field
            </when>
        </choose>
    </sql>

    <sql id="return">
        RETURN ${alias}.id, ${alias}.module,
            ${alias}.permissionsInfoType,
            ${alias}.pageId,
            ${alias}.pageIdName,
            ${alias}.componentId,
            ${alias}.componentIdName,
            ${alias}.buttonId,
            ${alias}.buttonIdName,
            ${alias}.interfaceAddress,
            ${alias}.interfaceAddressName,
            ${alias}.mapperId,
            ${alias}.mapperIdName,
            ${alias}.isUnderline,
            ${alias}.field,
            ${alias}.fieldName
            $$) as (id BIGINT, module TEXT,
            permissionsInfoType TEXT,
            pageId TEXT,
            pageIdName TEXT,
            componentId TEXT,
            componentIdName TEXT,
            buttonId TEXT,
            buttonIdName TEXT,
            interfaceAddress TEXT,
            interfaceAddressName TEXT,
            mapperId TEXT,
            mapperIdName TEXT,
            isUnderline BOOLEAN,
            field TEXT,
            fieldName TEXT)
    </sql>

    <select id="checkPermissionsInfo" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (info:
        <include refid="permissionsInfo">
            <property name="permissionsInfoType" value="permissionsInfoType.toString()"/>
        </include>
        )
        WHERE info.moduleCode = '${moduleCode}'
        <if test="id != null">AND info.id &lt;&gt; ${id}</if>
        <if test="pageId != null and pageId != ''">AND info.pageId = '${pageId}'</if>
        <if test="componentIdName != null and componentIdName != ''">AND info.componentIdName = '${componentIdName}'
        </if>
        <if test="buttonIdName != null and buttonIdName != ''">AND info.buttonIdName = '${buttonIdName}'</if>
        <if test="interfaceAddress != null and interfaceAddress != ''">AND info.interfaceAddress =
            '${interfaceAddress}'
        </if>
        <if test="mapperId != null and mapperId != ''">AND info.mapperId = '${mapperId}'</if>
        <if test="field != null and field != ''">AND info.field = '${field}'</if>
        <include refid="return">
            <property name="alias" value="info"/>
        </include>
    </select>

    <select id="insertPermissionsInfo" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',$$
        <foreach collection="permissionsInfoList" item="permissionsInfo" index="i">
            CREATE (info${i}:
            <include refid="permissionsInfo">
                <property name="permissionsInfoType" value="permissionsInfo.permissionsInfoType.toString()"/>
            </include>
            ${@com.datalink.fdop.permissions.utils.DomainAgeUtils@getPermissionsInfoAgeStr(permissionsInfo)})
        </foreach>
        RETURN id(info0), properties(info0)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updatePermissionsInfo" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (info:
        <include refid="permissionsInfo">
            <property name="permissionsInfoType" value="permissionsInfoType.toString()"/>
        </include>
        )
        WHERE info.id = ${id}
        <set>
            <if test="moduleCode != null and moduleCode != ''">info.moduleCode = '${moduleCode}',</if>
            <if test="moduleName != null and moduleName != ''">info.moduleName = '${moduleName}',</if>
            <if test="pageId != null and pageId != ''">info.pageId = '${pageId}',</if>
            <if test="pageIdName != null and pageIdName != ''">info.pageIdName = '${pageIdName}',</if>
            <if test="componentId != null and componentId != ''">info.componentId = '${componentId}',</if>
            <if test="componentIdName != null and componentIdName != ''">info.componentIdName = '${componentIdName}',
            </if>
            <if test="buttonId != null and buttonId != ''">info.buttonId = '${buttonId}',</if>
            <if test="buttonIdName != null and buttonIdName != ''">info.buttonIdName = '${buttonIdName}',</if>
            <if test="interfaceAddress != null and interfaceAddress != ''">info.interfaceAddress =
                '${interfaceAddress}',
            </if>
            <if test="interfaceAddressName != null and interfaceAddressName != ''">info.interfaceAddressName =
                '${interfaceAddressName}',
            </if>
            <if test="mapperId != null and mapperId != ''">info.mapperId = '${mapperId}',</if>
            <if test="mapperIdName != null and mapperIdName != ''">info.mapperIdName = '${mapperIdName}',</if>
            <if test="isUnderline != null">info.isUnderline = ${isUnderline},</if>
            <if test="field != null and field != ''">info.field = '${field}',</if>
            <if test="fieldName != null and fieldName != ''">info.fieldName = '${fieldName}',</if>
        </set>
        RETURN id(info), properties(info)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deletePermissionsInfoBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (info:
        <include refid="permissionsInfo">
            <property name="permissionsInfoType" value="permissionsInfoType.toString()"/>
        </include>
        )
        WHERE info.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE info RETURN id(info),properties(info) $$) as (id
        ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (info:
        <include refid="permissionsInfo">
            <property name="permissionsInfoType" value="permissionsInfoType.toString()"/>
        </include>
        )
        WHERE info.id = ${id}
        <include refid="return">
            <property name="alias" value="info"/>
        </include>
    </select>

    <select id="selectPermissionsInfoList" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (info:
        <include refid="permissionsInfo">
            <property name="permissionsInfoType" value="permissionsInfoType.toString()"/>
        </include>
        )
        WHERE info.moduleCode = '${moduleCode}'
        <include refid="return">
            <property name="alias" value="info"/>
        </include>
    </select>

    <sql id="createPermissionsInfoEdge1">
        <choose>
            <when test="${sourcePermissionsInfoType} == 'PAGE' and ${targetPermissionsInfoType} == 'COMPONENT'">
                MATCH (page:b_p_page), (component:b_p_component)
                WHERE page.id = ${permissionsInfoId}
                AND component.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                CREATE (page)-[edge:b_p_page_component_edge]->(component)
            </when>
            <when test="${sourcePermissionsInfoType} == 'COMPONENT' and ${targetPermissionsInfoType} == 'FIELD'">
                MATCH (component:b_p_component), (field:b_p_field)
                WHERE component.id = ${permissionsInfoId}
                AND field.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                CREATE (component)-[edge:b_p_component_field_edge]->(field)
            </when>
            <when test="${sourcePermissionsInfoType} == 'PAGE' and ${targetPermissionsInfoType} == 'BUTTON'">
                MATCH (page:b_p_page), (button:b_p_button)
                WHERE page.id = ${permissionsInfoId}
                AND button.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                CREATE (page)-[edge:b_p_page_button_edge]->(button)
            </when>
            <when test="${sourcePermissionsInfoType} == 'BUTTON' and ${targetPermissionsInfoType} == 'INTERFACE'">
                MATCH (button:b_p_button),(interface:b_p_interface)
                WHERE button.id = ${permissionsInfoId}
                AND interface.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                CREATE (button)-[edge:b_p_button_interface_edge]->(interface)
            </when>
            <when test="${sourcePermissionsInfoType} == 'INTERFACE' and ${targetPermissionsInfoType} == 'MAPPER'">
                MATCH (interface:b_p_interface), (mapper:b_p_mapper)
                WHERE interface.id = ${permissionsInfoId}
                AND mapper.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                CREATE (interface)-[edge:b_p_interface_mapper_edge]->(mapper)
                CREATE (mapper)-[:b_p_interface_mapper_edge]->(interface)
            </when>
            <when test="${sourcePermissionsInfoType} == 'MAPPER' and ${targetPermissionsInfoType} == 'FIELD'">
                MATCH (mapper:b_p_mapper), (field:b_p_field)
                WHERE mapper.id = ${permissionsInfoId}
                AND field.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                CREATE (mapper)-[edge:b_p_mapper_field_edge]->(field)
            </when>
        </choose>
    </sql>

    <select id="createPermissionsInfoEdge" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        <include refid="createPermissionsInfoEdge1">
            <property name="permissionsInfoId" value="${permissionsInfoId}"/>
            <property name="sourcePermissionsInfoType" value="sourcePermissionsInfoType.toString()"/>
            <property name="targetPermissionsInfoType" value="targetPermissionsInfoType.toString()"/>
            <property name="ids" value="ids"/>
        </include>
        RETURN id(edge),properties(edge) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <sql id="deletePermissionsInfoEdge">
        <choose>
            <when test="${sourcePermissionsInfoType} == 'PAGE' and ${targetPermissionsInfoType} == 'COMPONENT'">
                MATCH (page:b_p_page)-[edge]-(component:b_p_component)
                WHERE page.id = ${permissionsInfoId}
                AND component.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge
                RETURN id(edge),properties(edge) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype);
                SELECT count(1)
                FROM ag_catalog.cypher('zjdata_graph', $$
                MATCH (page:b_p_page)-[edge]-(component:b_p_component)-[edge1]-(field:b_p_field)
                WHERE page.id = ${permissionsInfoId}
                AND component.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge1
            </when>
            <when test="${sourcePermissionsInfoType} == 'COMPONENT' and ${targetPermissionsInfoType} == 'FIELD'">
                MATCH (component:b_p_component)-[edge]-(field:b_p_field)
                WHERE component.id = ${permissionsInfoId}
                AND field.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge
            </when>
            <when test="${sourcePermissionsInfoType} == 'PAGE' and ${targetPermissionsInfoType} == 'BUTTON'">
                MATCH (page:b_p_page)-[edge]-(button:b_p_button)
                WHERE page.id = ${permissionsInfoId}
                AND button.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge
                RETURN id(edge),properties(edge) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype);
                SELECT count(1)
                FROM ag_catalog.cypher('zjdata_graph', $$
                MATCH (page:b_p_page)-[edge]-(button:b_p_button)-[edge1]-(interface:b_p_interface)
                WHERE page.id = ${permissionsInfoId}
                AND button.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge1
            </when>
            <when test="${sourcePermissionsInfoType} == 'BUTTON' and ${targetPermissionsInfoType} == 'INTERFACE'">
                MATCH (button:b_p_button)-[edge]-(interface:b_p_interface)
                WHERE button.id = ${permissionsInfoId}
                AND interface.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge
            </when>
            <when test="${sourcePermissionsInfoType} == 'INTERFACE' and ${targetPermissionsInfoType} == 'MAPPER'">
                MATCH (interface:b_p_interface)-[edge]-(mapper:b_p_mapper)
                WHERE interface.id = ${permissionsInfoId}
                AND mapper.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge
                RETURN id(edge),properties(edge) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype);
                SELECT count(1)
                FROM ag_catalog.cypher('zjdata_graph', $$
                MATCH (interface:b_p_interface)-[edge]-(mapper:b_p_mapper)-[edge1]-(field:b_p_field)
                WHERE interface.id = ${permissionsInfoId}
                AND mapper.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge1
            </when>
            <when test="${sourcePermissionsInfoType} == 'MAPPER' and ${targetPermissionsInfoType} == 'FIELD'">
                MATCH (mapper:b_p_mapper)-[edge]-(field:b_p_field)
                WHERE mapper.id = ${permissionsInfoId}
                AND field.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
                DELETE edge
            </when>
        </choose>
    </sql>

    <select id="deletePermissionsInfoEdge" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        <include refid="deletePermissionsInfoEdge">
            <property name="permissionsInfoId" value="${permissionsInfoId}"/>
            <property name="sourcePermissionsInfoType" value="sourcePermissionsInfoType.toString()"/>
            <property name="targetPermissionsInfoType" value="targetPermissionsInfoType.toString()"/>
            <property name="ids" value="ids"/>
        </include>
        RETURN id(edge),properties(edge) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <sql id="treeCommonField">
        ${id} as ${alias}InfoId,
        ${moduleCode} as ${alias}ModuleCode,
        ${moduleName} as ${alias}ModuleName,
        ${permissionsInfoType} as ${alias}InfoType
    </sql>

    <sql id="returnFrontTreeField">
        $$
        ) as (
        pageInfoId BIGINT,pageModuleCode TEXT,pageModuleName TEXT,pageInfoType TEXT,pageId TEXT,pageIdName TEXT,
        componentInfoId BIGINT,componentModuleCode TEXT,componentModuleName TEXT,componentInfoType TEXT,componentId TEXT,componentIdName TEXT,
        fieldInfoId BIGINT,fieldModuleCode TEXT,fieldModuleName TEXT,fieldInfoType TEXT,field TEXT,fieldName TEXT
        )
    </sql>

    <resultMap id="front" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="pageInfoId"/>
        <result property="moduleCode" column="pageModuleCode"/>
        <result property="moduleName" column="pageModuleName"/>
        <result property="permissionsInfoType" column="pageInfoType"/>
        <result property="pageId" column="pageId"/>
        <result property="pageIdName" column="pageIdName"/>
        <!-- 定义子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="componentInfoId"/>
            <result property="moduleCode" column="componentModuleCode"/>
            <result property="moduleName" column="componentModuleName"/>
            <result property="permissionsInfoType" column="componentInfoType"/>
            <result property="componentId" column="componentId"/>
            <result property="componentIdName" column="componentIdName"/>
            <!-- 定义孙子集合属性的resultMap -->
            <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
                <id property="id" column="fieldInfoId"/>
                <result property="moduleCode" column="fieldModuleCode"/>
                <result property="moduleName" column="fieldModuleName"/>
                <result property="permissionsInfoType" column="fieldInfoType"/>
                <result property="field" column="field"/>
                <result property="fieldName" column="fieldName"/>
            </collection>
        </collection>
    </resultMap>

    <select id="selectFrontList" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="front">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (page:b_p_page)
        WHERE page.moduleCode = '${moduleCode}'
        <if test="pageId != null and pageId != ''">
            AND page.pageId = '${pageId}'
        </if>
        <if test="pageIdName != null and pageIdName != ''">
            AND page.pageIdName = '${pageIdName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="page.id"/>
            <property name="moduleCode" value="page.moduleCode"/>
            <property name="moduleName" value="page.moduleName"/>
            <property name="permissionsInfoType" value="page.permissionsInfoType"/>
            <property name="alias" value="page"/>
        </include>
        ,page.pageId, page.pageIdName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="component"/>
        </include>
        ,null as componentId,null as componentIdName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="field"/>
        </include>
        ,null as field, null as fieldName
        <include refid="returnFrontTreeField">
        </include>
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (page:b_p_page)-[:b_p_page_component_edge]->(component:b_p_component)
        WHERE page.moduleCode = '${moduleCode}'
        <if test="pageId != null and pageId != ''">
            AND page.pageId = '${pageId}'
        </if>
        <if test="pageIdName != null and pageIdName != ''">
            AND page.pageIdName = '${pageIdName}'
        </if>
        <if test="componentId != null and componentId != ''">
            AND component.componentId = '${componentId}'
        </if>
        <if test="componentIdName != null and componentIdName != ''">
            AND component.componentIdName = '${componentIdName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="page.id"/>
            <property name="moduleCode" value="page.moduleCode"/>
            <property name="moduleName" value="page.moduleName"/>
            <property name="permissionsInfoType" value="page.permissionsInfoType"/>
            <property name="alias" value="page"/>
        </include>
        ,page.pageId, page.pageIdName,
        <include refid="treeCommonField">
            <property name="id" value="component.id"/>
            <property name="moduleCode" value="component.moduleCode"/>
            <property name="moduleName" value="component.moduleName"/>
            <property name="permissionsInfoType" value="component.permissionsInfoType"/>
            <property name="alias" value="component"/>
        </include>
        ,component.componentId,component.componentIdName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="field"/>
        </include>
        ,null as field, null as fieldName
        <include refid="returnFrontTreeField">
        </include>
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (page:b_p_page)-[:b_p_page_component_edge]->(component:b_p_component)-[:b_p_component_field_edge]->(field:b_p_field)
        WHERE page.moduleCode = '${moduleCode}'
        <if test="pageId != null and pageId != ''">
            AND page.pageId = '${pageId}'
        </if>
        <if test="pageIdName != null and pageIdName != ''">
            AND page.pageIdName = '${pageIdName}'
        </if>
        <if test="componentId != null and componentId != ''">
            AND component.componentId = '${componentId}'
        </if>
        <if test="componentIdName != null and componentIdName != ''">
            AND component.componentIdName = '${componentIdName}'
        </if>
        <if test="field != null and field != ''">
            AND field.field = '${field}'
        </if>
        <if test="fieldName != null and fieldName != ''">
            AND field.fieldName = '${fieldName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="page.id"/>
            <property name="moduleCode" value="page.moduleCode"/>
            <property name="moduleName" value="page.moduleName"/>
            <property name="permissionsInfoType" value="page.permissionsInfoType"/>
            <property name="alias" value="page"/>
        </include>
        ,page.pageId, page.pageIdName,
        <include refid="treeCommonField">
            <property name="id" value="component.id"/>
            <property name="moduleCode" value="component.moduleCode"/>
            <property name="moduleName" value="component.moduleName"/>
            <property name="permissionsInfoType" value="component.permissionsInfoType"/>
            <property name="alias" value="component"/>
        </include>
        ,component.componentId,component.componentIdName,
        <include refid="treeCommonField">
            <property name="id" value="field.id"/>
            <property name="moduleCode" value="field.moduleCode"/>
            <property name="moduleName" value="field.moduleName"/>
            <property name="permissionsInfoType" value="field.permissionsInfoType"/>
            <property name="alias" value="field"/>
        </include>
        ,field.field, field.fieldName
        <include refid="returnFrontTreeField">
        </include>
    </select>

    <resultMap id="back" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="interfaceInfoId"/>
        <result property="moduleCode" column="interfaceModuleCode"/>
        <result property="moduleName" column="interfaceModuleName"/>
        <result property="permissionsInfoType" column="interfaceInfoType"/>
        <result property="interfaceAddress" column="interfaceAddress"/>
        <result property="interfaceAddressName" column="interfaceAddressName"/>
        <!-- 定义子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="mapperInfoId"/>
            <result property="moduleCode" column="mapperModuleCode"/>
            <result property="moduleName" column="mapperModuleName"/>
            <result property="permissionsInfoType" column="mapperInfoType"/>
            <result property="mapperId" column="mapperId"/>
            <result property="mapperIdName" column="mapperIdName"/>
            <!-- 定义孙子集合属性的resultMap -->
            <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
                <id property="id" column="fieldInfoId"/>
                <result property="moduleCode" column="fieldModuleCode"/>
                <result property="moduleName" column="fieldModuleName"/>
                <result property="permissionsInfoType" column="fieldInfoType"/>
                <result property="field" column="field"/>
                <result property="fieldName" column="fieldName"/>
            </collection>
        </collection>
    </resultMap>

    <sql id="returnBackTreeField">
        $$
        ) as (
        interfaceInfoId BIGINT,interfaceModuleCode TEXT,interfaceModuleName TEXT,interfaceInfoType TEXT,interfaceAddress TEXT,interfaceAddressName TEXT,
        mapperInfoId BIGINT,mapperModuleCode TEXT,mapperModuleName TEXT,mapperInfoType TEXT,mapperId TEXT,mapperIdName TEXT,
        fieldInfoId BIGINT,fieldModuleCode TEXT,fieldModuleName TEXT,fieldInfoType TEXT,field TEXT,fieldName TEXT
        )
    </sql>

    <select id="selectBackList" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="back">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (interface:b_p_interface)
        WHERE interface.moduleCode = '${moduleCode}'
        <if test="interfaceAddress != null and interfaceAddress != ''">
            AND interface.interfaceAddress = '${interfaceAddress}'
        </if>
        <if test="interfaceAddressName != null and interfaceAddressName != ''">
            AND interface.interfaceAddressName = '${interfaceAddressName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="interface.id"/>
            <property name="moduleCode" value="interface.moduleCode"/>
            <property name="moduleName" value="interface.moduleName"/>
            <property name="permissionsInfoType" value="interface.permissionsInfoType"/>
            <property name="alias" value="interface"/>
        </include>
        ,interface.interfaceAddress, interface.interfaceAddressName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="mapper"/>
        </include>
        ,null as mapperId,null as mapperIdName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="field"/>
        </include>
        ,null as field, null as fieldName
        <include refid="returnBackTreeField">
        </include>
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (interface:b_p_interface)-[:b_p_interface_mapper_edge]->(mapper:b_p_mapper)
        WHERE interface.moduleCode = '${moduleCode}'
        <if test="interfaceAddress != null and interfaceAddress != ''">
            AND interface.interfaceAddress = '${interfaceAddress}'
        </if>
        <if test="interfaceAddressName != null and interfaceAddressName != ''">
            AND interface.interfaceAddressName = '${interfaceAddressName}'
        </if>
        <if test="mapperId != null and mapperId != ''">
            AND mapper.mapperId = '${mapperId}'
        </if>
        <if test="mapperIdName != null and mapperIdName != ''">
            AND mapper.mapperIdName = '${mapperIdName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="interface.id"/>
            <property name="moduleCode" value="interface.moduleCode"/>
            <property name="moduleName" value="interface.moduleName"/>
            <property name="permissionsInfoType" value="interface.permissionsInfoType"/>
            <property name="alias" value="interface"/>
        </include>
        ,interface.interfaceAddress, interface.interfaceAddressName,
        <include refid="treeCommonField">
            <property name="id" value="mapper.id"/>
            <property name="moduleCode" value="mapper.moduleCode"/>
            <property name="moduleName" value="mapper.moduleName"/>
            <property name="permissionsInfoType" value="mapper.permissionsInfoType"/>
            <property name="alias" value="mapper"/>
        </include>
        ,mapper.mapperId,mapper.mapperIdName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="field"/>
        </include>
        ,null as field, null as fieldName
        <include refid="returnBackTreeField">
        </include>
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (interface:b_p_interface)-[:b_p_interface_mapper_edge]->(mapper:b_p_mapper)-[:b_p_mapper_field_edge]->(field:b_p_field)
        WHERE interface.moduleCode = '${moduleCode}'
        <if test="interfaceAddress != null and interfaceAddress != ''">
            AND interface.interfaceAddress = '${interfaceAddress}'
        </if>
        <if test="interfaceAddressName != null and interfaceAddressName != ''">
            AND interface.interfaceAddressName = '${interfaceAddressName}'
        </if>
        <if test="mapperId != null and mapperId != ''">
            AND mapper.mapperId = '${mapperId}'
        </if>
        <if test="mapperIdName != null and mapperIdName != ''">
            AND mapper.mapperIdName = '${mapperIdName}'
        </if>
        <if test="field != null and field != ''">
            AND field.field = '${field}'
        </if>
        <if test="fieldName != null and fieldName != ''">
            AND field.fieldName = '${fieldName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="interface.id"/>
            <property name="moduleCode" value="interface.moduleCode"/>
            <property name="moduleName" value="interface.moduleName"/>
            <property name="permissionsInfoType" value="interface.permissionsInfoType"/>
            <property name="alias" value="interface"/>
        </include>
        ,interface.interfaceAddress, interface.interfaceAddressName,
        <include refid="treeCommonField">
            <property name="id" value="mapper.id"/>
            <property name="moduleCode" value="mapper.moduleCode"/>
            <property name="moduleName" value="mapper.moduleName"/>
            <property name="permissionsInfoType" value="mapper.permissionsInfoType"/>
            <property name="alias" value="mapper"/>
        </include>
        ,mapper.mapperId,mapper.mapperIdName,
        <include refid="treeCommonField">
            <property name="id" value="field.id"/>
            <property name="moduleCode" value="field.moduleCode"/>
            <property name="moduleName" value="field.moduleName"/>
            <property name="permissionsInfoType" value="field.permissionsInfoType"/>
            <property name="alias" value="field"/>
        </include>
        ,field.field, field.fieldName
        <include refid="returnBackTreeField">
        </include>
    </select>

    <resultMap id="button" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="pageInfoId"/>
        <result property="moduleCode" column="pageModuleCode"/>
        <result property="moduleName" column="pageModuleName"/>
        <result property="permissionsInfoType" column="pageInfoType"/>
        <result property="pageId" column="pageId"/>
        <result property="pageIdName" column="pageIdName"/>
        <!-- 定义子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="buttonInfoId"/>
            <result property="moduleCode" column="buttonModuleCode"/>
            <result property="moduleName" column="buttonModuleName"/>
            <result property="permissionsInfoType" column="buttonInfoType"/>
            <result property="buttonId" column="buttonId"/>
            <result property="buttonIdName" column="buttonIdName"/>
            <!-- 定义孙子集合属性的resultMap -->
            <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
                <id property="id" column="interfaceInfoId"/>
                <result property="moduleCode" column="interfaceModuleCode"/>
                <result property="moduleName" column="interfaceModuleName"/>
                <result property="permissionsInfoType" column="interfaceInfoType"/>
                <result property="interfaceAddress" column="interfaceAddress"/>
                <result property="interfaceAddressName" column="interfaceAddressName"/>
            </collection>
        </collection>
    </resultMap>

    <sql id="returnButtonTreeField">
        $$
        ) as (
        pageInfoId BIGINT,pageModuleCode TEXT,pageModuleName TEXT,pageInfoType TEXT,pageId TEXT,pageIdName TEXT,
        buttonInfoId BIGINT,buttonModuleCode TEXT,buttonModuleName TEXT,buttonInfoType TEXT,buttonId TEXT,buttonIdName TEXT,
        interfaceInfoId BIGINT,interfaceModuleCode TEXT,interfaceModuleName TEXT,interfaceInfoType TEXT,interfaceAddress TEXT,interfaceAddressName TEXT
        )
    </sql>

    <select id="selectButtonList" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="button">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (page:b_p_page)
        WHERE page.moduleCode = '${moduleCode}'
        <if test="pageId != null and pageId != ''">
            AND page.pageId = '${pageId}'
        </if>
        <if test="pageIdName != null and pageIdName != ''">
            AND page.pageIdName = '${pageIdName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="page.id"/>
            <property name="moduleCode" value="page.moduleCode"/>
            <property name="moduleName" value="page.moduleName"/>
            <property name="permissionsInfoType" value="page.permissionsInfoType"/>
            <property name="alias" value="page"/>
        </include>
        ,page.pageId, page.pageIdName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="button"/>
        </include>
        ,null as buttonId,null as buttonIdName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="interface"/>
        </include>
        ,null as interfaceAddress, null as interfaceAddressName
        <include refid="returnButtonTreeField">
        </include>
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (page:b_p_page)-[:b_p_page_button_edge]->(button:b_p_button)
        WHERE page.moduleCode = '${moduleCode}'
        <if test="pageId != null and pageId != ''">
            AND page.pageId = '${pageId}'
        </if>
        <if test="pageIdName != null and pageIdName != ''">
            AND page.pageIdName = '${pageIdName}'
        </if>
        <if test="buttonId != null and buttonId != ''">
            AND button.buttonId = '${buttonId}'
        </if>
        <if test="buttonIdName != null and buttonIdName != ''">
            AND button.buttonIdName = '${buttonIdName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="page.id"/>
            <property name="moduleCode" value="page.moduleCode"/>
            <property name="moduleName" value="page.moduleName"/>
            <property name="permissionsInfoType" value="page.permissionsInfoType"/>
            <property name="alias" value="page"/>
        </include>
        ,page.pageId, page.pageIdName,
        <include refid="treeCommonField">
            <property name="id" value="button.id"/>
            <property name="moduleCode" value="button.moduleCode"/>
            <property name="moduleName" value="button.moduleName"/>
            <property name="permissionsInfoType" value="button.permissionsInfoType"/>
            <property name="alias" value="button"/>
        </include>
        ,button.buttonId,button.buttonIdName,
        <include refid="treeCommonField">
            <property name="id" value="null"/>
            <property name="moduleCode" value="null"/>
            <property name="moduleName" value="null"/>
            <property name="permissionsInfoType" value="null"/>
            <property name="alias" value="interface"/>
        </include>
        ,null as interfaceAddress, null as interfaceAddressName
        <include refid="returnButtonTreeField">
        </include>
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (page:b_p_page)-[:b_p_page_button_edge]->(button:b_p_button)-[:b_p_button_interface_edge]->(interface:b_p_interface)
        WHERE page.moduleCode = '${moduleCode}'
        <if test="pageId != null and pageId != ''">
            AND page.pageId = '${pageId}'
        </if>
        <if test="pageIdName != null and pageIdName != ''">
            AND page.pageIdName = '${pageIdName}'
        </if>
        <if test="buttonId != null and buttonId != ''">
            AND button.buttonId = '${buttonId}'
        </if>
        <if test="buttonIdName != null and buttonIdName != ''">
            AND button.buttonIdName = '${buttonIdName}'
        </if>
        <if test="interfaceAddress != null and interfaceAddress != ''">
            AND interface.interfaceAddress = '${interfaceAddress}'
        </if>
        <if test="interfaceAddressName != null and interfaceAddressName != ''">
            AND interface.interfaceAddressName = '${interfaceAddressName}'
        </if>
        RETURN
        <include refid="treeCommonField">
            <property name="id" value="page.id"/>
            <property name="moduleCode" value="page.moduleCode"/>
            <property name="moduleName" value="page.moduleName"/>
            <property name="permissionsInfoType" value="page.permissionsInfoType"/>
            <property name="alias" value="page"/>
        </include>
        ,page.pageId, page.pageIdName,
        <include refid="treeCommonField">
            <property name="id" value="button.id"/>
            <property name="moduleCode" value="button.moduleCode"/>
            <property name="moduleName" value="button.moduleName"/>
            <property name="permissionsInfoType" value="button.permissionsInfoType"/>
            <property name="alias" value="button"/>
        </include>
        ,button.buttonId,button.buttonIdName,
        <include refid="treeCommonField">
            <property name="id" value="interface.id"/>
            <property name="moduleCode" value="interface.moduleCode"/>
            <property name="moduleName" value="interface.moduleName"/>
            <property name="permissionsInfoType" value="interface.permissionsInfoType"/>
            <property name="alias" value="interface"/>
        </include>
        ,interface.interfaceAddress, interface.interfaceAddressName
        <include refid="returnButtonTreeField">
        </include>
    </select>

    <sql id="selectPermissionsInfoEdge">
        <choose>
            <when test="${sourcePermissionsInfoType} == 'PAGE' and ${targetPermissionsInfoType} == 'COMPONENT'">
                MATCH (page:b_p_page)-[]->(component:b_p_component)
                WHERE page.moduleCode = '${moduleCode}'
                RETURN
                <include refid="treeCommonField">
                    <property name="id" value="page.id"/>
                    <property name="moduleCode" value="page.moduleCode"/>
                    <property name="moduleName" value="page.moduleName"/>
                    <property name="permissionsInfoType" value="page.permissionsInfoType"/>
                    <property name="alias" value="page"/>
                </include>
                ,
                <include refid="treeCommonField">
                    <property name="id" value="component.id"/>
                    <property name="moduleCode" value="component.moduleCode"/>
                    <property name="moduleName" value="component.moduleName"/>
                    <property name="permissionsInfoType" value="component.permissionsInfoType"/>
                    <property name="alias" value="component"/>
                </include>
                <include refid="returnFrontTreeEdgeField">
                    <property name="source" value="page"/>
                    <property name="target" value="component"/>
                </include>
            </when>
            <when test="${sourcePermissionsInfoType} == 'COMPONENT' and ${targetPermissionsInfoType} == 'FIELD'">
                MATCH (component:b_p_component)-[]->(field:b_p_field)
                WHERE component.moduleCode = '${moduleCode}'
                RETURN
                <include refid="treeCommonField">
                    <property name="id" value="component.id"/>
                    <property name="moduleCode" value="component.moduleCode"/>
                    <property name="moduleName" value="component.moduleName"/>
                    <property name="permissionsInfoType" value="component.permissionsInfoType"/>
                    <property name="alias" value="component"/>
                </include>
                ,
                <include refid="treeCommonField">
                    <property name="id" value="field.id"/>
                    <property name="moduleCode" value="field.moduleCode"/>
                    <property name="moduleName" value="field.moduleName"/>
                    <property name="permissionsInfoType" value="field.permissionsInfoType"/>
                    <property name="alias" value="field"/>
                </include>
                <include refid="returnFrontTreeEdgeField">
                    <property name="source" value="component"/>
                    <property name="target" value="field"/>
                </include>
            </when>
            <when test="${sourcePermissionsInfoType} == 'PAGE' and ${targetPermissionsInfoType} == 'BUTTON'">
                MATCH (page:b_p_page)-[]->(button:b_p_button)
                WHERE page.moduleCode = '${moduleCode}'
                RETURN
                <include refid="treeCommonField">
                    <property name="id" value="page.id"/>
                    <property name="moduleCode" value="page.moduleCode"/>
                    <property name="moduleName" value="page.moduleName"/>
                    <property name="permissionsInfoType" value="page.permissionsInfoType"/>
                    <property name="alias" value="page"/>
                </include>
                ,
                <include refid="treeCommonField">
                    <property name="id" value="button.id"/>
                    <property name="moduleCode" value="button.moduleCode"/>
                    <property name="moduleName" value="button.moduleName"/>
                    <property name="permissionsInfoType" value="button.permissionsInfoType"/>
                    <property name="alias" value="button"/>
                </include>
                <include refid="returnFrontTreeEdgeField">
                    <property name="source" value="page"/>
                    <property name="target" value="button"/>
                </include>
            </when>
            <when test="${sourcePermissionsInfoType} == 'BUTTON' and ${targetPermissionsInfoType} == 'INTERFACE'">
                MATCH (button:b_p_button)-[]->(interface:b_p_interface)
                WHERE button.moduleCode = '${moduleCode}'
                RETURN
                <include refid="treeCommonField">
                    <property name="id" value="button.id"/>
                    <property name="moduleCode" value="button.moduleCode"/>
                    <property name="moduleName" value="button.moduleName"/>
                    <property name="permissionsInfoType" value="button.permissionsInfoType"/>
                    <property name="alias" value="button"/>
                </include>
                ,
                <include refid="treeCommonField">
                    <property name="id" value="interface.id"/>
                    <property name="moduleCode" value="interface.moduleCode"/>
                    <property name="moduleName" value="interface.moduleName"/>
                    <property name="permissionsInfoType" value="interface.permissionsInfoType"/>
                    <property name="alias" value="interface"/>
                </include>
                <include refid="returnFrontTreeEdgeField">
                    <property name="source" value="button"/>
                    <property name="target" value="interface"/>
                </include>
            </when>
            <when test="${sourcePermissionsInfoType} == 'INTERFACE' and ${targetPermissionsInfoType} == 'MAPPER'">
                MATCH (interface:b_p_interface)-[]->(mapper:b_p_mapper)
                WHERE interface.moduleCode = '${moduleCode}'
                RETURN
                <include refid="treeCommonField">
                    <property name="id" value="interface.id"/>
                    <property name="moduleCode" value="interface.moduleCode"/>
                    <property name="moduleName" value="interface.moduleName"/>
                    <property name="permissionsInfoType" value="interface.permissionsInfoType"/>
                    <property name="alias" value="interface"/>
                </include>
                ,
                <include refid="treeCommonField">
                    <property name="id" value="mapper.id"/>
                    <property name="moduleCode" value="mapper.moduleCode"/>
                    <property name="moduleName" value="mapper.moduleName"/>
                    <property name="permissionsInfoType" value="mapper.permissionsInfoType"/>
                    <property name="alias" value="mapper"/>
                </include>
                <include refid="returnFrontTreeEdgeField">
                    <property name="source" value="interface"/>
                    <property name="target" value="mapper"/>
                </include>
            </when>
            <when test="${sourcePermissionsInfoType} == 'MAPPER' and ${targetPermissionsInfoType} == 'FIELD'">
                MATCH (mapper:b_p_mapper)-[]->(field:b_p_field)
                WHERE mapper.moduleCode = '${moduleCode}'
                RETURN
                <include refid="treeCommonField">
                    <property name="id" value="mapper.id"/>
                    <property name="moduleCode" value="mapper.moduleCode"/>
                    <property name="moduleName" value="mapper.moduleName"/>
                    <property name="permissionsInfoType" value="mapper.permissionsInfoType"/>
                    <property name="alias" value="mapper"/>
                </include>
                ,
                <include refid="treeCommonField">
                    <property name="id" value="field.id"/>
                    <property name="moduleCode" value="field.moduleCode"/>
                    <property name="moduleName" value="field.moduleName"/>
                    <property name="permissionsInfoType" value="field.permissionsInfoType"/>
                    <property name="alias" value="field"/>
                </include>
                <include refid="returnFrontTreeEdgeField">
                    <property name="source" value="mapper"/>
                    <property name="target" value="field"/>
                </include>
            </when>
        </choose>
    </sql>

    <sql id="returnFrontTreeEdgeField">
        $$
        ) as (
        ${source}InfoId BIGINT,${source}ModuleCode TEXT,${source}ModuleName TEXT,${source}InfoType TEXT,
        ${target}InfoId BIGINT,${target}ModuleCode TEXT,${target}ModuleName TEXT,${target}InfoType TEXT
        )
    </sql>

    <resultMap id="pageAndComponent" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="pageInfoId"/>
        <result property="moduleCode" column="pageModuleCode"/>
        <result property="moduleName" column="pageModuleName"/>
        <result property="permissionsInfoType" column="pageInfoType"/>
        <result property="pageId" column="pageId"/>
        <result property="pageIdName" column="pageIdName"/>
        <!-- 定义子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="componentInfoId"/>
            <result property="moduleCode" column="componentModuleCode"/>
            <result property="moduleName" column="componentModuleName"/>
            <result property="permissionsInfoType" column="componentInfoType"/>
            <result property="componentId" column="componentId"/>
            <result property="componentIdName" column="componentIdName"/>
        </collection>
    </resultMap>

    <select id="selectPageAndComponentEdgeList" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="pageAndComponent">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        <include refid="selectPermissionsInfoEdge">
            <property name="sourcePermissionsInfoType" value="sourcePermissionsInfoType.toString()"/>
            <property name="targetPermissionsInfoType" value="targetPermissionsInfoType.toString()"/>
        </include>
    </select>

    <resultMap id="componentAndField" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="componentInfoId"/>
        <result property="moduleCode" column="componentModuleCode"/>
        <result property="moduleName" column="componentModuleName"/>
        <result property="permissionsInfoType" column="componentInfoType"/>
        <result property="componentId" column="componentId"/>
        <result property="componentIdName" column="componentIdName"/>
        <!-- 定义孙子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="fieldInfoId"/>
            <result property="moduleCode" column="fieldModuleCode"/>
            <result property="moduleName" column="fieldModuleName"/>
            <result property="permissionsInfoType" column="fieldInfoType"/>
            <result property="field" column="field"/>
            <result property="fieldName" column="fieldName"/>
        </collection>
    </resultMap>

    <select id="selectComponentAndFieldEdgeList"
            parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="componentAndField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        <include refid="selectPermissionsInfoEdge">
            <property name="sourcePermissionsInfoType" value="sourcePermissionsInfoType.toString()"/>
            <property name="targetPermissionsInfoType" value="targetPermissionsInfoType.toString()"/>
        </include>
    </select>

    <resultMap id="pageAndButton" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="pageInfoId"/>
        <result property="moduleCode" column="pageModuleCode"/>
        <result property="moduleName" column="pageModuleName"/>
        <result property="permissionsInfoType" column="pageInfoType"/>
        <result property="pageId" column="pageId"/>
        <result property="pageIdName" column="pageIdName"/>
        <!-- 定义子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="buttonInfoId"/>
            <result property="moduleCode" column="buttonModuleCode"/>
            <result property="moduleName" column="buttonModuleName"/>
            <result property="permissionsInfoType" column="buttonInfoType"/>
            <result property="buttonId" column="buttonId"/>
            <result property="buttonIdName" column="buttonIdName"/>
        </collection>
    </resultMap>

    <select id="selectPageAndButtonEdgeList" parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="pageAndButton">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        <include refid="selectPermissionsInfoEdge">
            <property name="sourcePermissionsInfoType" value="sourcePermissionsInfoType.toString()"/>
            <property name="targetPermissionsInfoType" value="targetPermissionsInfoType.toString()"/>
        </include>
    </select>

    <resultMap id="buttonAndInterface" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="buttonInfoId"/>
        <result property="moduleCode" column="buttonModuleCode"/>
        <result property="moduleName" column="buttonModuleName"/>
        <result property="permissionsInfoType" column="buttonInfoType"/>
        <result property="buttonId" column="buttonId"/>
        <result property="buttonIdName" column="buttonIdName"/>
        <!-- 定义孙子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="interfaceInfoId"/>
            <result property="moduleCode" column="interfaceModuleCode"/>
            <result property="moduleName" column="interfaceModuleName"/>
            <result property="permissionsInfoType" column="interfaceInfoType"/>
            <result property="interfaceAddress" column="interfaceAddress"/>
            <result property="interfaceAddressName" column="interfaceAddressName"/>
        </collection>
    </resultMap>

    <select id="selectButtonAndInterfaceEdgeList"
            parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="buttonAndInterface">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        <include refid="selectPermissionsInfoEdge">
            <property name="sourcePermissionsInfoType" value="sourcePermissionsInfoType.toString()"/>
            <property name="targetPermissionsInfoType" value="targetPermissionsInfoType.toString()"/>
        </include>
    </select>

    <resultMap id="interfaceAndMapper" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="interfaceInfoId"/>
        <result property="moduleCode" column="interfaceModuleCode"/>
        <result property="moduleName" column="interfaceModuleName"/>
        <result property="permissionsInfoType" column="interfaceInfoType"/>
        <result property="interfaceAddress" column="interfaceAddress"/>
        <result property="interfaceAddressName" column="interfaceAddressName"/>
        <!-- 定义子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="mapperInfoId"/>
            <result property="moduleCode" column="mapperModuleCode"/>
            <result property="moduleName" column="mapperModuleName"/>
            <result property="permissionsInfoType" column="mapperInfoType"/>
            <result property="mapperId" column="mapperId"/>
            <result property="mapperIdName" column="mapperIdName"/>
        </collection>
    </resultMap>

    <select id="selectInterfaceAndMapperEdgeList"
            parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="interfaceAndMapper">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        <include refid="selectPermissionsInfoEdge">
            <property name="sourcePermissionsInfoType" value="sourcePermissionsInfoType.toString()"/>
            <property name="targetPermissionsInfoType" value="targetPermissionsInfoType.toString()"/>
        </include>
    </select>

    <resultMap id="mapperAndField" type="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
        <id property="id" column="mapperInfoId"/>
        <result property="moduleCode" column="mapperModuleCode"/>
        <result property="moduleName" column="mapperModuleName"/>
        <result property="permissionsInfoType" column="mapperInfoType"/>
        <result property="mapperId" column="mapperId"/>
        <result property="mapperIdName" column="mapperIdName"/>
        <!-- 定义孙子集合属性的resultMap -->
        <collection property="children" ofType="com.datalink.fdop.permissions.api.domain.PermissionsInfo">
            <id property="id" column="fieldInfoId"/>
            <result property="moduleCode" column="fieldModuleCode"/>
            <result property="moduleName" column="fieldModuleName"/>
            <result property="permissionsInfoType" column="fieldInfoType"/>
            <result property="field" column="field"/>
            <result property="fieldName" column="fieldName"/>
        </collection>
    </resultMap>

    <select id="selectMapperAndFieldEdgeList"
            parameterType="com.datalink.fdop.permissions.api.domain.PermissionsInfo"
            resultMap="mapperAndField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        <include refid="selectPermissionsInfoEdge">
            <property name="sourcePermissionsInfoType" value="sourcePermissionsInfoType.toString()"/>
            <property name="targetPermissionsInfoType" value="targetPermissionsInfoType.toString()"/>
        </include>
    </select>


</mapper>