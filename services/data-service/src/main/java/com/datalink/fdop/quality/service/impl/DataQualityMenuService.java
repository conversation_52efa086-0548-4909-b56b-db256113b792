package com.datalink.fdop.quality.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.domain.DataQualityMenu;
import com.datalink.fdop.quality.api.domain.DataQualityTree;
import com.datalink.fdop.quality.mapper.DataQualityMapper;
import com.datalink.fdop.quality.mapper.DataQualityMenuMapper;
import com.datalink.fdop.quality.service.IDataQualityMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
public class DataQualityMenuService implements IDataQualityMenuService {

    @Autowired
    private DataQualityMapper dataQualityMapper;

    @Autowired
    private DataQualityMenuMapper dataQualityMenuMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataQualityMenu dataQualityMenu) {
        if (dataQualityMenuMapper.selectByCode(dataQualityMenu.getCode()) != null) {
            throw new ServiceException(Status.QUALITY_MENU_ALREADY_EXISTS);
        }
        dataQualityMenu.setId(IdWorker.getId());
        int insert = dataQualityMenuMapper.insertQualityMenu(dataQualityMenu);
        // 创建菜单边关系
        if (insert > 0 && dataQualityMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            dataQualityMenuMapper.createQualityMenuEdge(dataQualityMenu.getPid(), Arrays.asList(dataQualityMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataQualityMenu dataQualityMenu) {
        DataQualityMenu checkDataQualityMenu = dataQualityMenuMapper.selectById(dataQualityMenu.getId());
        if (checkDataQualityMenu == null) {
            throw new ServiceException(Status.QUALITY_MENU_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataQualityMenu.getCode()) && dataQualityMenuMapper.checkCodeIsExists(dataQualityMenu.getId(), dataQualityMenu.getCode()) != null) {
            throw new ServiceException(Status.QUALITY_MENU_ALREADY_EXISTS);
        }
        int update = dataQualityMenuMapper.updateById(dataQualityMenu);
        if (update > 0 && dataQualityMenu.getPid() != null) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            dataQualityMenuMapper.deleteQualityMenuEdge(Arrays.asList(dataQualityMenu.getId()), checkDataQualityMenu.getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (dataQualityMenu.getPid() != -1L) {
                dataQualityMenuMapper.createQualityMenuEdge(dataQualityMenu.getPid(), Arrays.asList(dataQualityMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            DataQualityMenu dataQualityMenu = dataQualityMenuMapper.selectById(id);
            if (dataQualityMenu == null) {
                continue;
            }
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = dataQualityMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = dataQualityMenuMapper.bacthUpdatePidById(menuIdList, dataQualityMenu.getPid());
                if (update > 0 && dataQualityMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataQualityMenuMapper.createQualityMenuEdge(dataQualityMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = dataQualityMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int update = dataQualityMapper.bacthUpdatePidById(elementIdList, dataQualityMenu.getPid());
                if (update > 0 && dataQualityMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataQualityMapper.createQualityAndMenuEdge(dataQualityMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return dataQualityMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<DataQualityTree> tree(String sort, String code, Boolean isQueryNode) {

        // 所有的数据集合
        List<DataQualityTree> trees = new ArrayList<>();
        // 添加数据元素树
        if (isQueryNode) {
            trees.addAll(dataQualityMapper.selectQualityTree(sort, code));
        }
        // 添加数据元素菜单树
        trees.addAll(dataQualityMenuMapper.selectMenuTree(sort, null));

        // 递归成树结构
        List<DataQualityTree> treeList = (List<DataQualityTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public PageDataInfo<DataQuality> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<DataQuality> page = PageUtils.getPage(DataQuality.class);
        IPage<DataQuality> dataEntityIPage = dataQualityMapper.overview(page, pid, sort, searchVo);

        return PageUtils.getPageInfo(dataEntityIPage.getRecords(), (int) dataEntityIPage.getTotal());
    }

}
