package com.datalink.fdop.quality.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.domain.DataQualityMenu;
import com.datalink.fdop.quality.api.domain.DataQualityTree;
import com.datalink.fdop.quality.service.IDataQualityMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11 14:22
 */
@RequestMapping(value = "/quality/menu")
@RestController
@Api(tags = "菜单api")
public class DataQualityMenuController extends BaseController {

    @Autowired
    private IDataQualityMenuService dataQualityMenuService;

    @ApiOperation("创建菜单")
    @Log(title = "数据质量", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataQualityMenu dataQualityMenu) {
        if (dataQualityMenu.getPid() == null) {
            dataQualityMenu.setPid(-1L);
        }
        return R.toResult(dataQualityMenuService.create(dataQualityMenu));
    }

    @ApiOperation("修改菜单")
    @Log(title = "数据质量", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataQualityMenu dataQualityMenu) {
        if (dataQualityMenu.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(dataQualityMenuService.update(dataQualityMenu));
    }

    @ApiOperation("删除菜单")
    @Log(title = "数据质量", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_QUALITY_MENU_TO_DELETE);
        }
        return R.toResult(dataQualityMenuService.delete(ids));
    }

    @ApiOperation("展示数据质量树结构")
    @Log(title = "数据质量")
    @GetMapping(value = "/tree")
    public R<List<DataQualityTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode) {
        List<DataQualityTree> list = dataQualityMenuService.tree(sort, code, isQueryNode);
        return R.ok(list);
    }

    @ApiOperation("数据质量总览")
    @Log(title = "数据质量")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataQuality>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                 @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                 @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(dataQualityMenuService.overview(pid, sort, searchVo));
    }

}
