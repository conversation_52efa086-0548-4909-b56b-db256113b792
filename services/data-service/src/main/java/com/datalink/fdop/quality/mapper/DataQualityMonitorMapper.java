package com.datalink.fdop.quality.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.quality.api.domain.DataQualityMonitor;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataQualityMonitorMapper extends BaseMapper<DataQualityMonitor> {

    Integer querySerialNumber();

    int createQualityMonitorAndMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertQualityMonitor(@Param("dataQualityMonitor") DataQualityMonitor dataQualityMonitor);

    int updateById(DataQualityMonitor dataQualityMonitor);

    int createMonitorAndTaskEdge(@Param("id") Long id, @Param("taskCode") Long taskCode);

    int createMonitorAndProcessEdge(@Param("id") Long id, @Param("processCode") Long processCode);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteQualityMonitorAndMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    DataQualityMonitor selectById(Long id);

    DataQualityMonitor selectByCode(String code);

    DataQualityMonitor checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<DataQualityMonitorTree> selectQualityMonitorTree(@Param("sort") String sort, @Param("code") String code);

    IPage<DataQualityMonitor> overview(IPage<DataQualityMonitor> page,
                                       @Param("pid") Long pid,
                                       @Param("sort") String sort,
                                       @Param("searchVo") SearchVo searchVo);


    int bindMonitorTable(@Param("id") Long id, @Param("tableIdss") List<List<String>> tableIdss);

    int unbindMonitorTable(@Param("id") Long id, @Param("tableIdss") List<List<String>> tableIdss);

    int bindMonitorView(@Param("id") Long id, @Param("viewIdss") List<List<String>> viewIdss);

    int unbindMonitorView(@Param("id") Long id, @Param("viewIdss") List<List<String>> viewIdss);

    List<String> selectMonitorTableList(@Param("id") Long id);

    List<String> selectMonitorViewList(@Param("id") Long id);

}
