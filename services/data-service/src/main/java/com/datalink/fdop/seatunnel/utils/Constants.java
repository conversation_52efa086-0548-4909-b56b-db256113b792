/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.datalink.fdop.seatunnel.utils;

public class Constants {

    private Constants() {
        throw new IllegalStateException("Utility class");
    }

    public static final String CONFIG_OPTIONS = "--config";

    public static final String DEPLOY_MODE_OPTIONS = "--deploy-mode";

    public static final String MASTER_OPTIONS = "--master";

    public static final String QUEUE_OPTIONS = "--queue";

    public static final String ENCRYPT_OPTIONS = "--encrypt";



    /**
     * 指定SeaTunnel任务名称
     * --name: SeaTunnel job name (default: SeaTunnel)
     */
    public static final String JOB_NAME = "-n";

    /**
     * 取消任务
     * --cancel-job:Cancel job by JobId
     */
    public static final String CANCEL_JOB = "-can";

    /**
     * 恢复任务
     * --restore: restore with savepoint by jobId
     */
    public static final String RESTORE_JOB = "-r";

    /**
     * SeaTunnel jobId
     */
    public static final String JOB_ID = "${job_id}";

    public static final String RUN_COMMAND = "%s/bin/seatunnel.sh";

}
