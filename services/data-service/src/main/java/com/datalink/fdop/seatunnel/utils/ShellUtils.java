
package com.datalink.fdop.seatunnel.utils;

import com.datalink.fdop.common.core.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * shell工具
 * <AUTHOR>
 */
public class ShellUtils {

    private static final ExecutorService executor = Executors.newFixedThreadPool(50);

    private static final int BUFFER_SIZE = 128; // 缓冲区大小，单位：字符

    /**
     * 执行 shell 命令
     *
     * @param command shell 命令
     * @return 返回 shell 命令 结果
     */
    public static void execute(String command,String logFilePath) {
        // TODO: 对command进行安全性检查，避免命令注入
        Process process;
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("/bin/bash", "-c", command);
            process = processBuilder.start();
            if (StringUtils.isNotEmpty(logFilePath)) {
                Path logFile = Paths.get(logFilePath);
                // 提交任务到线程池，用于读取标准输出流并写入日志文件
                executor.submit(() -> writeStreamToLogWithBuffer(process.getInputStream(), logFile));
                // 提交任务到线程池，用于读取错误输出流并写入日志文件
                executor.submit(() -> writeStreamToLogWithBuffer(process.getErrorStream(), logFile));
            }
        } catch (IOException ioException) {
            // 记录日志或采取其他错误处理措施
            ioException.printStackTrace();
        }
    }

    private static void writeStreamToLogWithBuffer(InputStream stream, Path logFile) {
        StringBuilder buffer = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(stream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                buffer.append(line).append("\n");
                if (buffer.length() >= BUFFER_SIZE) {
                    flushBuffer(buffer, logFile);
                }
            }
            // 确保所有剩余的内容都被写入文件
            flushBuffer(buffer, logFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void flushBuffer(StringBuilder buffer, Path logFile) throws IOException {
        if (buffer.length() > 0) {
            Files.write(logFile, buffer.toString().getBytes(StandardCharsets.UTF_8),
                    StandardOpenOption.APPEND, StandardOpenOption.CREATE);
            buffer.setLength(0); // 清空缓冲区
        }
    }

    /**
     * 获取结果
     *
     * @param process     进程信息
     * @param inputStream 输入流
     * @return 返回 结果
     */
    private static List<String> getResult(Process process, InputStream inputStream) {
        BufferedReader input = new BufferedReader(new InputStreamReader(inputStream));
        List<String> processList = new ArrayList<>();
        try {
            String line;
            while ((line = input.readLine()) != null) {
                processList.add(line);
            }
        } catch (IOException ioException) {
            ioException.printStackTrace();
        } finally {
            try {
                input.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return processList;
    }

    /**
     * list 转 字符串
     *
     * @param list      集合
     * @param splitChar 分隔符
     * @return 返回 字符串
     */
    private static String getListString(List<String> list, String splitChar) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (String value : list) {
            stringBuilder.append(value).append(splitChar);
        }
        // 删除最后一个分隔符
        if (stringBuilder.length() > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        return stringBuilder.toString();
    }
}