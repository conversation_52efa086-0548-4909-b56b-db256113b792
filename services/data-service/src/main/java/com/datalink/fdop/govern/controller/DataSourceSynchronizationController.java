package com.datalink.fdop.govern.controller;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.govern.api.domain.DataSourceSynchronization;
import com.datalink.fdop.govern.api.model.vo.DataSourceInfoShowVo;
import com.datalink.fdop.govern.api.model.vo.DataSourceInfoVo;
import com.datalink.fdop.govern.api.model.vo.MonitorTableOrVIewInfoVo;
import com.datalink.fdop.govern.service.DataSourceSynchronizationService;
import com.datalink.fdop.quality.api.domain.DataQuality;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value = "/govern/synchronization")
@RestController
@Api(tags = "数据源同步api")
public class DataSourceSynchronizationController {

    @Autowired
    private DataSourceSynchronizationService dataSourceSynchronizationService;




    @ApiOperation("创建同步信息")
    @Log(title = "数据治理")
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataSourceSynchronization dataSourceSynchronization) {
        return R.toResult(dataSourceSynchronizationService.create(dataSourceSynchronization));
    }


    @ApiOperation("修改同步信息")
    @Log(title = "数据治理")
    @PostMapping(value = "/update")
    public R update(@Validated @RequestBody DataSourceSynchronization dataSourceSynchronization) {
        return R.toResult(dataSourceSynchronizationService.update(dataSourceSynchronization));
    }


    @ApiOperation("删除同步信息")
    @Log(title = "数据治理")
    @PostMapping(value = "/del")
    public R del(@Validated @RequestBody List<Long> dataSourceIds) {
        return R.toResult(dataSourceSynchronizationService.del(dataSourceIds));
    }


    @ApiOperation("根据dataSourceId获取同步信息")
    @Log(title = "数据治理")
    @GetMapping(value = "/getSyncInfoByDataSourceId")
    public R getSyncInfoByDataSourceId(@Validated @RequestParam Long dataSourceId) {
        return R.ok(dataSourceSynchronizationService.getSyncByDataSourceId(dataSourceId));
    }

    @ApiOperation("根据dataSourceId获取同步信息")
    @Log(title = "数据治理")
    @GetMapping(value = "/sync")
    public R sync(@Validated @RequestParam Long dataSourceId) {
        dataSourceSynchronizationService.sync(dataSourceId);
        return R.ok();
    }


    @ApiOperation("同步数据存库")
    @Log(title = "数据治理")
    @PostMapping(value = "/save/syncData")
    public R saveSyncData(@RequestParam Long dataSourceId, @RequestBody List<DataSourceInfoVo> dataSourceInfoVos,@RequestParam Long projectCode) {

        return dataSourceSynchronizationService.saveSyncData(dataSourceInfoVos, dataSourceId, projectCode);
    }

    @ApiOperation("总览")
    @Log(title = "数据治理")
    @PostMapping(value = "/overview")
    public R<PageDataInfo> overview(
            @RequestBody(required = false) SearchVo searchVo
    ) {
        return R.ok(dataSourceSynchronizationService.overview(searchVo));
    }


    @ApiOperation("元数据树")
    @Log(title = "数据治理")
    @PostMapping(value = "/metadata/tree")
    public R getMetadataTree(@RequestParam(required = false) String tableName,@RequestParam(required = false,defaultValue = "ASC")String sort) {
        return R.ok(dataSourceSynchronizationService.getMetadataTree(tableName,sort));
    }


    @ApiOperation("获取表字段信息")
    @Log(title = "数据治理")
    @PostMapping(value = "/getField/{tableId}")
    public R<List<Field>> getFieldByTableId(@PathVariable(value = "tableId") Long tableId) {
        return R.ok(dataSourceSynchronizationService.getFieldByTableId(tableId));
    }


    @ApiOperation("获取字段信息")
    @Log(title = "数据治理")
    @PostMapping(value = "/getField")
    public R<Field> getFieldById(@PathVariable(value = "fieldId") Long fieldId) {
        return R.ok(dataSourceSynchronizationService.getFieldById(fieldId));
    }

    @ApiOperation("绑定标准或者规则")
    @Log(title = "数据治理")
    @PostMapping(value = "/binding/{fieldId}")
    public R bindingFieldId(@PathVariable(value = "fieldId") Long fieldId, @RequestParam(required = false) Long standardId, @RequestParam(required = false) Long qualityId) {
        dataSourceSynchronizationService.bindingFieldId(fieldId, standardId, qualityId);
        return R.ok();
    }


    @ApiOperation("元数据维护总揽")
    @Log(title = "数据治理")
    @PostMapping(value = "/metadata/overview")
    public R metadataOverview(@RequestBody(required = false) SearchVo searchVo) {
        PageDataInfo<DataSourceInfoShowVo> pageDataInfo = dataSourceSynchronizationService.metadataOverview(searchVo);
        return R.ok(pageDataInfo);
    }

    @ApiOperation("获取字段绑定规则")
    @Log(title = "数据治理")
    @GetMapping(value = "/getQualityByField")
    public R<DataQuality> getQualityByField(@RequestParam(value = "fId") Long fId) {
        return R.ok(dataSourceSynchronizationService.getQualityByField(fId));
    }

    @ApiOperation("校验字段校验是否通过")
    @Log(title = "数据治理")
    @GetMapping(value = "/checkField")
    public R<Boolean> checkField(@RequestParam(value = "fId") Long fId) {
        return R.ok(dataSourceSynchronizationService.checkField(fId));
    }

    @ApiOperation("查询监控绑定的表/视图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "monitorId", value = "规则监控id", required = true, dataType = "Long", paramType = "path"),
    })
    @Log(title = "数据质量")
    @GetMapping(value = "/selectMonitorTableOrViewInfoList/{monitorId}")
    public R<MonitorTableOrVIewInfoVo> selectMonitorTableOrViewInfoList(@PathVariable(value = "monitorId") Long monitorId) {
        return R.ok(dataSourceSynchronizationService.selectMonitorTableOrViewInfoList(monitorId));
    }

}
