package com.datalink.fdop.quality.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.quality.api.domain.DataQualityLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IDataQualityLogService {

    DataQualityLog create(DataQualityLog dataQualityLog);

    int update(DataQualityLog dataQualityLog);

    int delete(List<Long> ids);

    PageDataInfo<DataQualityLog> list(DataQualityLog dataQualityLog);

}
