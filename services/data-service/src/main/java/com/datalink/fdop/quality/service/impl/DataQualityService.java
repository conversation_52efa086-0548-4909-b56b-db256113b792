package com.datalink.fdop.quality.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.element.api.RemoteElementService;
import com.datalink.fdop.element.api.RemoteElementStrureService;
import com.datalink.fdop.element.api.RemoteEntityService;
import com.datalink.fdop.element.api.RemoteEntityStrureService;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.govern.api.RemoteStandardService;
import com.datalink.fdop.govern.api.domain.DataStandardTemporary;
import com.datalink.fdop.param.api.RemoteParamService;
import com.datalink.fdop.param.api.domain.GlobalParam;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.domain.DataQualityMenu;
import com.datalink.fdop.quality.api.domain.RegexRule;
import com.datalink.fdop.quality.api.domain.RegexValue;
import com.datalink.fdop.quality.api.enums.RegexRelationType;
import com.datalink.fdop.quality.api.enums.RegexRuleCiteType;
import com.datalink.fdop.quality.api.model.dto.DataEntityQuality;
import com.datalink.fdop.quality.api.model.vo.CheckRegexVo;
import com.datalink.fdop.quality.api.model.vo.DataQualityCopyVo;
import com.datalink.fdop.quality.mapper.DataQualityMapper;
import com.datalink.fdop.quality.mapper.DataQualityMenuMapper;
import com.datalink.fdop.quality.service.IDataQualityService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
public class DataQualityService implements IDataQualityService {

    private static final Logger logger = LoggerFactory.getLogger(DataQualityService.class);

    @Autowired
    private DataQualityMapper dataQualityMapper;

    @Autowired
    private DataQualityMenuMapper dataQualityMenuMapper;

    @Autowired
    private RemoteParamService remoteParamService;

    @Autowired
    private RemoteEntityService remoteEntityService;

    @Autowired
    private RemoteEntityStrureService remoteEntityStrureService;

    @Autowired
    private RemoteElementService remoteElementService;

    @Autowired
    private RemoteElementStrureService remoteElementStrureService;

    @Autowired
    private RemoteStandardService remoteStandardService;

    /**
     * 校验规则
     *
     * @param id
     * @return
     */
    private DataQuality checkQuality(Long id) {
        DataQuality dataQuality = dataQualityMapper.selectById(id);
        if (dataQuality == null) {
            throw new ServiceException(Status.QUALITY_DOES_NOT_EXIST);
        }
        if (dataQuality.getIsCiteStandard() == null) {
            dataQuality.setIsConfigType(false);
        }
        if (dataQuality.getIsCiteStandard()) {
            Long standardId = dataQualityMapper.selectStandardId(id);
            if (standardId == 0L) {
                throw new ServiceException(Status.THE_DATA_STANDARD_REFERENCED_BY_THE_RULE_DOES_NOT_EXIST);
            }
            dataQuality.setStandardId(standardId);
            dataQuality.setIsConfigType(true);
        }
        if (dataQuality.getIsConfigType() == null) {
            dataQuality.setIsConfigType(false);
        }
        return dataQuality;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataQuality create(DataQuality dataQuality) {
        if (dataQuality.getPid() != -1L && dataQualityMenuMapper.selectById(dataQuality.getPid()) == null) {
            throw new ServiceException(Status.QUALITY_MENU_DOES_NOT_EXIST);
        }
        if (dataQualityMapper.selectByCode(dataQuality.getCode()) != null) {
            throw new ServiceException(Status.QUALITY_ALREADY_EXISTS);
        }
        if (dataQuality.getIsCiteStandard() != null &&
                dataQuality.getIsCiteStandard() && dataQuality.getStandardId() == null) {
            throw new ServiceException(Status.THE_CRITERIA_THAT_NEED_TO_BE_REFERENCED_ARE_NOT_SPECIFIED);
        }

        dataQuality.setId(IdWorker.getId());
        int insert = dataQualityMapper.insertQuality(dataQuality);
        // 创建元素边关系
        if (insert > 0) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            if (dataQuality.getPid() != -1L) {
                dataQualityMapper.createQualityAndMenuEdge(dataQuality.getPid(), Arrays.asList(dataQuality.getId()));
            }

            // 添加规则和标准的关系
            if (dataQuality.getIsCiteStandard() != null && dataQuality.getIsCiteStandard()) {
                dataQualityMapper.createQualityAndStandardEdge(dataQuality.getId(), dataQuality.getStandardId());
            }
        }
        return dataQuality;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataQuality dataQuality) {
        DataQuality checkDataQuality = dataQualityMapper.selectById(dataQuality.getId());
        if (checkDataQuality == null) {
            throw new ServiceException(Status.QUALITY_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataQuality.getCode()) && dataQualityMapper.checkCodeIsExists(dataQuality.getId(), dataQuality.getCode()) != null) {
            throw new ServiceException(Status.QUALITY_ALREADY_EXISTS);
        }
        if (dataQuality.getIsCiteStandard() != null &&
                dataQuality.getIsCiteStandard() && dataQuality.getStandardId() == null) {
            throw new ServiceException(Status.THE_CRITERIA_THAT_NEED_TO_BE_REFERENCED_ARE_NOT_SPECIFIED);
        }
        // 不能拖拽到节点里面
        if (dataQuality.getPid() != null && dataQuality.getPid() != -1L && dataQualityMenuMapper.selectById(dataQuality.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_QUALITY_MENU);
        }

        // 转义正则表达式的特殊符号
        // if (StringUtils.isNotEmpty(dataQuality.getRegexRule())) {
        //     if (dataQuality.getRegexRule().contains("\\")) {
        //         dataQuality.setRegexRule(dataQuality.getRegexRule().replace("\\", "\\\\"));
        //     }
        //     if (dataQuality.getRegexRule().contains("'")) {
        //         dataQuality.setRegexRule(dataQuality.getRegexRule().replace("'", "\\'"));
        //     }
        // }

        int update = dataQualityMapper.updateById(dataQuality);
        if (update > 0) {
            // 创建规则和标准的关系
            if (dataQuality.getIsCiteStandard() != null && dataQuality.getIsCiteStandard()) {
                this.bindQualityAndStandardEdge(dataQuality.getId(), dataQuality.getStandardId());
            }
            if (dataQuality.getIsCiteStandard() == null || !dataQuality.getIsCiteStandard()) {
                // 先删除关系
                dataQualityMapper.deleteQualityAndStandardEdge(dataQuality.getId());
            }

            if (dataQuality.getPid() != null) {
                // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
                dataQualityMapper.deleteQualityAndMenuEdge(Arrays.asList(dataQuality.getId()), checkDataQuality.getPid());
                if (dataQuality.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataQualityMapper.createQualityAndMenuEdge(dataQuality.getPid(), Arrays.asList(dataQuality.getId()));
                }
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindQualityAndStandardEdge(Long qualityId, Long standardId) {
        // 先删除关系
        dataQualityMapper.deleteQualityAndStandardEdge(qualityId);
        // 在创建关系
        dataQualityMapper.createQualityAndStandardEdge(qualityId, standardId);
    }

    @Override
    public int copy(Long pid, List<DataQualityCopyVo> dataQualityCopyVoList) {
        // 检查菜单
        DataQualityMenu dataQualityMenu = dataQualityMenuMapper.selectById(pid);
        if (dataQualityMenu == null) {
            throw new ServiceException(Status.QUALITY_MENU_DOES_NOT_EXIST);
        }

        if (CollectionUtils.isEmpty(dataQualityCopyVoList)) {
            throw new ServiceException(Status.THE_COPIED_RULE_INFORMATION_CANNOT_BE_EMPTY);
        }

        for (DataQualityCopyVo dataQualityCopyVo : dataQualityCopyVoList) {
            // 检查实体
            DataQuality dataQuality = checkQuality(dataQualityCopyVo.getCodeNodeId());
            // 设置修改后的pid
            dataQuality.setPid(pid);

            // 修改基本信息
            dataQuality.setCode(dataQualityCopyVo.getCode());
            dataQuality.setName(dataQualityCopyVo.getName());
            dataQuality.setDescription(dataQualityCopyVo.getDescription());
            // 创建节点
            DataQuality copyDataQuality = this.create(dataQuality);

            // 创建规则和标准的关系
            if (copyDataQuality.getIsCiteStandard()) {
                this.bindQualityAndStandardEdge(copyDataQuality.getId(), copyDataQuality.getStandardId());
            }

            // 绑定元素
            List<DataElementStructureVo> dataElementStructureVoList = this.selectQualityElement(dataQualityCopyVo.getCodeNodeId(), null, null);
            if (CollectionUtils.isNotEmpty(dataElementStructureVoList)) {
                this.bindQualityElement(copyDataQuality.getId(), dataElementStructureVoList);
            }
        }
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        return dataQualityMapper.deleteBatchIds(ids);
    }

    @Override
    public DataQuality selectById(Long id) {
        // 校验规则
        DataQuality dataQuality = checkQuality(id);
        // 获取类型
        if (dataQuality.getIsCiteStandard()) {
            // Long standardId = dataQualityMapper.selectStandardId(id);
            // if (standardId == 0L) {
            //     throw new ServiceException(Status.THE_DATA_STANDARD_REFERENCED_BY_THE_RULE_DOES_NOT_EXIST);
            // }
            // dataQuality.setStandardId(standardId);
            // dataQuality.setIsConfigType(true);
            R<DataStandardTemporary> dataStandardTemporaryR = remoteStandardService.selectById(dataQuality.getStandardId());
            if (dataStandardTemporaryR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(dataStandardTemporaryR.getMsg());
            }
            DataStandardTemporary dataStandardTemporary = dataStandardTemporaryR.getData();
            dataQuality.setFieldType(dataStandardTemporary.getFieldType());
            Long length = dataStandardTemporary.getLength();
            dataQuality.setLength(length != null ? length : 0L);
            Long decimalLength = dataStandardTemporary.getDecimalLength();
            dataQuality.setDecimalLength(decimalLength != null ? decimalLength : 0L);
        }
        return dataQuality;
    }

    @Override
    public String getRegex(Long id, String prefix) {
        // 校验规则
        DataQuality dataQuality = checkQuality(id);
        return getRegex(prefix, dataQuality.getRegexRule());
    }

    @Override
    public Boolean checkRegex(CheckRegexVo checkRegexVo) {
        RegexRule regexRule = checkRegexVo.getRegexRule();
        String content = checkRegexVo.getContent();
        return checkRegex(regexRule, content);
    }

    /**
     * 校验正则表达式
     *
     * @param regexRule
     * @param content
     * @return
     */
    private Boolean checkRegex(RegexRule regexRule, String content) {
        // 解析第一层
        List<RegexRule> oneChildren = regexRule.getChildren();
        List<Boolean> oneFlags = new ArrayList<>();
        for (RegexRule oneChild : oneChildren) {
            // 解析第二层
            List<RegexValue> twoChildren = oneChild.getCondition();
            List<Boolean> twoFlags = new ArrayList<>();
            for (RegexValue regexValueObj : twoChildren) {
                // 为空不拼接sql
                if (regexValueObj == null) {
                    twoFlags.add(false);
                    continue;
                }
                String regexValue = regexValueObj.getRegexValue();
                // 如果是引用需要查询参数的值
                if (regexValueObj.getCiteType() == RegexRuleCiteType.CITE) {
                    R<GlobalParam> globalParamR = remoteParamService.selectById(Long.valueOf(regexValue));
                    if (globalParamR.getCode() != Status.SUCCESS.getCode()) {
                        throw new ServiceException(globalParamR.getMsg());
                    }
                    regexValue = globalParamR.getData().getValue().toString();
                }
                // 校验正则是否正确
                twoFlags.add(content.matches(regexValue));
            }

            // 二组关系的结果
            Boolean twoFlag;
            if (oneChild.getRelation() == RegexRelationType.AND) {
                twoFlag = twoFlags.stream().allMatch(two -> two == true);
            } else if (oneChild.getRelation() == RegexRelationType.OR) {
                twoFlag = twoFlags.stream().anyMatch(two -> two == true);
            } else {
                throw new ServiceException(Status.RELATIONSHIPS_CAN_ONLY_BE_AND_AND_OR);
            }
            oneFlags.add(twoFlag);
        }
        // 一组关系的结果
        Boolean oneFlag;
        if (regexRule.getRelation() == RegexRelationType.AND) {
            oneFlag = oneFlags.stream().allMatch(one -> one == true);
        } else if (regexRule.getRelation() == RegexRelationType.OR) {
            oneFlag = oneFlags.stream().anyMatch(one -> one == true);
        } else {
            throw new ServiceException(Status.RELATIONSHIPS_CAN_ONLY_BE_AND_AND_OR);
        }
        return oneFlag;
    }

    /**
     * 获取正则表达式
     *
     * @param regexRule
     * @return
     */
    private String getRegex(String prefix, RegexRule regexRule) {
        // 结果
        String regex = "";
        // 解析第一层
        List<RegexRule> oneChildren = regexRule.getChildren();
        for (RegexRule oneChild : oneChildren) {
            regex += "(";
            // 解析第二层
            List<RegexValue> twoChildren = oneChild.getCondition();
            for (RegexValue regexValueObj : twoChildren) {
                // 为空不拼接sql
                if (regexValueObj == null) {
                    continue;
                }
                regex += "(";
                String regexValue = regexValueObj.getRegexValue();
                // 如果是引用需要查询参数的值
                if (regexValueObj.getCiteType() == RegexRuleCiteType.CITE) {
                    R<GlobalParam> globalParamR = remoteParamService.selectById(Long.valueOf(regexValue));
                    if (globalParamR.getCode() != Status.SUCCESS.getCode()) {
                        throw new ServiceException(globalParamR.getMsg());
                    }
                    regexValue = globalParamR.getData().getValue().toString();
                }
                // 过滤正则表达式前面的^和后面的$，方便后面flinksql执行
                if ('^' == regexValue.charAt(0)) {
                    regexValue = regexValue.substring(1);
                }
                if ('$' == regexValue.charAt(regexValue.length() - 1)) {
                    regexValue = regexValue.substring(0, regexValue.length() - 1);
                }
                regex += prefix + " '" + regexValue + "')" + oneChild.getRelation().name();
            }
            regex = regex.substring(0, regex.length() - oneChild.getRelation().name().length());

            regex += ")" + regexRule.getRelation().name();
        }
        regex = regex.substring(0, regex.length() - regexRule.getRelation().name().length());
        return regex;
    }

    @Override
    public List<DataElementStructureVo> selectQualityElement(Long id, String code, String name) {
        // 校验规则
        checkQuality(id);
        List<DataElementStructureVo> dataElementStructureVoList = new ArrayList<>();
        dataElementStructureVoList.addAll(dataQualityMapper.selectQualityElement(id, null, null, null, true));
        dataElementStructureVoList.addAll(dataQualityMapper.selectQualityElement(id, null, code, name, false));
        if (CollectionUtils.isEmpty(dataElementStructureVoList)) {
            return new ArrayList<>();
        }
        // 顶级节点
        List<DataElementStructureVo> topNodeList = dataElementStructureVoList.stream()
                .filter(dataElementStructureVo -> dataElementStructureVo.getIsTree())
                .collect(Collectors.toList());

        for (DataElementStructureVo topNode : topNodeList) {
            // 字段类型没有子节点
            if (topNode.getDataElementType() == DataElementType.FIELD) {
                continue;
            }
            // 当前顶级几点下的所有子节点
            List<DataElementStructureVo> subNodeList = dataElementStructureVoList.stream()
                    .filter(dataElementStructureVo -> topNode.getId().equals(dataElementStructureVo.getParentDataElementId()))
                    .collect(Collectors.toList());
            // 排序
            subNodeList = subNodeList.stream()
                    .map(subNode -> {
                        subNode.setChildren(null);
                        return subNode;
                    }).sorted(Comparator.comparing(DataElementStructureVo::getSeq))
                    .collect(Collectors.toList());
            topNode.getChildren().addAll(subNodeList);
        }

        return topNodeList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int bindQualityElement(Long id, List<DataElementStructureVo> dataElementStructureVoList) {

        // 校验规则
        checkQuality(id);

        // 校验是否重复
        int distinct = dataElementStructureVoList.stream().map(DataElementStructureVo::getId).distinct().collect(Collectors.toList()).size();
        if (distinct != dataElementStructureVoList.size()) {
            throw new ServiceException(Status.THE_SAME_FIELD_EXISTS_IN_THE_QUALITY);
        }

        int insert = 0;
        for (DataElementStructureVo dataElementStructureVo : dataElementStructureVoList) {
            DataElementType dataElementType = dataElementStructureVo.getDataElementType();
            // 主数据类型
            switch (dataElementType) {
                case MAIN:
                    // 判断是否存在顶级的树边，存在则只需要添加子级，不存在则添加树边
                    if (dataQualityMapper.checkQualityElementEdge(id, dataElementStructureVo.getId(), null, true)) {
                        // 添加顶级节点的树边
                        insert += dataQualityMapper.insertTreeQualityElementEdge(id, dataElementStructureVo);
                    }
                    // 添加子节点
                    List<DataElementStructureVo> subDataElementStructureVoList = dataElementStructureVo.getChildren();
                    for (DataElementStructureVo subElementStructureVo : subDataElementStructureVoList) {
                        // 设置parentDataElementId
                        subElementStructureVo.setParentDataElementId(dataElementStructureVo.getId());
                        DataElementType subDataElementType = subElementStructureVo.getDataElementType();
                        switch (subDataElementType) {
                            // 建立 (d_g_data_govern)-[d_g_data_govern_element_edge]-(d_e_data_element)的双边关系
                            case MAIN:
                            case FIELD:
                                // 检验元素中是否存在这个字段,true代表不存在,false代表存在
                                if (!dataQualityMapper.checkQualityElementEdge(id, subElementStructureVo.getId(), dataElementStructureVo.getId(), false)) {
                                    throw new ServiceException(Status.THE_SAME_FIELD_EXISTS_IN_THE_QUALITY);
                                }
                                // 重新赋予序号
                                subElementStructureVo.setSeq(getMaxSeq(id, subElementStructureVo.getIsPk()));
                                insert += dataQualityMapper.insertQualityElementEdge(id, subElementStructureVo);
                                break;
                            case INPUT:
                                throw new ServiceException("不能引入录入类型字段");
                        }
                    }
                    break;
                case FIELD:
                    if (!dataQualityMapper.checkQualityElementEdge(id, dataElementStructureVo.getId(), null, true)) {
                        throw new ServiceException(Status.THE_SAME_FIELD_EXISTS_IN_THE_QUALITY);
                    }
                    // 重新赋予序号
                    // dataElementStructureVo.setSeq(getMaxSeq(id, dataElementStructureVo.getIsPk()));
                    insert += dataQualityMapper.insertTreeQualityElementEdge(id, dataElementStructureVo);
                    break;
                default:
                    break;
            }
        }

        return insert;
    }

    /**
     * 获取规则中的主键/非主键的最大的排序序号
     *
     * @param id
     * @param isPk
     * @return
     */
    private Integer getMaxSeq(Long id, Boolean isPk) {
        List<Integer> seqs = dataQualityMapper.selectQualityAllMaxSeq(id, isPk);
        return seqs.stream().max(Integer::compare).get() + 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int unbindQualityElement(Long id, List<DataElementStructureVo> dataElementStructureVoList) {
        // 校验规则
        checkQuality(id);

        int delete = 0;
        for (DataElementStructureVo dataElementStructureVo : dataElementStructureVoList) {
            DataElementType dataElementType = dataElementStructureVo.getDataElementType();
            // 主数据类型
            switch (dataElementType) {
                // 解绑 (d_g_data_govern)-[d_g_data_govern_element_edge]-(d_e_data_element)的双边关系
                case MAIN:
                case FIELD:
                    delete += dataQualityMapper.deleteQualityElementEdge(id, dataElementStructureVo);
                    break;
            }
        }

        return delete;
    }

    @Override
    public List<DataEntityQuality> parseEntityQuality(Long dataEntityId) {

        // 验证数据实体
        R<DataEntity> dataEntityR = remoteEntityService.selectById(dataEntityId);
        if (dataEntityR.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(dataEntityR.getMsg());
        }

        // 返回结构
        List<DataEntityQuality> dataEntityQualityList = new ArrayList<>();

        // 根据数据实体id查询数据实体引用的子段
        R<List<DataEntityStructureVo>> selectStructureByIdR = remoteEntityStrureService.selectStructureById(dataEntityId);
        if (selectStructureByIdR.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(selectStructureByIdR.getMsg());
        }

        List<DataEntityStructureVo> dataEntityStructureVos = selectStructureByIdR.getData();
        // 查询是否存在预定义的字段
        boolean isExistsPredefined = dataEntityStructureVos.stream()
                .anyMatch(dataEntityStructureVo -> dataEntityStructureVo.getEntityInsertType() == EntityInsertType.PREDEFINED);
        if (!isExistsPredefined) {
            return dataEntityQualityList;
        }

        // 所有预定义的字段，并且不包含录入类型的字段
        List<DataEntityStructureVo> structureVoList = dataEntityStructureVos.stream()
                .filter(dataEntityStructureVo -> dataEntityStructureVo.getEntityInsertType() == EntityInsertType.PREDEFINED)
                .filter(dataEntityStructureVo -> dataEntityStructureVo.getDataElementType() != DataElementType.INPUT)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(structureVoList)) {
            return dataEntityQualityList;
        }

        List<DataElementStructureVo> dataElementStructureVoList = new ArrayList<>();
        dataElementStructureVoList.addAll(dataQualityMapper.selectQualityElement(null,
                structureVoList.stream().map(DataEntityStructureVo::getId).collect(Collectors.toList()),
                null, null, false));
        dataElementStructureVoList.addAll(dataQualityMapper.selectQualityFieldElement(
                structureVoList.stream()
                        .filter(dataEntityStructureVo -> dataEntityStructureVo.getDataElementType() == DataElementType.FIELD)
                        .map(DataEntityStructureVo::getId).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(dataElementStructureVoList)) {
            return dataEntityQualityList;
        }

        // 转换成DataEntityQuality对象
        dataEntityQualityList = dataElementStructureVoList.stream()
                .map(structureVo -> {
                    return new DataEntityQuality(structureVo.getId(),
                            structureVo.getCode(),
                            structureVo.getName(),
                            structureVo.getDescription(),
                            structureVo.getDataElementType(),
                            structureVo.getFieldType());
                }).collect(Collectors.toList());

        for (DataEntityQuality dataEntityQuality : dataEntityQualityList) {
            // 根据字段id查询对应的所有规则
            dataEntityQuality.setDataQualityDtoList(dataQualityMapper.selectQualityByElementId(dataEntityQuality.getId()));
        }

        return dataEntityQualityList;
    }

    @Override
    public List<DataEntityQuality> parseElementQuality(Long elementId) {
        // 验证元素
        R<DataElement> r = remoteElementService.selectById(elementId);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        //返回结构
        List<DataEntityQuality> dataEntityQualityList = new ArrayList<>();

        R<List<DataElementStructure>> listR = remoteElementStrureService.list(elementId);
        List<DataElementStructure> structureList = listR.getData();

        if (listR.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(listR.getMsg());
        }

        // 转换成DataEntityQuality对象
        dataEntityQualityList = structureList.stream()
                .map(structureVo -> {
                    return new DataEntityQuality(structureVo.getId(),
                            structureVo.getCode(),
                            structureVo.getName(),
                            structureVo.getDescription(),
                            structureVo.getDataElementType(),
                            structureVo.getFieldType());
                }).collect(Collectors.toList());

        for (DataEntityQuality dataEntityQuality : dataEntityQualityList) {
            // 根据字段id查询对应的所有规则
            dataEntityQuality.setDataQualityDtoList(dataQualityMapper.selectQualityByElementId(dataEntityQuality.getId()));
        }

        return dataEntityQualityList;
    }

}
