package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityRelation;
import com.datalink.fdop.element.api.domain.DataEntityStructure;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.datalink.fdop.element.api.enums.EntityType;
import com.datalink.fdop.element.api.model.dto.DataEntityStructureDto;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataEntityStructureService {

    int create(DataEntityStructure dataEntityStructure);

    int batchCreate(Long dataEntityId, List<DataEntityStructure> dataEntityStructureList);

    int create(DataEntityStructure dataEntityStructure, Boolean isCheckLength);

    Map<String, Object> checkEntityStructure(Long dataEntityId, List<DataEntityStructureVo> dataEntityStructureVoList);

    int update(DataEntityStructure dataEntityStructure);

    int delete(Long dataEntityId, List<DataEntityStructureVo> dataEntityStructureVoList);

    int deleteL5Id(List<Long> ids);

    int addPredefined(Long dataEntityId, List<DataElementStructureVo> dataElementStructureVoList);

    int addPredefined(Long dataEntityId, List<DataElementStructureVo> dataElementStructureVoList, Boolean coverSeq);

    List<DataEntityStructureVo> selectStructureById(Long dataEntityId, Boolean isSort);

    /**
     * 根据租户id查询数据实体的结构
     *
     * @param tenantId     租户ID
     * @param dataEntityId 实体ID
     * @param isSort       排序
     */
    List<DataEntityStructureVo> selectStructureByIdAndTenantId(Long tenantId, Long dataEntityId, Boolean isSort);

    PageDataInfo<DataEntityStructureVo> selectStructureByIdPaging(Long dataEntityId, String sort, DataEntityStructureDto dataEntityStructureDto);

    int updateEntityElementEdge(List<DataElementStructure> dataElementStructureList);

    int importEntity(MultipartFile file, Long dataEntityId);

    void exportEntity(HttpServletResponse response, List<Long> dataEntityId, Boolean isExportData);

    List<DataEntityStructureVo> selectEntityMappingField(Long dataEntityId);

    String createByL5(Long dataEntityId, List<Long> ids);

    int createEntityRelation(List<DataEntityRelation> dataEntityRelations);

    int updateEntityRelation(List<DataEntityRelation> dataEntityRelations);

    int deleteEntityRelation(List<Long> ids);

    PageDataInfo<DataEntityRelation> queryEntityRelation(Long dataEntityId, EntityType entityType, DataEntityRelation dataEntityRelation);

    List<DataEntityRelation> queryEntityRelationByList(Long dataEntityId, EntityType entityType, DataEntityRelation dataEntityRelation, boolean useGovern);

    /**
     * 根据目标实体字段获取来源字段关系
     *
     * @param dataEntities 实体对象集合
     * @param entityType   实体类型
     * @return
     */
    List<DataEntityRelation> queryEntityRelationByTarget(List<DataEntity> dataEntities, EntityType entityType);

    /**
     * 根据来源实体字段获取目标字段关系
     *
     * @param dataEntities 实体对象集合
     * @param entityType   实体类型
     * @return
     */
    List<DataEntityRelation> queryEntityRelationBySource(List<DataEntity> dataEntities, EntityType entityType);

    /**
     * 按实体ID和插入类型删除字段结构
     *
     * @param dataEntityId     实体ID
     * @return 删除数量
     */
    int deleteByEntityIdAndInsertType(Long dataEntityId);

}
