package com.datalink.fdop.graph.controller;

import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.graph.api.domain.etl.*;
import com.datalink.fdop.graph.api.vo.EtlNodeVo;
import com.datalink.fdop.graph.service.IEtlService;
import com.datalink.fdop.param.api.model.vo.ParamVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/17 19:43
 */
@RestController
@RequestMapping(value = "/graph/etl/v2/{taskCode}")
@Api(tags = "ETL API")
public class EtlController extends BaseController {

    @Autowired
    private IEtlService etlService;

    @ApiOperation("创建ETL节点")
    @Log(title = "Flink Graph Center", businessType = BusinessType.INSERT)
    @PostMapping(value = "/createEtlNode")
    public R<EtlNode> createEtlNode(@PathVariable(value = "taskCode") long taskCode,
                                    @Validated @RequestBody EtlNode etlNode) {
        return R.ok(etlService.createEtlNode(taskCode, etlNode));
    }

    @ApiOperation("修改SOURCE节点")
    @Log(title = "Flink Graph Center", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateSourceNode")
    public R<SourceNode> updateSourceNode(@PathVariable(value = "taskCode") long taskCode,
                                          @RequestBody SourceNode sourceNode) {
        return R.ok(etlService.updateSourceNode(taskCode, sourceNode));
    }

    @ApiOperation("修改TRANSFORM节点")
    @Log(title = "Flink Graph Center", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateTransformNode")
    public R<TransformNode> updateTransformNode(@PathVariable(value = "taskCode") long taskCode,
                                                @RequestBody TransformNode transformNode) {
        return R.ok(etlService.updateTransformNode(taskCode, transformNode));
    }

    @ApiOperation("修改SINK节点")
    @Log(title = "Flink Graph Center", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateSinkNode")
    public R<SinkNode> updateSinkNode(@PathVariable(value = "taskCode") long taskCode,
                                      @RequestBody SinkNode sinkNode) {
        return R.ok(etlService.updateSinkNode(taskCode, sinkNode));
    }

    @ApiOperation("修改PARAMS节点")
    @Log(title = "Flink Graph Center", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateParamsNode")
    public R<ParamsNode> updateParamsNode(@PathVariable(value = "taskCode") long taskCode,
                                          @RequestBody ParamsNode paramsNode) {
        return R.ok(etlService.updateParamsNode(taskCode, paramsNode));
    }

    @ApiOperation("删除ETL节点")
    @Log(title = "Flink Graph Center", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deleteEtlNode")
    public R deleteEtlNode(@PathVariable(value = "taskCode") long taskCode,
                           @RequestParam(value = "nodeId") String nodeId) {
        etlService.deleteEtlNode(taskCode, nodeId);
        return R.ok();
    }

    @ApiOperation("创建节点之间的关系")
    @Log(title = "Flink Graph Center", businessType = BusinessType.INSERT)
    @PostMapping(value = "/createNodeEdge")
    public R<String> createNodeEdge(@PathVariable(value = "taskCode") long taskCode,
                                    @RequestParam(value = "sourceNodeId") String sourceNodeId,
                                    @RequestParam(value = "targetNodeId") String targetNodeId,
                                    @RequestBody Map<String, Object> frontData) {
        return R.ok(etlService.createNodeEdge(taskCode, sourceNodeId, targetNodeId, JSONObject.toJSONString(frontData)),
                Status.SUCCESS.getMsg());
    }

    @ApiOperation("删除节点之间的关系")
    @Log(title = "Flink Graph Center", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deleteNodeEdge")
    public R deleteNodeEdge(@PathVariable(value = "taskCode") long taskCode,
                            @RequestParam(value = "sourceNodeId") String sourceNodeId,
                            @RequestParam(value = "targetNodeId") String targetNodeId) {
        etlService.deleteNodeEdge(taskCode, sourceNodeId, targetNodeId);
        return R.ok();
    }

    @ApiOperation("查询ETL节点信息")
    @Log(title = "Flink Graph Center")
    @GetMapping(value = "/selectEtlNode")
    public R<EtlNode> selectEtlNode(@PathVariable(value = "taskCode") long taskCode,
                                    @RequestParam(value = "nodeId") String nodeId) {
        return R.ok(etlService.selectEtlNode(taskCode, nodeId));
    }

    @ApiOperation("查询SOURCE节点信息")
    @Log(title = "Flink Graph Center")
    @GetMapping(value = "/selectSourceNode")
    public R<SourceNode> selectSourceNode(@PathVariable(value = "taskCode") long taskCode,
                                          @RequestParam(value = "nodeId") String nodeId) {
        return R.ok(etlService.selectSourceNode(taskCode, nodeId));
    }

    @ApiOperation("查询SINK节点信息")
    @Log(title = "Flink Graph Center")
    @GetMapping(value = "/selectSinkNode")
    public R<SinkNode> selectSinkNode(@PathVariable(value = "taskCode") long taskCode,
                                      @RequestParam(value = "nodeId") String nodeId) {
        return R.ok(etlService.selectSinkNode(taskCode, nodeId));
    }

    @ApiOperation("查询PARAMS节点信息")
    @Log(title = "Flink Graph Center")
    @GetMapping(value = "/selectParamsNode")
    public R<ParamsNode> selectParamsNode(@PathVariable(value = "taskCode") long taskCode,
                                          @RequestParam(value = "nodeId") String nodeId) {
        return R.ok(etlService.selectParamsNode(taskCode, nodeId));
    }

    @ApiOperation("获取所有ETL节点信息")
    @Log(title = "Flink Graph Center")
    @GetMapping(value = "/selectEtlNodeList")
    public R<EtlNodeVo> selectEtlNodeList(@PathVariable(value = "taskCode") long taskCode) {
        return R.ok(etlService.selectEtlNodeList(taskCode));
    }

    @ApiOperation("修改ETL任务的状态")
    @Log(title = "Flink Graph Center")
    @PostMapping(value = "/updateEtlStatus")
    public R updateEtlStatus(@PathVariable(value = "taskCode") long taskCode) {
        etlService.updateEtlStatus(taskCode);
        return R.ok();
    }

    @ApiOperation(value = "解析ETL任务参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "string", value = "包含参数的字符串", required = true, dataType = "String", paramType = "body"),
    })
    @PostMapping(value = "/parseEtlTaskParams")
    @Log(title = "解析ETL任务参数")
    public R<List<ParamVo>> parseEtlTaskParams(@PathVariable(value = "taskCode") long taskCode) {
        return R.ok(etlService.parseEtlTaskParams(taskCode));
    }

}
