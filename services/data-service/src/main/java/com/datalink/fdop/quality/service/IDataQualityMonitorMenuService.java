package com.datalink.fdop.quality.service;


import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.quality.api.domain.DataQualityMonitor;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorMenu;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorTree;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IDataQualityMonitorMenuService {

    Integer querySerialNumber();

    int create(DataQualityMonitorMenu dataQualityMonitorMenu);

    int update(DataQualityMonitorMenu dataQualityMonitorMenu);

    int delete(List<Long> ids);

    List<DataQualityMonitorTree> tree(String sort, String code, Boolean isQueryNode);

    PageDataInfo<DataQualityMonitor> overview(Long pid, String sort, SearchVo searchVo);

}
