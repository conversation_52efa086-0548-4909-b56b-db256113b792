package com.datalink.fdop.quality.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.model.dto.DataEntityQuality;
import com.datalink.fdop.quality.api.model.vo.CheckRegexVo;
import com.datalink.fdop.quality.api.model.vo.DataQualityCopyVo;
import com.datalink.fdop.quality.service.IDataQualityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11 14:22
 */
@RequestMapping("/quality")
@RestController
@Api(tags = "规则api")
public class DataQualityController extends BaseController {

    @Autowired
    private IDataQualityService dataQualityService;

    @ApiOperation("创建规则")
    @Log(title = "数据质量", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R<DataQuality> create(@Validated @RequestBody DataQuality dataQuality) {
        if (dataQuality.getPid() == null) {
            dataQuality.setPid(-1L);
        }
        return R.ok(dataQualityService.create(dataQuality));
    }

    @ApiOperation("修改规则")
    @Log(title = "数据质量", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataQuality dataQuality) {
        if (dataQuality.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(dataQualityService.update(dataQuality));
    }

    @ApiOperation("复制规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pid", value = "实体菜单id", required = true, dataType = "Long", paramType = "query", example = "1"),
    })
    @Log(title = "数据质量", businessType = BusinessType.INSERT)
    @PostMapping(value = "/copy/{pid}")
    public R copy(@PathVariable(value = "pid") Long pid, @Validated @RequestBody List<DataQualityCopyVo> dataQualityCopyVoList) {
        dataQualityService.copy(pid, dataQualityCopyVoList);
        return R.ok();
    }

    @ApiOperation("删除规则")
    @Log(title = "数据质量", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.LEASE_SPECIFY_THE_QUALITY_TO_DELETE);
        }
        return R.toResult(dataQualityService.delete(ids));
    }

    @ApiOperation("根据id查询规则信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则id", required = true, dataType = "Long", paramType = "path"),
    })
    @Log(title = "数据质量")
    @GetMapping(value = "/selectById/{id}")
    public R<DataQuality> selectById(@PathVariable("id") Long id) {
        return R.ok(dataQualityService.selectById(id));
    }

    @ApiOperation("获取正则表达式")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则id", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "prefix", value = "前缀", required = true, dataType = "String", paramType = "query"),
    })
    @Log(title = "数据质量")
    @GetMapping(value = "/getRegex/{id}")
    public R<String> getRegex(@PathVariable("id") Long id, @RequestParam(value = "prefix") String prefix) {
        return R.ok(dataQualityService.getRegex(id, prefix), Status.SUCCESS.getMsg());
    }

    @ApiOperation("验证正则表达式")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "checkRegexVo", value = "正则表达式Vo对象", required = true, dataTypeClass = CheckRegexVo.class, paramType = "body"),
    })
    @Log(title = "数据质量")
    @PostMapping(value = "/checkRegex")
    public R<Boolean> checkRegex(@RequestBody CheckRegexVo checkRegexVo) {
        return R.ok(dataQualityService.checkRegex(checkRegexVo));
    }

    @ApiOperation("查询规则和元素的关系")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则id", required = true, dataType = "Long", paramType = "path"),
    })
    @Log(title = "数据质量")
    @GetMapping(value = "/selectQualityElement/{id}")
    public R<List<DataElementStructureVo>> selectQualityElement(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name
    ) {
        return R.ok(dataQualityService.selectQualityElement(id, code, name));
    }

    @ApiOperation("绑定规则和元素的关系")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则id", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "dataElementStructureVoList", value = "数据元素vo对象集合", required = true, allowMultiple = true, dataTypeClass = DataElementStructureVo.class, paramType = "body"),
    })
    @Log(title = "数据质量")
    @PostMapping(value = "/bindQualityElement/{id}")
    public R bindQualityElement(@PathVariable(value = "id") Long id, @RequestBody List<DataElementStructureVo> dataElementStructureVoList) {
        return R.toResult(dataQualityService.bindQualityElement(id, dataElementStructureVoList));
    }

    @ApiOperation("解绑规则和元素的关系")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则id", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "dataElementStructureVoList", value = "数据元素vo对象集合", required = true, allowMultiple = true, dataTypeClass = DataElementStructureVo.class, paramType = "body"),
    })
    @Log(title = "数据质量")
    @PostMapping(value = "/unbindQualityElement/{id}")
    public R unbindQualityElement(@PathVariable(value = "id") Long id, @RequestBody List<DataElementStructureVo> dataElementStructureVoList) {
        return R.toResult(dataQualityService.unbindQualityElement(id, dataElementStructureVoList));
    }

    @ApiOperation("解析实体中的规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataEntityId", value = "数据实体id", required = true, dataType = "Long", paramType = "path"),
    })
    @Log(title = "数据质量")
    @GetMapping(value = "/parseEntityQuality/{dataEntityId}")
    public R<List<DataEntityQuality>> parseEntityQuality(@PathVariable(value = "dataEntityId") Long dataEntityId) {
        return R.ok(dataQualityService.parseEntityQuality(dataEntityId));
    }

}
