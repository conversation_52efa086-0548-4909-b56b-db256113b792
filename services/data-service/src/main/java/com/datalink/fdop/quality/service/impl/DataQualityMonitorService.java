package com.datalink.fdop.quality.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.age.SqlUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.RemoteTaskAndProcessService;
import com.datalink.fdop.quality.api.domain.DataQualityMonitor;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorMenu;
import com.datalink.fdop.quality.api.model.vo.DataQualityMonitorCopyVo;
import com.datalink.fdop.quality.api.model.vo.MonitorTableOrVIewVo;
import com.datalink.fdop.quality.mapper.DataQualityMonitorMapper;
import com.datalink.fdop.quality.mapper.DataQualityMonitorMenuMapper;
import com.datalink.fdop.quality.service.IDataQualityMonitorService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.dolphinscheduler.fdop.api.RemoteProjectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
public class DataQualityMonitorService implements IDataQualityMonitorService {

    private static final Logger logger = LoggerFactory.getLogger(DataQualityMonitorService.class);

    @Autowired
    private DataQualityMonitorMapper dataQualityMonitorMapper;

    @Autowired
    private DataQualityMonitorMenuMapper dataQualityMonitorMenuMapper;

    @Autowired
    private RemoteTaskAndProcessService remoteTaskAndProcessService;

    @Autowired
    private RemoteProjectService remoteProjectService;


    @Override
    public Integer querySerialNumber() {
        return dataQualityMonitorMapper.querySerialNumber();
    }

    /**
     * 校验规则监控
     *
     * @param id
     * @return
     */
    private DataQualityMonitor checkQualityMonitor(Long id) {
        DataQualityMonitor dataQualityMonitor = dataQualityMonitorMapper.selectById(id);
        if (dataQualityMonitor == null) {
            throw new ServiceException(Status.RULE_MONITORING_DOES_NOT_EXIST);
        }
        return dataQualityMonitor;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataQualityMonitor create(DataQualityMonitor dataQualityMonitor) {
        if (dataQualityMonitor.getPid() != -1L && dataQualityMonitorMenuMapper.selectById(dataQualityMonitor.getPid()) == null) {
            throw new ServiceException(Status.THE_DATA_RULE_MONITORING_MENU_DOES_NOT_EXIST);
        }
        if (dataQualityMonitorMapper.selectByCode(dataQualityMonitor.getCode()) != null) {
            throw new ServiceException(Status.RULE_MONITORING_ALREADY_EXISTS);
        }
        dataQualityMonitor.setId(IdWorker.getId());
        if (dataQualityMonitor.getSerialNumber() == null) {
            dataQualityMonitor.setSerialNumber(this.querySerialNumber());
        }
        int insert = dataQualityMonitorMapper.insertQualityMonitor(dataQualityMonitor);
        // 创建元素边关系
        if (insert > 0 && dataQualityMonitor.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            dataQualityMonitorMapper.createQualityMonitorAndMenuEdge(dataQualityMonitor.getPid(), Arrays.asList(dataQualityMonitor.getId()));

            // 获取projectCode
            R queryProjectByNameR = remoteProjectService.queryProjectByName(SecurityUtils.getTenantId().toString());
            if (queryProjectByNameR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(queryProjectByNameR.getMsg());
            }
            Map projectMap = (Map) queryProjectByNameR.getData();
            Long projectCode = MapUtils.getLong(projectMap, "code");
            // 创建规则监控的任务、工作流、定时
            R ruleTaskAndProcessR = remoteTaskAndProcessService.createRuleTaskAndProcess(projectCode, dataQualityMonitor.getId());
            if (ruleTaskAndProcessR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(ruleTaskAndProcessR.getMsg());
            }
            // 获取规则监控的任务code、工作流code、定时id
            Map idMap = (Map) ruleTaskAndProcessR.getData();
            Long taskCode = MapUtils.getLong(idMap, "taskCode");
            Long processCode = MapUtils.getLong(idMap, "processCode");
            Integer scheduleId = MapUtils.getInteger(idMap, "scheduleId");

            // 创建监控和任务的关系
            dataQualityMonitorMapper.createMonitorAndTaskEdge(dataQualityMonitor.getId(), taskCode);
            // 创建监控和工作流的关系
            dataQualityMonitorMapper.createMonitorAndProcessEdge(dataQualityMonitor.getId(), processCode);
            // 添加定时id
            dataQualityMonitor.setScheduleId(scheduleId);
            dataQualityMonitorMapper.updateById(dataQualityMonitor);
        }
        return dataQualityMonitor;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataQualityMonitor dataQualityMonitor) {
        DataQualityMonitor checkDataQuality = dataQualityMonitorMapper.selectById(dataQualityMonitor.getId());
        if (checkDataQuality == null) {
            throw new ServiceException(Status.RULE_MONITORING_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataQualityMonitor.getCode()) && dataQualityMonitorMapper.checkCodeIsExists(dataQualityMonitor.getId(), dataQualityMonitor.getCode()) != null) {
            throw new ServiceException(Status.RULE_MONITORING_ALREADY_EXISTS);
        }
        // 不能拖拽到节点里面
        if (dataQualityMonitor.getPid() != null && dataQualityMonitor.getPid() != -1L && dataQualityMonitorMenuMapper.selectById(dataQualityMonitor.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_RULE_MONITORING_MENU);
        }

        int update = dataQualityMonitorMapper.updateById(dataQualityMonitor);
        if (update > 0) {
            if (dataQualityMonitor.getPid() != null) {
                // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
                dataQualityMonitorMapper.deleteQualityMonitorAndMenuEdge(Arrays.asList(dataQualityMonitor.getId()), checkDataQuality.getPid());
                if (dataQualityMonitor.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataQualityMonitorMapper.createQualityMonitorAndMenuEdge(dataQualityMonitor.getPid(), Arrays.asList(dataQualityMonitor.getId()));
                }
            }
        }
        return update;
    }

    @Override
    public int copy(Long pid, List<DataQualityMonitorCopyVo> dataQualityMonitorCopyVoList) {
        // 检查菜单
        DataQualityMonitorMenu dataQualityMonitorMenu = dataQualityMonitorMenuMapper.selectById(pid);
        if (dataQualityMonitorMenu == null) {
            throw new ServiceException(Status.THE_DATA_RULE_MONITORING_MENU_DOES_NOT_EXIST);
        }

        if (CollectionUtils.isEmpty(dataQualityMonitorCopyVoList)) {
            throw new ServiceException(Status.THE_COPIED_RULE_MONITORING_INFORMATION_CANNOT_BE_EMPTY);
        }

        for (DataQualityMonitorCopyVo dataQualityMonitorCopyVo : dataQualityMonitorCopyVoList) {
            // 检查实体
            DataQualityMonitor dataQualityMonitor = checkQualityMonitor(dataQualityMonitorCopyVo.getCodeNodeId());
            // 设置修改后的pid
            dataQualityMonitor.setPid(pid);

            // 修改基本信息
            dataQualityMonitor.setCode(dataQualityMonitorCopyVo.getCode());
            dataQualityMonitor.setName(dataQualityMonitorCopyVo.getName());
            dataQualityMonitor.setDescription(dataQualityMonitorCopyVo.getDescription());
            // 创建节点
            DataQualityMonitor copyDataQuality = this.create(dataQualityMonitor);

            // 查询绑定的表
            MonitorTableOrVIewVo monitorTableOrVIewVo = this.selectMonitorTableOrViewList(dataQualityMonitorCopyVo.getCodeNodeId());
            // 绑定表
            if (monitorTableOrVIewVo != null) {
                this.bindMonitorTableOrView(copyDataQuality.getId(), monitorTableOrVIewVo);
            }
        }
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        // 获取projectCode
        R queryProjectByNameR = remoteProjectService.queryProjectByName(SecurityUtils.getTenantId().toString());
        if (queryProjectByNameR.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(queryProjectByNameR.getMsg());
        }
        Map projectMap = (Map) queryProjectByNameR.getData();
        Long projectCode = MapUtils.getLong(projectMap, "code");

        for (Long id : ids) {
            // 查询规则监控信息
            DataQualityMonitor dataQualityMonitor = dataQualityMonitorMapper.selectById(id);
            if (dataQualityMonitor != null) {
                R deleteRuleTaskAndProcessR = remoteTaskAndProcessService.deleteRuleTaskAndProcess(projectCode, dataQualityMonitor.getTaskCode(),
                        dataQualityMonitor.getProcessCode(), dataQualityMonitor.getScheduleId());
                if (deleteRuleTaskAndProcessR.getCode() != Status.SUCCESS.getCode()) {
                    throw new ServiceException(deleteRuleTaskAndProcessR.getMsg());
                }
            }
        }
        return dataQualityMonitorMapper.deleteBatchIds(ids);
    }

    @Override
    public DataQualityMonitor selectById(Long id) {
        // 校验规则监控
        DataQualityMonitor dataQualityMonitor = checkQualityMonitor(id);
        return dataQualityMonitor;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int bindMonitorTableOrView(Long id, MonitorTableOrVIewVo monitorTableOrVIewVo) {
        // 校验规则监控
        checkQualityMonitor(id);

        int insert = 0;
        List<String> tableIdList = monitorTableOrVIewVo.getTableIdList();
        if (CollectionUtils.isNotEmpty(tableIdList)) {
            insert += dataQualityMonitorMapper.bindMonitorTable(id, SqlUtils.getInSql(tableIdList));
        }
        List<String> viewIdList = monitorTableOrVIewVo.getViewIdList();
        if (CollectionUtils.isNotEmpty(viewIdList)) {
            insert += dataQualityMonitorMapper.bindMonitorView(id, SqlUtils.getInSql(viewIdList));
        }

        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int unbindMonitorTableOrView(Long id, MonitorTableOrVIewVo monitorTableOrVIewVo) {
        // 校验规则监控
        checkQualityMonitor(id);

        int delete = 0;
        List<String> tableIdList = monitorTableOrVIewVo.getTableIdList();
        if (CollectionUtils.isNotEmpty(tableIdList)) {
            delete += dataQualityMonitorMapper.unbindMonitorTable(id, SqlUtils.getInSql(tableIdList));
        }
        List<String> viewIdList = monitorTableOrVIewVo.getViewIdList();
        if (CollectionUtils.isNotEmpty(viewIdList)) {
            delete += dataQualityMonitorMapper.unbindMonitorView(id, SqlUtils.getInSql(viewIdList));
        }
        return delete;
    }

    @Override
    public MonitorTableOrVIewVo selectMonitorTableOrViewList(Long id) {
        // 校验规则监控
        checkQualityMonitor(id);

        List<String> tableIdList = dataQualityMonitorMapper.selectMonitorTableList(id);

        List<String> viewIdList = dataQualityMonitorMapper.selectMonitorViewList(id);

        return new MonitorTableOrVIewVo(tableIdList, viewIdList);
    }
}
