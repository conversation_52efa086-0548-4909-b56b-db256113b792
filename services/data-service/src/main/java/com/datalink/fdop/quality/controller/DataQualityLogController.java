package com.datalink.fdop.quality.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.quality.api.domain.DataQualityLog;
import com.datalink.fdop.quality.service.IDataQualityLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11 14:22
 */
@RequestMapping("/quality/log")
@RestController
@Api(tags = "规则日志api")
public class DataQualityLogController extends BaseController {

    @Autowired
    private IDataQualityLogService dataQualityLogService;

    @ApiOperation("创建规则日志")
    @Log(title = "数据质量", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R<DataQualityLog> create(@RequestBody DataQualityLog dataQualityLog) {
        return R.ok(dataQualityLogService.create(dataQualityLog));
    }

    @ApiOperation("修改规则日志")
    @Log(title = "数据质量", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataQualityLog dataQualityLog) {
        return R.toResult(dataQualityLogService.update(dataQualityLog));
    }

    @ApiOperation("删除规则日志")
    @Log(title = "数据质量", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        return R.toResult(dataQualityLogService.delete(ids));
    }

    @ApiOperation(value = "查询规则日志列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功", response = PageDataInfo.class)
    })
    @PostMapping(value = "/list")
    @Log(title = "数据质量")
    public R<PageDataInfo<DataQualityLog>> list(@RequestBody(required = false) DataQualityLog dataQualityLog) {
        return R.ok(dataQualityLogService.list(dataQualityLog));
    }

}
