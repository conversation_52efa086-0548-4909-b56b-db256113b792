package com.datalink.fdop.quality.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.quality.api.domain.DataQualityMonitor;
import com.datalink.fdop.quality.api.model.vo.DataQualityMonitorCopyVo;
import com.datalink.fdop.quality.api.model.vo.MonitorTableOrVIewVo;
import com.datalink.fdop.quality.service.IDataQualityMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11 14:22
 */
@RequestMapping(value = "/quality/monitor")
@RestController
@Api(tags = "规则监控api")
public class DataQualityMonitorController extends BaseController {

    @Autowired
    private IDataQualityMonitorService dataQualityMonitorService;

    @ApiOperation("查询规则监控的序号")
    @Log(title = "数据质量")
    @GetMapping(value = "/querySerialNumber")
    public R<Integer> querySerialNumber() {
        return R.ok(dataQualityMonitorService.querySerialNumber());
    }

    @ApiOperation("创建规则监控")
    @Log(title = "数据质量", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R<DataQualityMonitor> create(@Validated @RequestBody DataQualityMonitor dataQualityMonitor) {
        if (dataQualityMonitor.getPid() == null) {
            dataQualityMonitor.setPid(-1L);
        }
        return R.ok(dataQualityMonitorService.create(dataQualityMonitor));
    }

    @ApiOperation("修改规则监控")
    @Log(title = "数据质量", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataQualityMonitor dataQualityMonitor) {
        if (dataQualityMonitor.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(dataQualityMonitorService.update(dataQualityMonitor));
    }

    @ApiOperation("复制规则监控")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pid", value = "实体菜单id", required = true, dataType = "Long", paramType = "query", example = "1"),
    })
    @Log(title = "数据质量", businessType = BusinessType.INSERT)
    @PostMapping(value = "/copy/{pid}")
    public R copy(@PathVariable(value = "pid") Long pid, @Validated @RequestBody List<DataQualityMonitorCopyVo> dataQualityMonitorCopyVoList) {
        dataQualityMonitorService.copy(pid, dataQualityMonitorCopyVoList);
        return R.ok();
    }

    @ApiOperation("删除规则监控")
    @Log(title = "数据质量", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.LEASE_SPECIFY_THE_QUALITY_TO_DELETE);
        }
        return R.toResult(dataQualityMonitorService.delete(ids));
    }

    @ApiOperation("根据id查询规则监控信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则监控id", required = true, dataType = "Long", paramType = "path"),
    })
    @Log(title = "数据质量")
    @GetMapping(value = "/selectById/{id}")
    public R<DataQualityMonitor> selectById(@PathVariable("id") Long id) {
        return R.ok(dataQualityMonitorService.selectById(id));
    }

    @ApiOperation("绑定监控和表/视图的关系")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则监控id", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "tableIdList", value = "表id集合", required = true, dataType = "Long", paramType = "body"),
    })
    @Log(title = "数据质量")
    @PostMapping(value = "/bindMonitorTableOrView/{id}")
    public R bindMonitorTableOrView(@PathVariable(value = "id") Long id, @RequestBody MonitorTableOrVIewVo monitorTableOrVIewVo) {
        return R.toResult(dataQualityMonitorService.bindMonitorTableOrView(id, monitorTableOrVIewVo));
    }

    @ApiOperation("解绑监控和表/视图的关系")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则监控id", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "tableIdList", value = "表id集合", required = true, dataType = "Long", paramType = "body"),
    })
    @Log(title = "数据质量")
    @DeleteMapping(value = "/unbindMonitorTableOrView/{id}")
    public R unbindMonitorTableOrView(@PathVariable(value = "id") Long id, @RequestBody MonitorTableOrVIewVo monitorTableOrVIewVo) {
        return R.toResult(dataQualityMonitorService.unbindMonitorTableOrView(id, monitorTableOrVIewVo));
    }

    @ApiOperation("查询监控绑定的表/视图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则监控id", required = true, dataType = "Long", paramType = "path"),
    })
    @Log(title = "数据质量")
    @GetMapping(value = "/selectMonitorTableOrViewList/{id}")
    public R<MonitorTableOrVIewVo> selectMonitorTableOrViewList(@PathVariable(value = "id") Long id) {
        return R.ok(dataQualityMonitorService.selectMonitorTableOrViewList(id));
    }

}
