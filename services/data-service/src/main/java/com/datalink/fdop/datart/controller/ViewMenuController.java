package com.datalink.fdop.datart.controller;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.datart.domain.View;
import com.datalink.fdop.datart.domain.ViewMenu;
import com.datalink.fdop.datart.domain.ViewTree;
import com.datalink.fdop.datart.service.ViewMenuService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/datart/views/menu")
public class ViewMenuController {


    @Autowired
    private ViewMenuService viewMenuService;

    @ApiOperation("创建菜单")
    @Log(title = "自助分析", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 3000)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody ViewMenu viewMenu) {
        if (viewMenu.getPid() == null) {
            viewMenu.setPid(-1L);
        }
        return R.toResult(viewMenuService.create(viewMenu));
    }

    @ApiOperation("修改菜单")
    @Log(title = "自助分析", businessType = BusinessType.UPDATE)
    @RepeatSubmit(interval = 3000)
    @PostMapping(value = "/update")
    public R update(@RequestBody ViewMenu viewMenu) {
        if (viewMenu.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }



        return R.toResult(viewMenuService.update(viewMenu));
    }

    @ApiOperation("删除菜单")
    @Log(title = "自助分析", businessType = BusinessType.DELETE)
    @RepeatSubmit(interval = 3000)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.SPECIFY_THE_VIEW_MENU_TO_BE_DELETED);
        }
        return R.toResult(viewMenuService.delete(ids));
    }

    @ApiOperation("展示数据视图菜单树结构")
    @Log(title = "自助分析")
    @GetMapping(value = "/tree")
    public R<List<ViewTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode) {
        List<ViewTree> list = viewMenuService.tree(sort, code, isQueryNode);
        return R.ok(list);
    }


    @ApiOperation("数据视图总览")
    @Log(title = "自助分析")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<View>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                          @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                          @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(viewMenuService.overview(pid, sort, searchVo));
    }
}
