package com.datalink.fdop.quality.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.quality.api.domain.DataQualityMonitor;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorMenu;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorTree;
import com.datalink.fdop.quality.mapper.DataQualityMonitorMapper;
import com.datalink.fdop.quality.mapper.DataQualityMonitorMenuMapper;
import com.datalink.fdop.quality.service.IDataQualityMonitorMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
public class DataQualityMonitorMenuService implements IDataQualityMonitorMenuService {

    @Autowired
    private DataQualityMonitorMapper dataQualityMonitorMapper;

    @Autowired
    private DataQualityMonitorMenuMapper dataQualityMonitorMenuMapper;


    @Override
    public Integer querySerialNumber() {
        return dataQualityMonitorMenuMapper.querySerialNumber();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataQualityMonitorMenu dataQualityMonitorMenu) {
        if (dataQualityMonitorMenuMapper.selectByCode(dataQualityMonitorMenu.getCode()) != null) {
            throw new ServiceException(Status.THE_DATA_RULE_MONITORING_MENU_ALREADY_EXISTS);
        }
        dataQualityMonitorMenu.setId(IdWorker.getId());
        if (dataQualityMonitorMenu.getSerialNumber() == null) {
            dataQualityMonitorMenu.setSerialNumber(this.querySerialNumber());
        }
        int insert = dataQualityMonitorMenuMapper.insertQualityMonitorMenu(dataQualityMonitorMenu);
        // 创建菜单边关系
        if (insert > 0 && dataQualityMonitorMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            dataQualityMonitorMenuMapper.createQualityMonitorMenuEdge(dataQualityMonitorMenu.getPid(), Arrays.asList(dataQualityMonitorMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataQualityMonitorMenu dataQualityMonitorMenu) {
        DataQualityMonitorMenu checkDataQualityMenu = dataQualityMonitorMenuMapper.selectById(dataQualityMonitorMenu.getId());
        if (checkDataQualityMenu == null) {
            throw new ServiceException(Status.THE_DATA_RULE_MONITORING_MENU_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataQualityMonitorMenu.getCode()) && dataQualityMonitorMenuMapper.checkCodeIsExists(dataQualityMonitorMenu.getId(), dataQualityMonitorMenu.getCode()) != null) {
            throw new ServiceException(Status.THE_DATA_RULE_MONITORING_MENU_ALREADY_EXISTS);
        }
        int update = dataQualityMonitorMenuMapper.updateById(dataQualityMonitorMenu);
        if (update > 0 && dataQualityMonitorMenu.getPid() != null) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            dataQualityMonitorMenuMapper.deleteQualityMonitorMenuEdge(Arrays.asList(dataQualityMonitorMenu.getId()), checkDataQualityMenu.getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (dataQualityMonitorMenu.getPid() != -1L) {
                dataQualityMonitorMenuMapper.createQualityMonitorMenuEdge(dataQualityMonitorMenu.getPid(), Arrays.asList(dataQualityMonitorMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            DataQualityMonitorMenu dataQualityMonitorMenu = dataQualityMonitorMenuMapper.selectById(id);
            if (dataQualityMonitorMenu == null) {
                continue;
            }
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = dataQualityMonitorMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = dataQualityMonitorMenuMapper.bacthUpdatePidById(menuIdList, dataQualityMonitorMenu.getPid());
                if (update > 0 && dataQualityMonitorMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataQualityMonitorMenuMapper.createQualityMonitorMenuEdge(dataQualityMonitorMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = dataQualityMonitorMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int update = dataQualityMonitorMapper.bacthUpdatePidById(elementIdList, dataQualityMonitorMenu.getPid());
                if (update > 0 && dataQualityMonitorMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataQualityMonitorMapper.createQualityMonitorAndMenuEdge(dataQualityMonitorMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return dataQualityMonitorMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<DataQualityMonitorTree> tree(String sort, String code, Boolean isQueryNode) {

        // 所有的数据集合
        List<DataQualityMonitorTree> trees = new ArrayList<>();
        // 添加数据元素树
        if (isQueryNode) {
            trees.addAll(dataQualityMonitorMapper.selectQualityMonitorTree(sort, code));
        }
        // 添加数据元素菜单树
        trees.addAll(dataQualityMonitorMenuMapper.selectMenuTree(sort, null));

        // 递归成树结构
        List<DataQualityMonitorTree> treeList = (List<DataQualityMonitorTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public PageDataInfo<DataQualityMonitor> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<DataQualityMonitor> page = PageUtils.getPage(DataQualityMonitor.class);
        IPage<DataQualityMonitor> dataEntityIPage = dataQualityMonitorMapper.overview(page, pid, sort, searchVo);

        return PageUtils.getPageInfo(dataEntityIPage.getRecords(), (int) dataEntityIPage.getTotal());
    }

}
