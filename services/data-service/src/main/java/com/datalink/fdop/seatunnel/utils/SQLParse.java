package com.datalink.fdop.seatunnel.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.util.TablesNamesFinder;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class SQLParse {

//    public static void main(String[] args) throws Exception {
//        String filePath = "D:\\test.sql"; // 替换为你本地的 SQL 文件路径
//        String sqlContent = readSqlFromFile(filePath);
//
//        Statement statement = CCJSqlParserUtil.parse(sqlContent);
//        Set<String> tables = extractTables(statement);
//        for (String table : tables) {
//            if (table.contains(".")) {
//                System.out.println(table);
//            }
//        }
//    }


    public static void main(String[] args) throws JSQLParserException {
        List<SeaTunnelField> seaTunnelFields = extractColumns("select cs.kostl as \"cost_center_id\",cs.kokrs as \"control_area_id\",to_date(cs.datbi,'yyyymmdd') as \"date_to\",to_date(datab,'yyyymmdd') as \"date_from\",kosar as \"cost_center_type\",func_area as \"expense_type\",waers as \"currency\",bukrs as \"company_id\",werks as \"plant_id\",khinr as \"cost_center_group_id\",ktext as \"cost_center_desc\",ltext as \"cost_center_text\",' ' as \"enable\",' ' as \"create_by\",' ' as \"create_time\",' ' as \"update_by\",' ' as \"update_time\" from saphanadb.csks cs left join saphanadb.cskt ct on cs.mandt = ct.mandt and cs.kostl = ct.kostl and cs.kokrs = ct.kokrs and cs.datbi = ct.datbi");
        System.out.println(seaTunnelFields);
    }

    // 从文件读取 SQL 内容
    private static String readSqlFromFile(String filePath) throws IOException {
        return new String(Files.readAllBytes(Paths.get(filePath)));
    }

    // 提取所有表名（包含 WITH 中的子查询）
    public static Set<String> extractTables(Statement statement) {
        Set<String> tableNames = new HashSet<>();
        TablesNamesFinder finder = new TablesNamesFinder();

        // 提取主查询的表
        tableNames.addAll(finder.getTableList(statement));

        // 如果包含 WITH 子句，提取其子查询中的表
        if (statement instanceof Select) {
            Select select = (Select) statement;
            List<WithItem> withItems = select.getWithItemsList();
            if (withItems != null) {
                for (WithItem withItem : withItems) {
                    if (withItem.getSubSelect() != null) {
                        SelectBody withSelectBody = withItem.getSubSelect().getSelectBody();
                        SubSelect sub = new SubSelect();
                        sub.setSelectBody(withSelectBody);
                        tableNames.addAll(finder.getTableList(sub));
                    }
                }
            }
        }
        return tableNames;
    }

    /**
     * 提取所有列信息
     * @param querySql
     * @return
     * @throws JSQLParserException
     */
    public static List<SeaTunnelField> extractColumns(String querySql) throws JSQLParserException {
        Statement statement = CCJSqlParserUtil.parse(querySql);
        List<SeaTunnelField> seaTunnelFields = new ArrayList<>();
        if (statement instanceof Select) {
            Select select = (Select) statement;
            SelectBody selectBody = select.getSelectBody();
            if (selectBody instanceof PlainSelect) {
                PlainSelect plainSelect = (PlainSelect) selectBody;
                List<SelectItem> selectItems = plainSelect.getSelectItems();
                for (SelectItem selectItem : selectItems) {
                    if (selectItem instanceof net.sf.jsqlparser.statement.select.AllColumns) {
                        // 处理 * 的情况
                        seaTunnelFields.add(new SeaTunnelField(IdWorker.getId(), "*", "ALL", 0L, 0L, false));
                    } else if (selectItem instanceof net.sf.jsqlparser.statement.select.AllTableColumns) {
                        // 处理 table.* 的情况
                        net.sf.jsqlparser.statement.select.AllTableColumns allTableColumns = (net.sf.jsqlparser.statement.select.AllTableColumns) selectItem;
                        seaTunnelFields.add(new SeaTunnelField(IdWorker.getId(), allTableColumns.getTable().getName() + ".*", "ALL", 0L, 0L, false));
                    } else if (selectItem instanceof net.sf.jsqlparser.statement.select.SelectExpressionItem) {
                        net.sf.jsqlparser.statement.select.SelectExpressionItem selectExpressionItem = (net.sf.jsqlparser.statement.select.SelectExpressionItem) selectItem;
                        Expression expression = selectExpressionItem.getExpression();
                        String columnName = selectExpressionItem.getAlias() != null ? selectExpressionItem.getAlias().getName() : null;
                        if (expression instanceof Column) {
                            Column column = (Column) expression;
                            if (columnName == null) {
                                columnName = column.getColumnName();
                            }
                            // 去除列名中的转义符
                            columnName = columnName.replace("\"", "").replace("`", "").replace("'", "");
                            seaTunnelFields.add(new SeaTunnelField(IdWorker.getId(), columnName, "STRING", 0L, 0L, false));
                        } else if (expression instanceof net.sf.jsqlparser.expression.StringValue) {
                            // 处理伪列的情况
                            if (columnName != null) {
                                // 去除列名中的转义符
                                columnName = columnName.replace("\"", "").replace("`", "").replace("'", "");
                                seaTunnelFields.add(new SeaTunnelField(IdWorker.getId(), columnName, "STRING", 0L, 0L, false));
                            }
                        } else {
                            // 处理函数表达式的情况
                            if (columnName != null) {
                                // 去除列名中的转义符
                                columnName = columnName.replace("\"", "").replace("`", "").replace("'", "");
                                seaTunnelFields.add(new SeaTunnelField(IdWorker.getId(), columnName, "STRING", 0L, 0L, false));
                            }
                        }
                    }
                }
            }
        }
        return seaTunnelFields;
    }




}