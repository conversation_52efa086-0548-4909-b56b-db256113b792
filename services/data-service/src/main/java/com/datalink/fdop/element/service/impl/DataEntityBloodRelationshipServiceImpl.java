package com.datalink.fdop.element.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.datalink.fdop.element.api.domain.*;
import com.datalink.fdop.element.api.enums.EntityType;
import com.datalink.fdop.element.mapper.DataEntityMapper;
import com.datalink.fdop.element.mapper.DataEntityMenuMapper;
import com.datalink.fdop.element.service.IDataEntityBloodRelationshipService;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DataEntityBloodRelationshipServiceImpl implements IDataEntityBloodRelationshipService {

    @Autowired
    private DataEntityMapper dataEntityMapper;

    @Autowired
    private DataEntityMenuMapper dataEntityMenuMapper;

    @Autowired
    private IDataEntityStructureService dataEntityStructureService;

    @Override
    public DataEntityBloodVo getBloodByTagId(Long tagId) {
        DataEntityBloodVo dataEntityBloodVo = new DataEntityBloodVo();
        // 实体血缘关系
        List<DataEntityBloodRelationship> dataEntityBloodRelationships = new ArrayList<>();
        List<DataEntityBlood> dataEntityBloodList = new ArrayList<>();
        List<DataEntity> dataEntities = dataEntityMapper.selectEntityListByTagId(tagId);
        if (CollectionUtils.isNotEmpty(dataEntities)) {
            dataEntities.forEach(dataEntity -> {
                // 初始化ADS目标节点
                getNextDataEntityBlood(dataEntity.getId(), dataEntityBloodList, dataEntityBloodRelationships, dataEntity.getEntityType());
            });
            dataEntityBloodVo.setDataEntityBloodList(dataEntityBloodList);
            dataEntityBloodVo.setDataEntityBloodRelationshipList(dataEntityBloodRelationships);
            return dataEntityBloodVo;
        }
        return dataEntityBloodVo;
    }

    @Override
    public DataEntityBloodVo getBloodByL3AssetId(Long assetId) {
        DataEntityBloodVo dataEntityBloodVo = new DataEntityBloodVo();
        // 实体血缘关系
        List<DataEntityBloodRelationship> dataEntityBloodRelationships = new ArrayList<>();
        List<DataEntityBlood> dataEntityBloodList = new ArrayList<>();
        List<DataEntityMenu> dataEntityMenus = dataEntityMenuMapper.selectEntityListByL3Id(assetId);
        if (CollectionUtils.isNotEmpty(dataEntityMenus)) {
            dataEntityMenus.forEach(entityMenu -> {
                // 初始化ADS目标节点
                getNextDataEntityBlood(entityMenu.getId(), dataEntityBloodList, dataEntityBloodRelationships, entityMenu.getEntityType());
            });
            dataEntityBloodVo.setDataEntityBloodList(dataEntityBloodList);
            dataEntityBloodVo.setDataEntityBloodRelationshipList(dataEntityBloodRelationships);
            return dataEntityBloodVo;
        }
        return dataEntityBloodVo;
    }


    private void getNextDataEntityBlood(Long dataEntityId, List<DataEntityBlood> dataEntityBloods, List<DataEntityBloodRelationship> dataEntityBloodRelationships, EntityType entityType) {
        List<DataEntity> dataEntities = new ArrayList<>();
        // 初始出发节点
        DataEntity initNodeEntity = dataEntityMapper.selectById(dataEntityId);
        dataEntityBloods.add(generateDataEntityBloodByEntity(initNodeEntity));
        dataEntities.add(initNodeEntity);
        // 遍历上层血缘
        while (true) {
            List<DataEntityRelation> dataEntityRelations = dataEntityStructureService.queryEntityRelationBySource(dataEntities, entityType);
            List<DataEntityBloodEachVo> dataEntityBloodEachVoList = appendSourceBloodRelationship(dataEntityRelations, dataEntityBloods, dataEntityBloodRelationships);
            if (CollectionUtils.isEmpty(dataEntityBloodEachVoList)) {
                break;
            }
            dataEntities = new ArrayList<>();
            for (DataEntityBloodEachVo blood : dataEntityBloodEachVoList) {
                if (blood != null && blood.getId() != null) {
                    dataEntities.add(dataEntityMapper.selectById(blood.getId()));
                }
            }
        }
        dataEntities = new ArrayList<>();
        // 从初始节点出发寻找下层血缘
        dataEntities.add(initNodeEntity);
        entityType = initNodeEntity.getEntityType();
        while (true) {
            List<DataEntityRelation> dataEntityRelations = dataEntityStructureService.queryEntityRelationByTarget(dataEntities, entityType);
            List<DataEntityBloodEachVo> dataEntityBloodEachVoList = appendTargetBloodRelationship(dataEntityRelations, dataEntityBloods, dataEntityBloodRelationships);
            if (CollectionUtils.isEmpty(dataEntityBloodEachVoList)) {
                break;
            }
            dataEntities = new ArrayList<>();
            for (DataEntityBloodEachVo blood : dataEntityBloodEachVoList) {
                if (blood != null && blood.getId() != null) {
                    dataEntities.add(dataEntityMapper.selectById(blood.getId()));
                }
            }
        }
    }

    private List<DataEntityBloodEachVo> appendSourceBloodRelationship(List<DataEntityRelation> dataEntityRelations, List<DataEntityBlood> dataEntityBloods, List<DataEntityBloodRelationship> dataEntityBloodRelationships) {
        List<DataEntityBloodEachVo> dataEntityBloodEachVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataEntityRelations)) {
            // 根据主键group by 筛选出唯一实体信息
            // 过滤掉 getTargetDataEntityId() 为 null 的元素
            List<DataEntityRelation> filteredDataEntityRelations = dataEntityRelations.stream()
                    .filter(relation -> relation.getTargetDataEntityId() != null)
                    .collect(Collectors.toList());
            List<DataEntityRelation> dataEntityList = filteredDataEntityRelations.stream()
                    .collect(Collectors.groupingBy(DataEntityRelation::getTargetDataEntityId))
                    .values().stream()
                    .map(item -> item.get(0))
                    .collect(Collectors.toList());
            for (DataEntityRelation dataEntityRelation : dataEntityList) {
                if (dataEntityRelation.getTargetDataEntityId() == null) {
                    continue;
                }
                DataEntityBlood dataEntityBlood = new DataEntityBlood();
                dataEntityBlood.setId(dataEntityRelation.getTargetDataEntityId());
                dataEntityBlood.setCode(dataEntityRelation.getTargetDataEntityCode());
                dataEntityBlood.setName(dataEntityRelation.getTargetDataEntityName());
                dataEntityBlood.setEntityType(dataEntityRelation.getTargetDataEntityType());
                dataEntityBlood.setDataEntityStructures(dataEntityStructureService.selectStructureById(dataEntityRelation.getTargetDataEntityId(), true));
                if (dataEntityRelation.getTargetDataEntityType() == EntityType.DWD || dataEntityRelation.getTargetDataEntityType() == EntityType.DIM) {
                    // 查询DWD实体信息
                    DataEntity subEntity = dataEntityMapper.selectById(dataEntityRelation.getTargetDataEntityId());
                    if (subEntity != null && subEntity.getPid() != null) {
                        DataEntityMenu entity = dataEntityMenuMapper.selectById(subEntity.getPid());
                        dataEntityBlood.setTableId(entity.getId());
                        dataEntityBlood.setTableCode(entity.getCode());
                        dataEntityBlood.setTableName(entity.getName());
                    }
                }
                dataEntityBloods.add(dataEntityBlood);
                // 获取上一层需要遍历的血缘实体节点
                dataEntityBloodEachVoList.add(new DataEntityBloodEachVo(dataEntityRelation.getTargetDataEntityId(), dataEntityRelation.getTargetDataEntityType()));
            }
            dataEntityRelations.forEach(dataEntityRelation -> {
                DataEntityBloodRelationship dataEntityBloodRelationship = new DataEntityBloodRelationship();
                dataEntityBloodRelationship.setSourceId(dataEntityRelation.getSourceDataEntityId());
                dataEntityBloodRelationship.setSourceStructureId(dataEntityRelation.getSourceDataEntityStructureId());
                dataEntityBloodRelationship.setTargetId(dataEntityRelation.getTargetDataEntityId());
                dataEntityBloodRelationship.setTargetStructureId(dataEntityRelation.getTargetDataEntityStructureId());
                dataEntityBloodRelationships.add(dataEntityBloodRelationship);
            });
        }
        return dataEntityBloodEachVoList;
    }

    /**
     * 拼接所有目标实体血缘信息
     *
     * @param dataEntityRelations
     * @param dataEntityBloods
     * @param dataEntityBloodRelationships
     * @return
     */
    private List<DataEntityBloodEachVo> appendTargetBloodRelationship(List<DataEntityRelation> dataEntityRelations, List<DataEntityBlood> dataEntityBloods, List<DataEntityBloodRelationship> dataEntityBloodRelationships) {
        List<DataEntityBloodEachVo> dataEntityBloodEachVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataEntityRelations)) {
            // 根据主键group by 筛选出唯一实体信息
            // 过滤掉 getSourceDataEntityId() 为 null 的元素
            List<DataEntityRelation> filteredDataEntityRelations = dataEntityRelations.stream()
                    .filter(relation -> relation.getSourceDataEntityId() != null)
                    .collect(Collectors.toList());
            List<DataEntityRelation> dataEntityList = filteredDataEntityRelations.stream()
                    .collect(Collectors.groupingBy(DataEntityRelation::getSourceDataEntityId))
                    .values().stream()
                    .map(item -> item.get(0))
                    .collect(Collectors.toList());
            for (DataEntityRelation dataEntityRelation : dataEntityList) {
                if (dataEntityRelation.getSourceDataEntityId() == null) {
                    continue;
                }
                DataEntityBlood dataEntityBlood = new DataEntityBlood();
                dataEntityBlood.setId(dataEntityRelation.getSourceDataEntityId());
                dataEntityBlood.setCode(dataEntityRelation.getSourceDataEntityCode());
                dataEntityBlood.setName(dataEntityRelation.getSourceDataEntityName());
                dataEntityBlood.setEntityType(dataEntityRelation.getSourceDataEntityType());
                dataEntityBlood.setDataEntityStructures(dataEntityStructureService.selectStructureById(dataEntityRelation.getSourceDataEntityId(), true));
                if (dataEntityRelation.getSourceDataEntityType() == EntityType.DWD || dataEntityRelation.getSourceDataEntityType() == EntityType.DIM) {
                    // 查询DWD实体信息
                    DataEntity subEntity = dataEntityMapper.selectById(dataEntityRelation.getSourceDataEntityId());
                    if (subEntity != null && subEntity.getPid() != null) {
                        DataEntityMenu entity = dataEntityMenuMapper.selectById(subEntity.getPid());
                        dataEntityBlood.setTableId(entity.getId());
                        dataEntityBlood.setTableCode(entity.getCode());
                        dataEntityBlood.setTableName(entity.getName());
                    }
                }
                dataEntityBloods.add(dataEntityBlood);
                // 获取下一层需要遍历的血缘实体节点
                dataEntityBloodEachVoList.add(new DataEntityBloodEachVo(dataEntityRelation.getSourceDataEntityId(), dataEntityRelation.getSourceDataEntityType()));
            }
            dataEntityRelations.forEach(dataEntityRelation -> {
                DataEntityBloodRelationship dataEntityBloodRelationship = new DataEntityBloodRelationship();
                dataEntityBloodRelationship.setSourceId(dataEntityRelation.getSourceDataEntityId());
                dataEntityBloodRelationship.setSourceStructureId(dataEntityRelation.getSourceDataEntityStructureId());
                dataEntityBloodRelationship.setTargetId(dataEntityRelation.getTargetDataEntityId());
                dataEntityBloodRelationship.setTargetStructureId(dataEntityRelation.getTargetDataEntityStructureId());
                dataEntityBloodRelationships.add(dataEntityBloodRelationship);
            });
        }
        return dataEntityBloodEachVoList;
    }

    private DataEntityBlood generateDataEntityBloodByEntity(DataEntity entity) {
        DataEntityBlood dataEntityBlood = new DataEntityBlood();
        dataEntityBlood.setId(entity.getId());
        dataEntityBlood.setCode(entity.getCode());
        dataEntityBlood.setName(entity.getName());
        dataEntityBlood.setEntityType(entity.getEntityType());
        dataEntityBlood.setDataEntityStructures(dataEntityStructureService.selectStructureById(entity.getId(), true));
        if (entity.getEntityType() == EntityType.DWD || entity.getEntityType() == EntityType.DIM) {
            // 查询DWD实体信息
            DataEntity subEntity = dataEntityMapper.selectById(entity.getId());
            DataEntityMenu parentMenu = dataEntityMenuMapper.selectById(subEntity.getPid());
            dataEntityBlood.setTableId(parentMenu.getId());
            dataEntityBlood.setTableCode(parentMenu.getCode());
            dataEntityBlood.setTableName(parentMenu.getName());
        }
        return dataEntityBlood;
    }


}
