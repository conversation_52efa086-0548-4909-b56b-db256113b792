package com.datalink.fdop.quality.service;

import com.datalink.fdop.quality.api.domain.DataQualityMonitor;
import com.datalink.fdop.quality.api.model.vo.DataQualityMonitorCopyVo;
import com.datalink.fdop.quality.api.model.vo.MonitorTableOrVIewVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IDataQualityMonitorService {

    Integer querySerialNumber();

    DataQualityMonitor create(DataQualityMonitor dataQualityMonitor);

    int update(DataQualityMonitor dataQualityMonitor);

    int copy(Long pid, List<DataQualityMonitorCopyVo> dataQualityMonitorCopyVoList);

    int delete(List<Long> ids);

    DataQualityMonitor selectById(Long id);

    int bindMonitorTableOrView(Long id, MonitorTableOrVIewVo monitorTableOrVIewVo);

    int unbindMonitorTableOrView(Long id, MonitorTableOrVIewVo monitorTableOrVIewVo);

    MonitorTableOrVIewVo selectMonitorTableOrViewList(Long id);

}
