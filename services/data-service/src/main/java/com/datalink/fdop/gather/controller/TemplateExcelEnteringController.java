package com.datalink.fdop.gather.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.alert.api.utils.JSONUtils;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.gather.api.domain.Resource;
import com.datalink.fdop.gather.api.model.TemplateFieldConfigVo;
import com.datalink.fdop.gather.service.TemplateExcelEnteringService;
import com.datalink.fdop.gather.service.TemplateService;
import com.datalink.fdop.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2022/7/6 19:46
 */
@RestController
@RequestMapping("/gather/excel")
@Api(tags = "模板Excel录入API")
@Slf4j
public class TemplateExcelEnteringController {

    @Autowired
    private TemplateExcelEnteringService excelEnteringService;

    @Autowired
    private TemplateService templateService;

    private static final String realPath = "/gather/admin/resources";
    // private static final String realPath = "/Users/<USER>/Documents/tmp/gather";
    // private static final String realPath = "E:\\tmp\\test";

    @ApiOperation("模板文件上传")
    @Log(title = "数据采集")
    @PostMapping("/upload")
    @ResponseBody
    @RepeatSubmit(interval = 1000)
    public R upload(MultipartFile file, @RequestParam Long templateId,
                    @RequestParam(required = false, defaultValue = "1000") int batchCount,
                    @RequestParam(required = false, defaultValue = "false") boolean useSkipError,
                    @RequestParam(value = "templateFieldConfigVo", required = false) String templateFieldConfigVo) throws IOException {
        // 获取用户id
        LoginUser loginAppUser = SecurityUtils.getLoginUser();
        if (loginAppUser == null) {
            throw new ServiceException("不合法用户");
        }
        Long userId = loginAppUser.getUserid();

        // 日志id
        Long logId = IdWorker.getId();

        //先将文件保存到本地，让后在本地读取文件
        String realPath = this.realPath;
        File newFile = new File(realPath);
        // 如果文件夹存在,则删除重建
        if (newFile.exists()) {
            newFile.delete();
        }
        newFile.mkdirs();
        // 上传
        File uploadFile = new File(newFile, file.getOriginalFilename());
        file.transferTo(uploadFile);
        //异步读取文件
        // excelEnteringService.readFile(templateId,batchCount,userid,excelEnteringService,"E:\\tmp\\test\\1\\salposinfo19.csv", IdWorker.getId());
        excelEnteringService.readFile(templateId, userId, logId, batchCount, uploadFile.getPath(), JSONObject.parseObject(templateFieldConfigVo, TemplateFieldConfigVo.class), useSkipError, null, null);
        return R.ok();
    }

    @ApiOperation("从指定路径读取文件")
    @Log(title = "数据采集")
    @PostMapping("/getFiles")
    @ResponseBody
    public R<List<Long>> getFiles(@RequestParam Long templateId,
                                  @RequestParam Long userId,
                                  @RequestParam(required = false, defaultValue = "1000") int batchCount,
                                  @RequestParam(required = false, defaultValue = "false") boolean useSkipError,
                                  @RequestBody TemplateFieldConfigVo templateFieldConfigVo) {
        List<Long> logIds = new ArrayList<>();
        List<Resource> resourceList = JSONUtils.toList(templateFieldConfigVo.getResourceList(), Resource.class);
        if (resourceList == null) {
            throw new ServiceException("资源文件列表为空");
        }
        // 生成日志id与资源文件集合顺序对应绑定
        for (int i = 0; i < resourceList.size(); i++) {
            logIds.add(IdWorker.getId());
        }
        excelEnteringService.getFiles(templateId, userId, batchCount, templateFieldConfigVo, useSkipError, resourceList, logIds);
        return R.ok(logIds);
    }

    @ApiOperation("从指定路径读取文件（根据租户id）")
    @Log(title = "数据采集")
    @PostMapping("/getFilesByTenantId/{tenantId}")
    @ResponseBody
    public R<List<Long>> getFilesByTenantId(@PathVariable(value = "tenantId") Long tenantId,
                                            @RequestParam Long templateId,
                                            @RequestParam Long userId,
                                            @RequestParam(required = false, defaultValue = "1000") int batchCount,
                                            @RequestParam(required = false, defaultValue = "false") boolean useSkipError,
                                            @RequestBody TemplateFieldConfigVo templateFieldConfigVo) {
        List<Long> logIds = new ArrayList<>();
        List<Resource> resourceList = JSONUtils.toList(templateFieldConfigVo.getResourceList(), Resource.class);
        if (resourceList == null) {
            throw new ServiceException("资源文件列表为空");
        }
        // 生成日志id与资源文件集合顺序对应绑定
        for (int i = 0; i < resourceList.size(); i++) {
            logIds.add(IdWorker.getId());
        }
        excelEnteringService.getFilesByTenantId(templateId, userId, batchCount, templateFieldConfigVo, useSkipError, resourceList, logIds, tenantId);
        return R.ok(logIds);
    }

    @ApiOperation("模板文件下载")
    @Log(title = "数据采集")
    @PostMapping("/download")
    @ResponseBody
    @RepeatSubmit(interval = 1000)
    public R download(@RequestParam Long templateId, HttpServletResponse response, @RequestParam Boolean type) throws IOException {
        LoginUser loginAppUser = SecurityUtils.getLoginUser();
        if (loginAppUser == null) {
            throw new ServiceException("不合法用户");
        }
        Long userid = loginAppUser.getUserid();
        excelEnteringService.getWriteData(templateId, userid, response, type);
        return R.ok();
    }

}
