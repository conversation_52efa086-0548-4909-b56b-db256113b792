package com.datalink.fdop.gather.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.datalink.fdop.auth.api.RemoteTakeEffectAuthService;
import com.datalink.fdop.auth.api.vo.CalculateValueVo;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datasource.dynamic.DataSourceHolder;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.element.api.RemoteElementService;
import com.datalink.fdop.element.api.RemoteEntityTableService;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.gather.api.domain.GatherLog;
import com.datalink.fdop.gather.api.domain.Template;
import com.datalink.fdop.gather.api.domain.TemplateField;
import com.datalink.fdop.gather.api.enums.*;
import com.datalink.fdop.gather.api.model.TemplateFieldConfig;
import com.datalink.fdop.gather.api.model.TemplateFieldConfigVo;
import com.datalink.fdop.gather.api.model.dto.TemplateDataDto;
import com.datalink.fdop.gather.api.utils.DataTypeChangeUtils;
import com.datalink.fdop.gather.factory.SqlDefinitionFactory;
import com.datalink.fdop.gather.listener.ModelDataListener;
import com.datalink.fdop.gather.listener.NoModelDataListener;
import com.datalink.fdop.gather.mapper.TemplateEnteringMapper;
import com.datalink.fdop.gather.mapper.TemplateExcelEnteringMapper;
import com.datalink.fdop.gather.mapper.TemplateFieldMapper;
import com.datalink.fdop.gather.mapper.TemplateMapper;
import com.datalink.fdop.gather.service.GatherLogService;
import com.datalink.fdop.gather.service.TemplateEnteringService;
import com.datalink.fdop.gather.service.TemplateExcelEnteringService;
import com.datalink.fdop.gather.service.TemplateFieldService;
import com.datalink.fdop.gather.utils.FieldCheckUtil;
import com.datalink.fdop.gather.utils.GatherDataHandle;
import com.datalink.fdop.gather.utils.SheetSearchUtils;
import com.datalink.fdop.gather.utils.TmpTableUtils;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import com.datalink.fdop.gather.api.domain.Resource;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/7/6 19:52
 */
@Service
@Slf4j
public class TemplateExcelEnteringServiceImpl implements TemplateExcelEnteringService {

    @Autowired
    private TemplateExcelEnteringMapper excelEnteringMapper;
    @Autowired
    private GatherLogService gatherLogService;
    @Autowired
    private TemplateMapper templateMapper;
    @Autowired
    private TemplateFieldMapper fieldMapper;
    @Autowired
    private TemplateEnteringService enteringService;

    @Autowired
    private TemplateFieldService fieldService;
    @Autowired
    private RemoteElementService elementService;
    @Autowired
    private TemplateEnteringMapper enteringMapper;
    @Autowired
    private RemoteEntityTableService remoteEntityTableService;
    @Autowired
    private RemoteDriveService remoteDriveService;
    @Autowired
    private RemoteJdbcService remoteJdbcService;
    @Autowired
    private RemoteTakeEffectAuthService takeEffectAuthService;
    @Autowired
    private FieldCheckUtil fieldCheckUtil;
    @Autowired
    private TemplateFieldService templateFieldService;

    @Autowired
    private GatherDataHandle gatherDataHandle;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void saveData(Long userId, Long templateId, Long logId, Map<Integer, String> headMap, List<Map<Integer, String>> cachedDataList) {

        try {
            //获取模板
            VlabelItem<Template> vlabelItem = templateMapper.selectById(templateId);
            if (vlabelItem == null) {
                throw new IllegalArgumentException();
            }
            Template template = vlabelItem.getProperties();

            //插入数据
            if (template.getTemplateType() == TemplateType.ENTITY) {
                saveEntityData(userId, template, headMap, cachedDataList);
            } else if (template.getTemplateType() == TemplateType.MAIN) {
                //获取元素
                R<DataElement> r = elementService.selectById(template.getElementId());
                if (r == null) {
                    throw new IllegalArgumentException("数据元素不存在");
                }
                DataElement element = r.getData();
                saveMainData(userId, template, headMap, cachedDataList, element);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }

    }

    private void saveEntityData(Long userId, Template template, Map<Integer, String> headMap, List<Map<Integer, String>> cachedDataList) {
        try {
            //获取关联表
            VlabelItem<DataEntityTable> vla = templateMapper.findEntityTable(template.getId());
            if (vla == null) {
                throw new ServiceException(" error:--未找到模板id为" + template.getId() + "未找到关联表");
            }
            DataEntityTable entityTable = vla.getProperties();
            //获取数据源
            R<DataSource> sourceR = remoteDriveService.queryDataSource(entityTable.getDataSourceId());
            DataSource dataSource = sourceR.getData();
            //字段映射关系
            R<List<DataEntityTableMapping>> listR = remoteEntityTableService.selectTableMapping(template.getEntityId(), entityTable.getId());
            if (listR.getCode() != 200 || listR.getData() == null) {
                throw new ServiceException(" error:--未找到模板id为" + template.getId() + "未获取关联表相关映射字段信息");
            }
            List<DataEntityTableMapping> mappings = listR.getData();
            //获取模板字段
            List<TemplateField> fields = fieldService.findAll(template.getId());
            if (fields == null) {
                throw new IllegalArgumentException(" error:--未找到模板id为" + template.getId() + "对应模板列信息");
            }
            List<Map<String, Object>> vals = getVal(headMap, cachedDataList, fields);
            R<List<CalculateValueVo>> authR = takeEffectAuthService.findAuthByUserId(userId);
            if (authR.getCode() != 200) {
                throw new ServiceException(authR.getMsg());
            }
            List<CalculateValueVo> calculateValueVos = authR.getData();
            if (template.getProcessingMode() == ProcessingMode.ADDTO) {
                String sql = SqlDefinitionFactory
                        .getInstanceByType(dataSource.getType())
                        .templateEntityTableInsertSql(fields, vals, entityTable.getDatabaseName(), entityTable.getTableName()).toString();
                log.info("-----------插入的sql:{}", sql);
                R r = remoteJdbcService.execDMlSql(dataSource.getId(), sql);
                if (r.getCode() != 200) {
                    throw new IllegalArgumentException("数据追加失败；sql:" + sql);
                }
            } else if (template.getProcessingMode() == ProcessingMode.REPLACE) {
                //主键存在，修改，不存在，则插入
                for (Map<String, Object> val : vals) {
                    String updateInsertSql = SqlDefinitionFactory
                            .getInstanceByType(dataSource.getType()).templateEntityTableUpdateInsertSql(mappings, calculateValueVos, fields, val, entityTable, fieldCheckUtil, template).toString();
                    R r = remoteJdbcService.execDMlSql(dataSource.getId(), updateInsertSql);
                    if (r.getCode() != 200) {
                        throw new IllegalArgumentException("替换查询主键失败；sql:" + updateInsertSql);
                    }
                }
            } else if (template.getProcessingMode() == ProcessingMode.COVER) {
                String sql = SqlDefinitionFactory
                        .getInstanceByType(dataSource.getType())
                        .templateEntityTableExcelDelDataSql(entityTable).toString();
                remoteJdbcService.execDMlSql(dataSource.getId(), sql);
                String sqlInsert = SqlDefinitionFactory
                        .getInstanceByType(dataSource.getType())
                        .templateEntityTableInsertSql(fields, vals, entityTable.getDatabaseName(), entityTable.getTableName()).toString();
                R r = remoteJdbcService.execDMlSql(dataSource.getId(), sqlInsert);
                if (r.getCode() != 200) {
                    throw new IllegalArgumentException("数据覆盖失败；sql:" + sql);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("追加主数据失败");
        }

    }

    private List<Map<String, Object>> getVal(Map<Integer, String> headMap, List<Map<Integer, String>> cachedDataList, List<TemplateField> fields) {
        List<Map<String, Object>> list = Lists.newArrayList();
        for (Map<Integer, String> map : cachedDataList) {
            Map<String, Object> objectMap = Maps.newHashMap();
            String data = "";
            for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                for (TemplateField field : fields) {
                    if (entry.getValue() != null) {
                        if (entry.getValue().equals(field.getFieldText())) {
                            objectMap.put(field.getFieldText(), map.get(entry.getKey()));
                        }
                    }
                }
            }
            list.add(objectMap);
        }
        return list;
    }

    private void saveMainData(Long userId, Template template, Map<Integer, String> headMap, List<Map<Integer, String>> cachedDataList, DataElement element) {
        //拼接图形sql
        try {
            //获取模板字段
            List<TemplateField> fields = fieldService.findAll(template.getId());
            if (fields == null) {
                throw new IllegalArgumentException(" error:--未找到模板id为" + template.getId() + "对应模板列信息");
            }
            if (template.getProcessingMode() == ProcessingMode.ADDTO) {
                String sql = mainDataAddTo(fields, headMap, cachedDataList, element);
                enteringMapper.executeInsert(sql);
            } else if (template.getProcessingMode() == ProcessingMode.REPLACE) {
                //先查询主键列是否重复，重复则修改，反则添加
                for (Map<Integer, String> integerStringMap : cachedDataList) {
                    List<TemplateField> fieldList = fields.stream().filter(field -> field.getIsPk()).collect(Collectors.toList());
                    String pkSql = jointMainSelectSql(element, fieldList, null, null, template.getId(), headMap, integerStringMap);
                    int i = enteringMapper.executeSelectCount(pkSql);
                    if (i > 0) {
                        String sql = mainDataUpdate(fields, headMap, integerStringMap, element, template.getId());
                        enteringMapper.executeInsert(sql);
                    } else {
                        String sql = mainDataAddTo(fields, headMap, Arrays.asList(integerStringMap), element);
                        enteringMapper.executeInsert(sql);
                    }
                }
            } else if (template.getProcessingMode() == ProcessingMode.COVER) {
                String delSql = enteringService.jointMainDelSql(element);
                enteringMapper.executeInsert(delSql);
                String sql = mainDataAddTo(fields, headMap, cachedDataList, element);
                enteringMapper.executeInsert(sql);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("追加主数据失败");
        }
    }

    private String jointMainSelectSql(DataElement element, List<TemplateField> fieldInfo, List<CalculateValueVo> valueVos, SearchVo searchVo, Long templateId, Map<Integer, String> headMap, Map<Integer, String> value) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(*) ");
        sql.append(" FROM ag_catalog.cypher('zjdata_element_graph', $$ ");
        sql.append(" MATCH (node: " + element.getMapTableName() + ") ");
        sql.append(" where 1=1 ");
        if (searchVo != null) {
            String condition = SearchUtils.parseSearchCondition("node", searchVo);
            sql.append(" and " + condition);
        }

        List<TemplateField> vlabelItems = fieldService.findAll(templateId);
        List<TemplateField> pkFields = null;
        if (vlabelItems != null) {
            pkFields = vlabelItems.stream().filter(field -> field.getIsPk()).collect(Collectors.toList());
        }
        if (pkFields == null) {
            throw new IllegalArgumentException("模板数据未找到主键列");
        }
        String pkValue = "";
        for (TemplateField pkField : pkFields) {
            String data = "";
            for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                if (entry.getValue().equals(pkField.getCode())) {
                    data = value.get(entry.getKey());
                    break;
                }
            }
            if (StringUtils.isEmpty(data)) {
                throw new IllegalArgumentException("未获取到主键列" + pkField.getCode() + "数据");
            }
            pkValue += " node." + pkField.getMapFieldName() + "=" + DataTypeChangeUtils.changeField(pkField.getFieldType(), data) + " and";
        }
        pkValue = pkValue.substring(0, pkValue.length() - 3);
        sql.append(" and ");
        sql.append(pkValue);

        //拼接行权限
       /* String condition=jointAuthSql(fieldInfo,valueVos);
        if (StringUtils.isNotEmpty(condition)) {
            sql.append(" where ");
            sql.append(condition);
        }*/
        //拼接返回列
        String res = jointResSql(fieldInfo);
        sql.append(" RETURN ");
        sql.append(res);
        return sql.toString();

    }

    private String jointResSql(List<TemplateField> fieldInfo) {
        String reuslt = "";
        String line = "";
        String lineProperty = "";
        for (TemplateField newField : fieldInfo) {
            line += "node." + newField.getMapFieldName() + " as " + newField.getFieldText() + " ,";
            lineProperty += newField.getFieldText()
                    + " "
                    + DataTypeChangeUtils.getFieldProperty(newField.getFieldType())
                    + " ,";
        }
        line = line.substring(0, line.length() - 1);
        lineProperty = lineProperty.substring(0, lineProperty.length() - 1);
        reuslt = line + " $$) as ( " + lineProperty + ")";
        return reuslt;
    }

    private String mainDataUpdate(List<TemplateField> fields, Map<Integer, String> headMap, Map<Integer, String> value, DataElement element, Long templateId) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(1) ");
        sql.append("FROM ag_catalog.cypher('zjdata_element_graph', $$ ");
        sql.append("MATCH (a:");
        sql.append(element.getMapTableName() + ")");
        sql.append(" WITH a ");
        sql.append(" MATCH (b: ");
        sql.append(element.getMapTableName() + ")");
        sql.append(" where ");
        List<TemplateField> vlabelItems = fieldService.findAll(templateId);
        List<TemplateField> pkFields = null;
        if (vlabelItems != null) {
            pkFields = vlabelItems.stream().filter(field -> field.getIsPk()).collect(Collectors.toList());
        }
        if (pkFields == null) {
            throw new IllegalArgumentException("模板数据未找到主键列");
        }
        String pkValue = "";
        for (TemplateField pkField : pkFields) {
            String data = "";
            for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                if (entry.getValue().equals(pkField.getCode())) {
                    data = value.get(entry.getKey());
                    break;
                }
            }
            if (StringUtils.isEmpty(data)) {
                throw new IllegalArgumentException("未获取到主键列" + pkField.getCode() + "数据");
            }
            pkValue += " b." + pkField.getMapFieldName() + "=" + DataTypeChangeUtils.changeField(pkField.getFieldType(), data) + " and";
        }
        pkValue = pkValue.substring(0, pkValue.length() - 3);
        sql.append(pkValue);
        String josnValue = " set ";
        for (TemplateField field : fields) {
            String data = "";
            for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                if (entry.getValue().equals(field.getCode())) {
                    data = value.get(entry.getKey());
                    break;
                }
            }
            //数据校验
            if (checkedData(field, data)) {
                String fieldValue = DataTypeChangeUtils.changeField(field.getFieldType(), data);
                if (StringUtils.isNotEmpty(fieldValue)) {
                    josnValue += " b." + field.getMapFieldName() + "=" + fieldValue + " ,";
                }
            } else {
                throw new IllegalArgumentException("必输项检查未通过");
            }

        }
        josnValue = josnValue.substring(0, josnValue.length() - 2);
        sql.append(josnValue);
        sql.append("RETURN id(a),properties(a) ");
        sql.append("$$) as (id ag_catalog.agtype,properties ag_catalog.agtype) ");
        return sql.toString();

    }

    private String mainDataAddTo(List<TemplateField> fields, Map<Integer, String> headMap, List<Map<Integer, String>> cachedDataList, DataElement element) {
        StringBuilder sql = new StringBuilder();
        try {
            sql.append("SELECT COUNT(1) ");
            sql.append("FROM ag_catalog.cypher('zjdata_element_graph', $$ ");
            for (Map<Integer, String> value : cachedDataList) {
                sql.append("CREATE (: ");
                sql.append(element.getMapTableName() + " ");
                //查出所有的
                String josnValue = "{";
                for (TemplateField field : fields) {
                    String data = "";
                    for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                        if (entry.getValue().equals(field.getCode())) {
                            data = value.get(entry.getKey());
                            break;
                        }
                    }
                    //数据校验
                    if (checkedData(field, data)) {
                        String fieldValue = DataTypeChangeUtils.changeField(field.getFieldType(), data);
                        if (StringUtils.isNotEmpty(fieldValue)) {
                            josnValue += " " + field.getMapFieldName() + ":" + fieldValue + " ,";
                        }
                    } else {
                        throw new IllegalArgumentException("必输项检查未通过");
                    }

                }
                String substring = josnValue.substring(0, josnValue.length() - 1);
                sql.append(substring);
                sql.append("})");
            }
            sql.append("$$) as (a ag_catalog.agtype) ");
        } catch (Exception e) {
            throw new ServiceException("追加数据错误");
        }

        return sql.toString();
    }

    private boolean checkedData(TemplateField field, String data) {
        //校验主键是否null值
        if (field.getIsPk() && StringUtils.isEmpty(data)) {
            log.error("主键列为null");
            return false;
        }
        //校验必须列是否null值
        if (field.getIsMust() && (StringUtils.isEmpty(data))) {
            log.error("必输列为null");
            return false;
        }
        return true;
    }

    @Override
    @Async
    public void readFile(Long templateId, Long userId, Long logId, int batchCount, String realPath, TemplateFieldConfigVo templateFieldConfigVo,
                         boolean useSkipError, String batchId, Resource resource) {
        File file = new File(realPath);
        // 获取excel中的行数

        // 导入的batchId 根据当前时间
        if (StringUtils.isEmpty(batchId)) {
            batchId = DateUtils.dateTimeNow();
        }
        GatherLog gatherLog = gatherLogService.insertLog(new GatherLog(logId, templateId, batchId, GatherLogStatus.RUNING));
        if (gatherLog == null) {
            throw new ServiceException("插入日志失败");
        }

        // 获取模板对象
        VlabelItem<Template> templateVlabelItem = templateMapper.selectById(templateId);
        if (templateVlabelItem == null) {
            throw new ServiceException(Status.TEMPLATE_NOT_EXIST);
        }
        Template template = templateVlabelItem.getProperties();

        try {

            // 在中redis生成一个key，用来判断该任务是否存在
            // gatherDataHandle.cacheGather(batchId, templateId);

            // 初始化数据
            TemplateDataDto templateDataDto;
            if (template.getTemplateType() == TemplateType.ENTITY) {
                // 初始化实体数据
                templateDataDto = gatherDataHandle.initEntityData(batchId, templateId, logId);
                templateDataDto.setUseSkipError(useSkipError);
            } else {
                // 初始化元素数据
                templateDataDto = gatherDataHandle.initElementData(batchId, templateId, logId);
            }

            // 是否不用指定行列来读excel
            if (templateFieldConfigVo != null) {
                // 读取excel中指定的行列数据
                this.registerReadListener(templateId, file, templateFieldConfigVo, batchId, batchCount, templateDataDto, resource);
            } else {
                EasyExcel.read(file)
                        .registerReadListener(new NoModelDataListener(templateId, batchId, batchCount, gatherDataHandle, templateDataDto, null, resource))
                        .sheet()
                        .doRead();
            }

            // 消费数据
            // gatherDataHandle.consumptionData(templateDataDto);

            // 删除key,代表任务结束
            // gatherDataHandle.deleteGatherKey(batchId, templateId);
        } catch (Exception e) {
            e.printStackTrace();
            // 记录错误日志
            String errorLog = e.getMessage();
            if (StringUtils.isEmpty(errorLog)) {
                errorLog = "java.lang.NullPointerException";
            } else {
                errorLog = errorLog.replaceAll("\\'", "\"");
            }
            gatherLogService.updateLog(new GatherLog(logId, GatherLogStatus.FALSE, errorLog, new Date()));
            // throw new ServiceException(e.getMessage());
        } finally {
            // file.delete();
        }
    }

    @Override
    @Async
    public void readFile(Long templateId, Long userId, Long logId, int batchCount, String realPath, TemplateFieldConfigVo templateFieldConfigVo, boolean useSkipError, String batchId, Resource resource, Long tenantId) {
        // 强制切换数据源查询指定租户数据源
        DataSourceHolder.setDataSourceKey(Constants.FORCE_SWITCH_TENANT_PREFIX + tenantId);
        File file = new File(realPath);
        // 获取excel中的行数

        // 导入的batchId 根据当前时间
        if (StringUtils.isEmpty(batchId)) {
            batchId = DateUtils.dateTimeNow();
        }
        GatherLog gatherLog = gatherLogService.insertLog(new GatherLog(logId, templateId, batchId, GatherLogStatus.RUNING));
        if (gatherLog == null) {
            throw new ServiceException("插入日志失败");
        }
        try {
            // 获取模板对象
            VlabelItem<Template> templateVlabelItem = templateMapper.selectById(templateId);
            if (templateVlabelItem == null) {
                throw new ServiceException(Status.TEMPLATE_NOT_EXIST);
            }
            Template template = templateVlabelItem.getProperties();

            // 在中redis生成一个key，用来判断该任务是否存在
            // gatherDataHandle.cacheGather(batchId, templateId);

            // 初始化数据
            TemplateDataDto templateDataDto;
            if (template.getTemplateType() == TemplateType.ENTITY) {
                // 初始化实体数据
                templateDataDto = gatherDataHandle.initEntityData(batchId, templateId, logId);
                templateDataDto.setUseSkipError(useSkipError);
            } else {
                // 初始化元素数据
                templateDataDto = gatherDataHandle.initElementData(batchId, templateId, logId);
            }

            // 是否不用指定行列来读excel
            if (templateFieldConfigVo != null) {
                // 读取excel中指定的行列数据
                this.registerReadListener(templateId, file, templateFieldConfigVo, batchId, batchCount, templateDataDto, resource);
            } else {
                EasyExcel.read(file)
                        .registerReadListener(new NoModelDataListener(templateId, batchId, batchCount, gatherDataHandle, templateDataDto, null, resource))
                        .sheet()
                        .doRead();
            }

            // 消费数据
            // gatherDataHandle.consumptionData(templateDataDto);

            // 删除key,代表任务结束
            // gatherDataHandle.deleteGatherKey(batchId, templateId);
        } catch (Exception e) {
            e.printStackTrace();
            // 记录错误日志
            String errorLog = e.getMessage();
            if (StringUtils.isEmpty(errorLog)) {
                errorLog = "java.lang.NullPointerException";
            } else {
                errorLog = errorLog.replaceAll("\\'", "\"");
            }
            gatherLogService.updateLog(new GatherLog(logId, GatherLogStatus.FALSE, errorLog, new Date()));
            // throw new ServiceException(e.getMessage());
        } finally {
            // 查询结果后还原
            DataSourceHolder.setDataSourceKey(null);
        }
    }

    // @Async
    public void registerReadListener(
            Long templateId,
            File file,
            TemplateFieldConfigVo templateFieldConfigVo,
            String batchId,
            int batchCount,
            TemplateDataDto templateDataDto,
            Resource resource
    ) throws Exception {
        // 获取模板中的所有字段
        List<TemplateField> templateFieldList = templateFieldService.findAll(templateId);
        if (CollectionUtils.isEmpty(templateFieldList)) {
            throw new ServiceException("模板字段不能为空");
        }
        // 开始位置
        String startPosition = "";
        // 结束类型
        ReadBelowEndType readBelowEndType = null;
        // 结束位置ddddd
        String endPosition = "";
        // 设置config
        List<TemplateFieldConfig> templateFieldConfigList = templateFieldConfigVo.getTemplateFieldConfigList();
        if (CollectionUtils.isNotEmpty(templateFieldConfigList)) {
            Stopwatch configSw = Stopwatch.createStarted();
            for (TemplateField templateField : templateFieldList) {
                // 过滤内置列
                if (TmpTableUtils.BATCHID.equalsIgnoreCase(templateField.getCode())) {
                    continue;
                }
                if (TmpTableUtils.IMPORT_TIME.equalsIgnoreCase(templateField.getCode())) {
                    continue;
                }

                for (TemplateFieldConfig templateFieldConfig : templateFieldConfigList) {
                    ReadType readType = templateFieldConfig.getReadType();
                    // 读取类型不能为空
                    if (readType == null) {
                        throw new ServiceException("读取类型不能为空");
                    }
                    String start = templateFieldConfig.getStartPosition();
                    ReadBelowEndType endType = templateFieldConfig.getEndType();
                    String end = templateFieldConfig.getEndPosition();
                    // 起始位置不能为空
                    if (StringUtils.isEmpty(start)) {
                        throw new ServiceException("起始位置不能为空");
                    }
                    // 结束类型不能为空
                    if (endType == null) {
                        throw new ServiceException("结束类型不能为空");
                    }
                    // 结束类型为POUR,则结束位置不能为空
                    if (endType == ReadBelowEndType.POUR && StringUtils.isEmpty(end)) {
                        throw new ServiceException("结束位置不能为空");
                    }
                    // 起始位置的值只能是数字
                    if (!start.matches("[0-9]+")) {
                        throw new ServiceException("起始位置的值只能是数字（行数）");
                    }
                    // 结束位置的值只能是数字
                    if (endType == ReadBelowEndType.POUR && !end.matches("[0-9]+")) {
                        throw new ServiceException("结束位置的值只能是数字（行数）");
                    }
                    // 起始和结束位置只能>=1
                    if (Integer.valueOf(start) < 1 || Integer.valueOf(start) < 1) {
                        throw new ServiceException("起始和结束位置只能大于等于1");
                    }
                    // 向下的值
                    String belowPosition = templateFieldConfig.getBelowPosition();
                    // 列位置不能为空
                    if (readType == ReadType.BELOW && StringUtils.isNotEmpty(belowPosition)) {
                        // 列位置只能是数字
                        if (!belowPosition.matches("[0-9]+")) {
                            throw new ServiceException("向下时,列位置只能是数字（列数）");
                        }
                        // 列位置只能>=1
                        if (Integer.valueOf(belowPosition) < 1) {
                            throw new ServiceException("向下时,列位置只能大于等于1");
                        }
                    }

                    // 循环的值只能是C5,A2格式
                    if (readType == ReadType.CYCLE) {
                        if (StringUtils.isEmpty(belowPosition)) {
                            throw new ServiceException("列位置不能为空");
                        }
                        // 拆分excel中的位置: A3 = [A,3]
                        String regex = "((?<=[a-zA-Z])(?=[0-9]))|((?<=[0-9])(?=[a-zA-Z]))";
                        String[] split = belowPosition.split(regex);
                        if (split.length != 2) {
                            throw new ServiceException("循环时,列位置格式不正确(如：A1,B2)");
                        }
                    }
                    // 设置统一的开始，结束类型，结束位置
                    startPosition = templateFieldConfig.getStartPosition();
                    readBelowEndType = templateFieldConfig.getEndType();
                    endPosition = templateFieldConfig.getEndPosition();
                    if (templateField.getCode().equalsIgnoreCase(templateFieldConfig.getFieldText())) {
                        templateField.setReadType(templateFieldConfig.getReadType());
                        templateField.setBelowPosition(templateFieldConfig.getBelowPosition());
                        templateField.setStartPosition(templateFieldConfig.getStartPosition());
                        templateField.setEndType(templateFieldConfig.getEndType());
                        templateField.setEndPosition(templateFieldConfig.getEndPosition());
                        break;
                    }
                }
            }
            log.info("批次数据[{}]读配置规范性校验耗时: {} 秒", batchId, configSw.elapsed(TimeUnit.SECONDS));
        }
        // 读取数据
        // gatherDataHandle.readData(file, templateId, batchId, batchCount, sheetNameList, startPosition, readBelowEndType, endPosition, templateFieldList, templateDataDto);

        int startPositionInt = Integer.valueOf(startPosition);
        int endPositionInt = 0;
        if (StringUtils.isNotEmpty(endPosition)) {
            endPositionInt = Integer.valueOf(endPosition);
        }

        // 获取文件名（包括扩展名）
        String fileName = file.getName();
        // 检查扩展名是否为.csv
        if (fileName.toLowerCase().endsWith(".csv")) {
            Stopwatch readCsvSW = Stopwatch.createStarted();
            EasyExcel.read(file)
                    .registerReadListener(new NoModelDataListener(templateId, batchId, batchCount, gatherDataHandle, templateDataDto, templateFieldList, resource))
                    .sheet()
                    .doRead();
            readCsvSW.stop();
            log.info("批次数据[{}]读取Csv文件耗时: {} 秒", batchId, readCsvSW.elapsed(TimeUnit.SECONDS));
        } else {
            Stopwatch sheetExcelSW = Stopwatch.createStarted();
            // 获取所有sheetName
            Map<Integer, String> sheetNameMap = ExcelUtils.getAllSheetName(file);
            // 采集任务Sheet页匹配条件
            SearchVo sheetSearch = templateFieldConfigVo.getSheetSearch();
            Set<String> sheetNameList = new HashSet<>();
            if (sheetSearch != null) {
                sheetNameList = SheetSearchUtils.parseSheetSearchCondition(sheetNameMap, templateFieldConfigVo.getSheetSearch());
            } else {
                // 如果没有配置Sheet匹配条件则默认读取Excel下所有Sheet页
                for (Map.Entry<Integer, String> entryMap : sheetNameMap.entrySet()) {
                    String sheetName = entryMap.getValue();
                    sheetNameList.add(sheetName);
                }
            }
            log.info("匹配的sheet页:{}", sheetNameList);
            if (CollectionUtils.isEmpty(sheetNameList)) {
                throw new ServiceException("没有匹配的sheet页");
            }
            FileInputStream fileInputStream = null;
            Workbook workbook = null;
            //XSSFWorkbook xssfWorkbook = null;
            for (String sheetName : sheetNameList) {
                Sheet sheet = null;
                try {
                    fileInputStream = new FileInputStream(file);
                    workbook = WorkbookFactory.create(fileInputStream);
                    //xssfWorkbook = new XSSFWorkbook(fileInputStream);
                    sheet = workbook.getSheet(sheetName);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new ServiceException(e.getMessage());
                } finally {
                    if (workbook != null) {
                        try {
                            workbook.close();
                        } catch (Exception e) {
                        }
                    }
                    if (fileInputStream != null) {
                        try {
                            fileInputStream.close();
                        } catch (Exception e) {
                        }
                    }
                    // gatherLogMapper.updateById(new GatherLog(templateDataDto.getLogId(), GatherLogStatus.TRUE, "执行完成"));
                }
                sheetExcelSW.stop();
                log.info("批次数据[{}]匹配SHEET页耗时: {} 秒", batchId, sheetExcelSW.elapsed(TimeUnit.SECONDS));
                Stopwatch readExcelSW = Stopwatch.createStarted();
                EasyExcel.read(file)
                        .registerReadListener(new ModelDataListener(sheet, batchId, batchCount,
                                startPositionInt, readBelowEndType, endPositionInt, gatherDataHandle,
                                templateDataDto, templateFieldList, resource))
                        .sheet(sheetName)
                        .headRowNumber(startPositionInt - 1) // 读取开始行
                        .doRead();
                readExcelSW.stop();
                log.info("批次数据[{}]读取Excel文件耗时: {} 秒", batchId, readExcelSW.elapsed(TimeUnit.SECONDS));
            }
        }


    }

    @Override
    public void getWriteData(Long templateId, Long userid, HttpServletResponse response, Boolean type) throws IOException {
        //找出有权限的模板
        List<TemplateField> fields = fieldService.findAll(templateId);
        List<List<String>> headList = Lists.newArrayList();
        List<List<Object>> dataList = Lists.newArrayList();
        Template template = templateMapper.selectById(templateId).getProperties();
        PageDataInfo<Map<String, Object>> pageDataInfo = enteringService.findData(templateId, template.getTemplateType(), 0, 2147483647, null);
        List<Map<String, Object>> mainDatas = pageDataInfo.getTotalList();
        for (TemplateField field : fields) {
            List<String> head = Lists.newArrayList();
            head.add(field.getFieldText());
            headList.add(head);
        }
        if (type) {
            for (Map<String, Object> mainData : mainDatas) {
                List<Object> data = Lists.newArrayList();
                for (TemplateField field : fields) {

                    data.add(mainData.get(field.getFieldText()));
                }
                dataList.add(data);
            }
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(template.getCode(), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream()).head(headList).sheet("模板").doWrite(dataList);
    }


    @Override
    @Async
    public void getFiles(Long templateId, Long userId, int batchCount, TemplateFieldConfigVo templateFieldConfigVo, boolean useSkipError, List<Resource> resourceList, List<Long> logIds) {
        List<String> batchNumbers = DateUtils.generateBatchNumbers(resourceList.size());
        for (int i = 0; i < resourceList.size(); i++) {
            Resource resource = resourceList.get(i);
            log.info("resource fileName {}, resource info {}", resource.getFileName(), JSON.toJSONString(resource));
            File file = new File(resource.getFullName());
            log.info("读取文件信息" + file.toString());
            if (file.exists()) {
                log.info("文件存在");
                readFile(templateId, userId, logIds.get(i), batchCount, file.getPath(), templateFieldConfigVo, useSkipError, batchNumbers.get(i), resource);
            }
        }
    }

    @Override
    @Async
    public void getFilesByTenantId(Long templateId, Long userId, int batchCount, TemplateFieldConfigVo templateFieldConfigVo, boolean useSkipError, List<Resource> resourceList, List<Long> logIds, Long tenantId) {
        List<String> batchNumbers = DateUtils.generateBatchNumbers(resourceList.size());
        for (int i = 0; i < resourceList.size(); i++) {
            Resource resource = resourceList.get(i);
            log.info("resource fileName {}, resource info {}", resource.getFileName(), JSON.toJSONString(resource));
            File file = new File(resource.getFullName());
            log.info("读取文件信息" + file.toString());
            if (file.exists()) {
                log.info("文件存在");
                readFile(templateId, userId, logIds.get(i), batchCount, file.getPath(), templateFieldConfigVo, useSkipError, batchNumbers.get(i), resource, tenantId);
            }
        }
    }

}
