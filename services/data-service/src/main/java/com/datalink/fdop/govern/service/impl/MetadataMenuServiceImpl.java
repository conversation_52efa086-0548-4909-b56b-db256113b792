package com.datalink.fdop.govern.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.MenuType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.MetadataMenu;
import com.datalink.fdop.govern.api.domain.MetadataTree;
import com.datalink.fdop.govern.api.model.vo.MetadataOverviewVo;
import com.datalink.fdop.govern.mapper.DataSourceSynchronizationMapper;
import com.datalink.fdop.govern.mapper.MetadataMenuMapper;
import com.datalink.fdop.govern.service.MetadataMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class MetadataMenuServiceImpl  implements MetadataMenuService {

    @Autowired
    private MetadataMenuMapper  metadataMenuMapper;

    @Autowired
    private DataSourceSynchronizationMapper synchronizationMapper;

    @Override
    @Transactional
    public int create(MetadataMenu metadataMenu) {
        if (metadataMenuMapper.selectByCode(metadataMenu.getCode()) != null) {
            throw new ServiceException(Status.STANDARD_MENU_ALREADY_EXISTS);
        }
        metadataMenu.setId(IdWorker.getId());
        int insert = metadataMenuMapper.insertMetadataMenu(metadataMenu);
        // 创建菜单边关系
        if (insert > 0 && metadataMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            metadataMenuMapper.createMetadataMenuEdge(metadataMenu.getPid(), Arrays.asList(metadataMenu.getId()));
        }
        return insert;
    }

    @Override
    public int update(MetadataMenu metadataMenu) {
        VlabelItem<MetadataMenu> vlabelItem = metadataMenuMapper.selectById(metadataMenu.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.STANDARD_MENU_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(metadataMenu.getCode()) && metadataMenuMapper.checkCodeIsExists(metadataMenu.getId(), metadataMenu.getCode()) != null) {
            throw new ServiceException(Status.STANDARD_MENU_ALREADY_EXISTS);
        }
        int update = metadataMenuMapper.updateById(metadataMenu);
        if (update > 0 && metadataMenu.getPid() != null) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            metadataMenuMapper.deleteMetadataMenuEdge(Arrays.asList(metadataMenu.getId()), vlabelItem.getProperties().getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (metadataMenu.getPid() != -1L) {
                metadataMenuMapper.createMetadataMenuEdge(metadataMenu.getPid(), Arrays.asList(metadataMenu.getId()));
            }
        }
        return update;
    }

    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<MetadataMenu> vlabelItem = metadataMenuMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            MetadataMenu metadataMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = metadataMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = metadataMenuMapper.bacthUpdatePidById(menuIdList, metadataMenu.getPid());
                if (update > 0 && metadataMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    metadataMenuMapper.createMetadataMenuEdge(metadataMenu.getPid(), menuIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return metadataMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<MetadataTree> tree(String sort, String code, Boolean isQueryNode) {

        // 所有的数据集合
        List<MetadataTree> trees = new ArrayList<>();
        // 添加数据元素树
        if (isQueryNode) {
            //正式表
            List<MetadataTree> metadataTrees = metadataMenuMapper.selectNodeTree(sort, code);
            trees.addAll(metadataTrees);
        }
        // 添加数据元素菜单树
        trees.addAll(metadataMenuMapper.selectMenuTree(sort, code));

        // 一级菜单集合
        List<MetadataTree> menuList = new ArrayList<>();

        // 获取第一级节点
        for (MetadataTree tree : trees) {
            if (tree.getPid().equals(-1L)) {
                menuList.add(tree);
            }
        }
        // 递归获取子节点
        for (MetadataTree parent : menuList) {
            recursiveTree(parent, trees);
        }
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(menuList);
        }
        return menuList;
    }

    @Override
    public int createNode(Long menuId, Long nodeId, MenuType menuType, String code, int serial) {
        String table ="";
        switch (menuType){
            case schema:
                table="d_g_synchronization_schema";
                break;
            case datasource:
                table="datasource";
                break;
            case database:
                table="d_g_synchronization_database";
                break;
            case table:
                table="d_g_synchronization_table";
                break;
            case view:
                table="d_g_synchronization_view";
                break;
            case field:
                table="d_g_synchronization_field";
                break;
        }

        if (StringUtils.isEmpty(table)) {
            throw new ServiceException("类型不匹配");
        }

        //创建关系
        return metadataMenuMapper.createMenuNode(menuId,nodeId,table,code,menuType,serial);
    }

    @Override
    public PageDataInfo overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<MetadataOverviewVo> page = PageUtils.getPage(MetadataOverviewVo.class);
        // 查询数据
        List<Long> menuIds = getChildrenMenuId(Arrays.asList(pid));
        IPage<MetadataOverviewVo> iPage = metadataMenuMapper.selectAll(page, sort, menuIds, searchVo);
        List<MetadataOverviewVo> records = iPage.getRecords();
        for (MetadataOverviewVo record : records) {
            MenuType menuType = record.getMenuType();
            String table ="";
            String table2 ="";
            switch (menuType){
                case schema:
                    table="d_g_synchronization_database";
                    table2="datasource";
                    break;
                case database:
                    table="datasource";
                    break;
                case table:
                    table="d_g_synchronization_schema";
                    break;
                case view:
                    table="d_g_synchronization_schema";
                    break;
                case field:
                    table="d_g_synchronization_view";
                    table2="d_g_synchronization_table";
                    break;
            }
            if (StringUtils.isNotEmpty(table)) {
                String metaData= synchronizationMapper.getMetadata(table,table2,record.getId());
                record.setMetadata(metaData);
            }

        }

        return PageUtils.getPageInfo(records,(int) iPage.getTotal());
    }

    @Override
    public int delNode(Long menuId, Long nodeId, String code) {
        return metadataMenuMapper.delMenuNode(menuId,nodeId,code);
    }



    /**
     * 递归获取子节点
     */
    private MetadataTree recursiveTree(MetadataTree parent, List<MetadataTree> menuList) {
        for (MetadataTree menu : menuList) {
            if (parent.getMenuType() == MenuType.MENU && parent.getId().equals(menu.getPid())) {
                // 如果是菜单就继续递归查询
                if (menu.getMenuType() == MenuType.MENU) {
                    menu = recursiveTree(menu, menuList);
                }
                parent.getChildren().add(menu);
            }
        }
        return parent;
    }


    public List<Long> getChildrenMenuId(List<Long> ids) {
        List<Long> childrenMenuIds = new ArrayList<>();
        List<Long> menuIds = metadataMenuMapper.selectByPids(ids);
        if (CollectionUtils.isNotEmpty(menuIds)) {
            childrenMenuIds.addAll(getChildrenMenuId(menuIds));
        }
        childrenMenuIds.addAll(ids);
        return childrenMenuIds;
    }
}
