package com.datalink.fdop.quality.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.quality.api.domain.DataQualityMenu;
import com.datalink.fdop.quality.api.domain.DataQualityTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataQualityMenuMapper extends BaseMapper<DataQualityMenu> {

    int createQualityMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertQualityMenu(@Param("dataQualityMenu") DataQualityMenu dataQualityMenu);

    int updateById(DataQualityMenu dataQualityMenu);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteQualityMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    DataQualityMenu selectById(Long id);

    DataQualityMenu selectByCode(String code);

    DataQualityMenu selectByPid(Long pid);

    DataQualityMenu checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<DataQualityTree> selectMenuTree(@Param("sort") String sort, @Param("code") String code);

}
