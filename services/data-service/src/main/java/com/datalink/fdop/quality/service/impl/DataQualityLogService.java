package com.datalink.fdop.quality.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.quality.api.domain.DataQualityLog;
import com.datalink.fdop.quality.api.domain.DataQualityMonitor;
import com.datalink.fdop.quality.api.enums.DataQualityLogStatus;
import com.datalink.fdop.quality.api.enums.DataQualityStorageType;
import com.datalink.fdop.quality.api.enums.InspectionType;
import com.datalink.fdop.quality.mapper.DataQualityLogMapper;
import com.datalink.fdop.quality.service.IDataQualityLogService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
public class DataQualityLogService implements IDataQualityLogService {

    private static final Logger logger = LoggerFactory.getLogger(DataQualityLogService.class);

    @Autowired
    private DataQualityLogMapper dataQualityLogMapper;

    @Autowired
    private DataQualityMonitorService dataQualityMonitorService;

    /**
     * 校验规则日志
     *
     * @param id
     * @return
     */
    private DataQualityLog checkQualityLog(Long id) {
        VlabelItem<DataQualityLog> vlabelItem = dataQualityLogMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.QUALITY_LOG_DOES_NOT_EXIST);
        }
        DataQualityLog dataQualityLog = vlabelItem.getProperties();
        return dataQualityLog;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataQualityLog create(DataQualityLog dataQualityLog) {
        String errorLog = dataQualityLog.getErrorLog();
        if (errorLog.contains("不通过")) {
            dataQualityLog.setErrorStatus(false);
        }else {
            dataQualityLog.setErrorStatus(true);
        }
        int insert = dataQualityLogMapper.insertQualityLog(dataQualityLog);
        // 绑定规则日志和实体的关系
        if (insert > 0 && dataQualityLog.getStorageType() != null && dataQualityLog.getStorageType() == DataQualityStorageType.ENTITY) {
            dataQualityLogMapper.creareQualityLogAndEntityEdge(dataQualityLog.getId(), dataQualityLog.getEntityId());
        }
        return dataQualityLog;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataQualityLog dataQualityLog) {
        // 校验规则日志
        checkQualityLog(dataQualityLog.getId());

        return dataQualityLogMapper.updateById(dataQualityLog);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        return dataQualityLogMapper.deleteBatchIds(ids);
    }

    @Override
    public PageDataInfo<DataQualityLog> list(DataQualityLog dataQualityLog) {
        // 获取分页参数
        Page<DataQualityLog> page = PageUtils.getPage(DataQualityLog.class);

        IPage<DataQualityLog> dataQualityLogIPage = dataQualityLogMapper.selectList(page, dataQualityLog);

        List<DataQualityLog> records = dataQualityLogIPage.getRecords();

        // 获取实体类型的监控日志id集合
        List<DataQualityLog> dataQualityLogs = new ArrayList<>();
        List<Long> entityIds = records.stream()
                .filter(dataQualityLog1 -> dataQualityLog1.getStorageType() == DataQualityStorageType.ENTITY)
                .map(DataQualityLog::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(entityIds)) {
            dataQualityLogs = dataQualityLogMapper.selectStorageNameByIds(entityIds);
        }

        for (DataQualityLog record : records) {
            if (record.getDataQualityLogStatus() != DataQualityLogStatus.SUCCESS && record.getDataQualityLogStatus() != DataQualityLogStatus.RUNNING_EXECUTION) {
                record.setDataQualityLogStatus(DataQualityLogStatus.FAILURE);
            }
            // 规则监控需要根据任务code查询规则监控的code
            if (record.getInspectionType() == InspectionType.RULE_MONITORING) {
                // 规则监控的任务名称是规则监控的id
                String taskInstanceName = record.getTaskInstanceName();
                DataQualityMonitor dataQualityMonitor = dataQualityMonitorService.selectById(Long.valueOf(taskInstanceName));
                record.setTaskInstanceName(dataQualityMonitor.getCode());
            }
            if (record.getStorageType() == null || record.getStorageType() == DataQualityStorageType.TABLE
                    || record.getStorageType() == DataQualityStorageType.VIEW) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(dataQualityLogs)) {
                for (DataQualityLog qualityLog : dataQualityLogs) {
                    if (qualityLog.getId().equals(record.getId())) {
                        record.setStorageName(qualityLog.getStorageName());
                        break;
                    }
                }
            }
        }
        return PageUtils.getPageInfo(records, (int) dataQualityLogIPage.getTotal());
    }
}
