package com.datalink.fdop.quality.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.domain.DataQualityTree;
import com.datalink.fdop.quality.api.model.dto.DataQualityDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataQualityMapper extends BaseMapper<DataQuality> {

    int createQualityAndMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertQuality(@Param("dataQuality") DataQuality dataQuality);

    int updateById(DataQuality dataQuality);

    int deleteQualityAndStandardEdge(@Param("qualityId") Long qualityId);

    int createQualityAndStandardEdge(@Param("qualityId") Long qualityId,
                                     @Param("standardId") Long standardId);

    Long selectStandardId(@Param("qualityId") Long qualityId);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteQualityAndMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    IPage<List<DataQuality>> selectList(IPage<VlabelItem> page, @Param("dataQuality") DataQuality dataQuality);

    DataQuality selectById(Long id);

    DataQuality selectByCode(String code);

    DataQuality checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<DataQualityTree> selectQualityTree(@Param("sort") String sort, @Param("code") String code);

    IPage<DataQuality> overview(IPage<DataQuality> page,
                                @Param("pid") Long pid,
                                @Param("sort") String sort,
                                @Param("searchVo") SearchVo searchVo);

    List<DataElementStructureVo> selectQualityElement(@Param("id") Long id, @Param("dataElementIdList") List<Long> dataElementIdList,
                                                      @Param("code") String code,
                                                      @Param("name") String name, @Param("isTree") Boolean isTree);

    List<DataElementStructureVo> selectQualityFieldElement(@Param("dataElementIdList") List<Long> dataElementIdList);

    boolean checkQualityElementEdge(
            @Param("id") Long id,
            @Param("dataElementId") Long dataElementId,
            @Param("parentDataElementId") Long parentDataElementId,
            @Param("isTree") Boolean isTree);

    int insertTreeQualityElementEdge(@Param("id") Long id, @Param("dataElementStructureVo") DataElementStructureVo dataElementStructureVo);

    int insertQualityElementEdge(@Param("id") Long id, @Param("dataElementStructureVo") DataElementStructureVo dataElementStructureVo);

    List<Integer> selectQualityAllMaxSeq(@Param("id") Long id, @Param("isPk") Boolean isPk);

    int deleteQualityElementEdge(@Param("id") Long id, @Param("dataElementStructureVo") DataElementStructureVo dataElementStructureVo);

    List<DataQualityDto> selectQualityByElementId(@Param("dataElementId") Long dataElementId);

}
