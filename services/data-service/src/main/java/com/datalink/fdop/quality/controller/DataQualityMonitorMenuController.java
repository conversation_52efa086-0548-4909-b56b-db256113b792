package com.datalink.fdop.quality.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.quality.api.domain.DataQualityMonitor;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorMenu;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorTree;
import com.datalink.fdop.quality.service.IDataQualityMonitorMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11 14:22
 */
@RequestMapping(value = "/quality/monitor/menu")
@RestController
@Api(tags = "监控菜单api")
public class DataQualityMonitorMenuController extends BaseController {

    @Autowired
    private IDataQualityMonitorMenuService dataQualityMonitorMenuService;

    @ApiOperation("查询规则监控菜单的序号")
    @Log(title = "数据质量")
    @GetMapping(value = "/querySerialNumber")
    public R<Integer> querySerialNumber() {
        return R.ok(dataQualityMonitorMenuService.querySerialNumber());
    }

    @ApiOperation("创建监控菜单")
    @Log(title = "数据质量", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataQualityMonitorMenu dataQualityMonitorMenu) {
        if (dataQualityMonitorMenu.getPid() == null) {
            dataQualityMonitorMenu.setPid(-1L);
        }
        return R.toResult(dataQualityMonitorMenuService.create(dataQualityMonitorMenu));
    }

    @ApiOperation("修改监控菜单")
    @Log(title = "数据质量", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataQualityMonitorMenu dataQualityMonitorMenu) {
        if (dataQualityMonitorMenu.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(dataQualityMonitorMenuService.update(dataQualityMonitorMenu));
    }

    @ApiOperation("删除监控菜单")
    @Log(title = "数据质量", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.SPECIFY_THE_MONITORING_MENU_TO_BE_DELETED);
        }
        return R.toResult(dataQualityMonitorMenuService.delete(ids));
    }

    @ApiOperation("展示监控树结构")
    @Log(title = "数据质量")
    @GetMapping(value = "/tree")
    public R<List<DataQualityMonitorTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode) {
        List<DataQualityMonitorTree> list = dataQualityMonitorMenuService.tree(sort, code, isQueryNode);
        return R.ok(list);
    }

    @ApiOperation("监控总览")
    @Log(title = "数据质量")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataQualityMonitor>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                        @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                        @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(dataQualityMonitorMenuService.overview(pid, sort, searchVo));
    }

}
