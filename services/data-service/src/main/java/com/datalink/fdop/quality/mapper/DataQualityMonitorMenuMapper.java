package com.datalink.fdop.quality.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorMenu;
import com.datalink.fdop.quality.api.domain.DataQualityMonitorTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataQualityMonitorMenuMapper extends BaseMapper<DataQualityMonitorMenu> {

    Integer querySerialNumber();

    int createQualityMonitorMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertQualityMonitorMenu(@Param("dataQualityMonitorMenu") DataQualityMonitorMenu dataQualityMonitorMenu);

    int updateById(DataQualityMonitorMenu dataQualityMonitorMenu);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteQualityMonitorMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    DataQualityMonitorMenu selectById(Long id);

    DataQualityMonitorMenu selectByCode(String code);

    DataQualityMonitorMenu selectByPid(Long pid);

    DataQualityMonitorMenu checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<DataQualityMonitorTree> selectMenuTree(@Param("sort") String sort, @Param("code") String code);

}
