package com.datalink.fdop.graph.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.ResourceUtils;
import com.datalink.fdop.common.core.utils.SpringUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.drive.api.RemoteFieldRelationService;
import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.graph.api.enums.RuntimeMode;
import com.datalink.fdop.graph.api.graph.etl.enums.EtlNodeType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/22 14:06
 */
public class FlinkSqlUtils {

    public static final String CATALOG = "iceberg";

    // flink-doris配置项
    public static final String FLINK_DORIS_SINK = "'sink.enable-delete' = 'false','sink.properties.escape_delimiters' = 'true','sink.properties.column_separator' = '\\x01','sink.properties.format' = 'json','sink.properties.read_json_by_line' = 'true','sink.label-prefix' = '$labelPrefix'";
    public static final String FLINK_DORIS_SINK_CDC = "'sink.enable-delete' = 'true'";

    /**
     * 获取flink jdbcUrl
     *
     * @param urlPrefix
     * @param host
     * @param port
     * @param databaseName
     * @param urlSuffix
     * @return
     */
    public static String getFlinkJdbcUrl(String urlPrefix, String host, int port, String databaseName, String urlSuffix) {
        return String.format("%s%s:%s/%s%s", urlPrefix, host, port, databaseName, (StringUtils.isEmpty(urlSuffix) ? "" : "?" + urlSuffix));
    }

    public static String getPgFlinkJdbcUrl(String urlPrefix, String host, int port, String databaseName, String schema, String urlSuffix) {
        return String.format("%s%s:%s/%s?currentSchema=%s%s", urlPrefix, host, port, databaseName, schema, (StringUtils.isEmpty(urlSuffix) ? "" : "&" + urlSuffix));
    }

    public static String getHanaFlinkJdbcUrl(String urlPrefix, String host, int port, String databaseName, String urlSuffix) {
        return String.format("%s%s:%s/?databaseName=%s%s", urlPrefix, host, port, databaseName, (StringUtils.isEmpty(urlSuffix) ? "" : "&" + urlSuffix));
    }

    public static String getSqlServerFlinkJdbcUrl(String urlPrefix, String host, int port, String databaseName, String urlSuffix) {
        return String.format("%s%s:%s;database=%s;%s", urlPrefix, host, port, databaseName, (StringUtils.isEmpty(urlSuffix) ? "" : urlSuffix));
    }

    /**
     * 获取flink sql
     *
     * @param field
     * @param dataSourceBasicInfo
     * @param runtimeMode
     * @return
     */
    public static String getFlinkSql(String flinkTableName, String field, DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName, EtlNodeType nodeType, String dataFilter, RuntimeMode runtimeMode) {
        switch (dataSourceBasicInfo.getType()) {
            case "mysql":
                return getFlinkMysqlSql(flinkTableName, field, dataSourceBasicInfo, databaseName, tableName, dataFilter, runtimeMode);
            case "postgresql":
                // pg类型的databaseName是schemaName
                return getFlinkPgSql(flinkTableName, field, dataSourceBasicInfo, databaseName, tableName, dataFilter, runtimeMode);
            case "sqlserver":
                return getFlinkSqlServerSql(flinkTableName, field, dataSourceBasicInfo, databaseName, tableName, dataFilter, runtimeMode);
            case "iceberg":
                return getFlinkIcebergSql(flinkTableName, field, databaseName, tableName, dataSourceBasicInfo.getMetastore(), dataSourceBasicInfo.getWarehouse());
            case "doris":
                return getFlinkDorisSql(flinkTableName, field, dataSourceBasicInfo, databaseName, tableName, nodeType, runtimeMode);
            case "hana":
                return getFlinkHanaSql(flinkTableName, field, dataSourceBasicInfo, databaseName, tableName, dataFilter, runtimeMode);
            case "oracle":
                return getFlinkOracleSql(flinkTableName, field, dataSourceBasicInfo, databaseName, tableName, dataFilter, runtimeMode);
            case "risingwave":
                return getFlinkPgSql(flinkTableName, field, dataSourceBasicInfo, databaseName, tableName, dataFilter, runtimeMode);
            default:
                throw new ServiceException(Status.UNKNOWN_DATA_SOURCE_TYPE);
        }
    }

    /**
     * 获取redis的flink sql
     *
     * @param flinkTableName
     * @param host
     * @param port
     * @param password
     * @param command
     * @return
     */
    public static String getRedisFlinkSql(String flinkTableName, String host, String port, String password, String command) {
        String flinkSql = ResourceUtils.getResourceContent("flink/flink-redis");
        return flinkSql.replaceAll("\\$flinkTableName", flinkTableName)
                .replaceAll("\\$host", host)
                .replaceAll("\\$port", port)
                .replaceAll("\\$password", password)
                .replaceAll("\\$command", command);
    }


    /**
     * 获取flink mysql sql
     *
     * @param flinkTableName
     * @param field
     * @param dataSourceBasicInfo
     * @param databaseName
     * @param tableName
     * @param runtimeMode
     * @return
     */
    public static String getFlinkMysqlSql(String flinkTableName, String field, DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName, String dataFilter, RuntimeMode runtimeMode) {
        String flinkSql;
        // 批/流
        if (runtimeMode == null || runtimeMode == RuntimeMode.BATCH) {
            String jdbcUrl = FlinkSqlUtils.getFlinkJdbcUrl(dataSourceBasicInfo.getUrlPrefix(), dataSourceBasicInfo.getHost(), dataSourceBasicInfo.getPort(), databaseName, dataSourceBasicInfo.getUrlSuffix());
            return getFlinkSql(flinkTableName, field, jdbcUrl, dataSourceBasicInfo.getUsername(), dataSourceBasicInfo.getPassword(), tableName, dataFilter);
        } else {
            flinkSql = ResourceUtils.getResourceContent("flink/flink-mysql-cdc");
            return flinkSql.replaceAll("\\$flinkTableName", flinkTableName)
                    .replaceAll("\\$field", field)
                    .replaceAll("\\$hostname", dataSourceBasicInfo.getHost())
                    .replaceAll("\\$port", dataSourceBasicInfo.getPort() + "")
                    .replaceAll("\\$username", dataSourceBasicInfo.getUsername())
                    .replaceAll("\\$password", dataSourceBasicInfo.getPassword())
                    .replaceAll("\\$databaseName", databaseName)
                    .replaceAll("\\$tableName", tableName);
        }
    }

    public static String getFlinkDorisSql(String flinkTableName, String field, DataSourceBasicInfo dataSourceBasicInfo, String databaseName, String tableName, EtlNodeType nodeType, RuntimeMode runtimeMode) {
        String flinkSql;
        // 批/流
        if (runtimeMode == null || runtimeMode == RuntimeMode.BATCH) {
            return getFlinkBatchDorisSql(flinkTableName, field, dataSourceBasicInfo.getHost(), String.valueOf(dataSourceBasicInfo.getBePort()), databaseName, tableName, dataSourceBasicInfo.getUsername(), dataSourceBasicInfo.getPassword(), nodeType);
        } else {
            flinkSql = ResourceUtils.getResourceContent("flink/flink-doris");
            // 替换基础参数
            flinkSql = flinkSql.replaceAll("\\$flinkTableName", flinkTableName)
                    .replaceAll("\\$field", field)
                    .replaceAll("\\$hostname", dataSourceBasicInfo.getHost())
                    .replaceAll("\\$port", dataSourceBasicInfo.getBePort() + "")
                    .replaceAll("\\$databaseName", databaseName)
                    .replaceAll("\\$tableName", tableName)
                    .replaceAll("\\$username", dataSourceBasicInfo.getUsername())
                    .replaceAll("\\$password", dataSourceBasicInfo.getPassword());
            // 添加flink-doris-cdc的配置
            if (nodeType == EtlNodeType.SINK) {
                flinkSql = flinkSql.replace("$sink", "," + FLINK_DORIS_SINK + "," + FLINK_DORIS_SINK_CDC)
                        .replace("$labelPrefix", IdWorker.getIdStr());
            } else if (nodeType == EtlNodeType.SOURCE) {
                flinkSql = flinkSql.replace("$sink", "");
            }
            return flinkSql;
        }
    }

    /**
     * 获取flink oracle sql
     *
     * @param flinkTableName
     * @param field
     * @param dataSourceBasicInfo
     * @param schemaName
     * @param tableName
     * @param dataFilter
     * @param runtimeMode
     * @return
     */
    public static String getFlinkOracleSql(String flinkTableName, String field, DataSourceBasicInfo dataSourceBasicInfo, String schemaName, String tableName, String dataFilter, RuntimeMode runtimeMode) {
        String flinkSql;
        // 批/流
        if (runtimeMode == null || runtimeMode == RuntimeMode.BATCH) {
            return getFlinkSql(flinkTableName, field, dataSourceBasicInfo.getJdbcUrl().replace("//", ""), dataSourceBasicInfo.getUsername(), dataSourceBasicInfo.getPassword(), schemaName + "." + tableName, dataFilter);
        } else {
            throw new ServiceException("目前不支持oracle的流任务");
        }
    }

    /**
     * 获取flink hana sql
     *
     * @param flinkTableName
     * @param field
     * @param dataSourceBasicInfo
     * @param schemaName
     * @param tableName
     * @param runtimeMode
     * @return
     */
    public static String getFlinkHanaSql(String flinkTableName, String field, DataSourceBasicInfo dataSourceBasicInfo, String schemaName, String tableName, String dataFilter, RuntimeMode runtimeMode) {
        String flinkSql;
        // 批/流
        if (runtimeMode == null || runtimeMode == RuntimeMode.BATCH) {
            String jdbcUrl = FlinkSqlUtils.getHanaFlinkJdbcUrl(dataSourceBasicInfo.getUrlPrefix(), dataSourceBasicInfo.getHost(), dataSourceBasicInfo.getPort(), dataSourceBasicInfo.getDatabase(), dataSourceBasicInfo.getUrlSuffix());
            return getFlinkSql(flinkTableName, field, jdbcUrl, dataSourceBasicInfo.getUsername(), dataSourceBasicInfo.getPassword(), schemaName + "." + tableName, dataFilter);
        } else {
            throw new ServiceException("目前不支持hana的流任务");
        }
    }

    /**
     * 获取flink postgres sql
     *
     * @param flinkTableName
     * @param field
     * @param dataSourceBasicInfo
     * @param schemaName
     * @param tableName
     * @param runtimeMode
     * @return
     */
    public static String getFlinkPgSql(String flinkTableName, String field, DataSourceBasicInfo dataSourceBasicInfo, String schemaName, String tableName, String dataFilter, RuntimeMode runtimeMode) {
        String flinkSql;
        // 批/流
        if (runtimeMode == null || runtimeMode == RuntimeMode.BATCH) {
            String jdbcUrl = FlinkSqlUtils.getPgFlinkJdbcUrl(dataSourceBasicInfo.getUrlPrefix(), dataSourceBasicInfo.getHost(), dataSourceBasicInfo.getPort(), dataSourceBasicInfo.getDatabase(), schemaName, dataSourceBasicInfo.getUrlSuffix());
            return getFlinkSql(flinkTableName, field, jdbcUrl, dataSourceBasicInfo.getUsername(), dataSourceBasicInfo.getPassword(), tableName, dataFilter);
        } else {
            flinkSql = ResourceUtils.getResourceContent("flink/flink-postgresql-cdc");
            return flinkSql.replaceAll("\\$flinkTableName", flinkTableName)
                    .replaceAll("\\$field", field)
                    .replaceAll("\\$hostname", dataSourceBasicInfo.getHost())
                    .replaceAll("\\$port", dataSourceBasicInfo.getPort() + "")
                    .replaceAll("\\$username", dataSourceBasicInfo.getUsername())
                    .replaceAll("\\$password", dataSourceBasicInfo.getPassword())
                    .replaceAll("\\$databaseName", dataSourceBasicInfo.getDatabase())
                    .replaceAll("\\$schemaName", schemaName)
                    .replaceAll("\\$tableName", tableName);
        }
    }

    /**
     * 获取flink sqlserver sql
     *
     * @param flinkTableName
     * @param field
     * @param dataSourceBasicInfo
     * @param schemaName
     * @param tableName
     * @param runtimeMode
     * @return
     */
    public static String getFlinkSqlServerSql(String flinkTableName, String field, DataSourceBasicInfo dataSourceBasicInfo, String schemaName, String tableName, String dataFilter, RuntimeMode runtimeMode) {
        String flinkSql;
        // 批/流
        if (runtimeMode == null || runtimeMode == RuntimeMode.BATCH) {
            String jdbcUrl = FlinkSqlUtils.getSqlServerFlinkJdbcUrl(dataSourceBasicInfo.getUrlPrefix(), dataSourceBasicInfo.getHost(), dataSourceBasicInfo.getPort(), dataSourceBasicInfo.getDatabase(), dataSourceBasicInfo.getUrlSuffix());
            return getFlinkSql(flinkTableName, field, jdbcUrl, dataSourceBasicInfo.getUsername(), dataSourceBasicInfo.getPassword(), schemaName + "." + tableName, dataFilter);
        } else {
            flinkSql = ResourceUtils.getResourceContent("flink/flink-sqlserver-cdc");
            return flinkSql.replaceAll("\\$flinkTableName", flinkTableName)
                    .replaceAll("\\$field", field)
                    .replaceAll("\\$hostname", dataSourceBasicInfo.getHost())
                    .replaceAll("\\$port", dataSourceBasicInfo.getPort() + "")
                    .replaceAll("\\$username", dataSourceBasicInfo.getUsername())
                    .replaceAll("\\$password", dataSourceBasicInfo.getPassword())
                    .replaceAll("\\$databaseName", dataSourceBasicInfo.getDatabase())
                    .replaceAll("\\$schemaName", schemaName)
                    .replaceAll("\\$tableName", tableName);
        }
    }

    /**
     * 获取flink iceberg sql
     *
     * @param flinkTableName
     * @param field
     * @param databaseName
     * @param tableName
     * @param metastore
     * @param warehouse
     * @return
     */
    public static String getFlinkIcebergSql(String flinkTableName, String field, String databaseName, String tableName, String metastore, String warehouse) {
        String flinkSql = ResourceUtils.getResourceContent("flink/flink-iceberg");
        return flinkSql.replaceAll("\\$flinkTableName", flinkTableName)
                .replaceAll("\\$field", field)
                .replaceAll("\\$databaseName", databaseName)
                .replaceAll("\\$tableName", tableName)
                .replaceAll("\\$metastore", metastore)
                .replaceAll("\\$warehouse", warehouse);
    }

    /**
     * 获取flink sql
     *
     * @param field
     * @param url
     * @param username
     * @param password
     * @param tableName
     * @return
     */
    public static String getFlinkSql(String flinkTableName, String field, String url, String username, String password, String tableName, String dataFilter) {
        String flinkSql = ResourceUtils.getResourceContent("flink/flink-sql");

        return flinkSql.replaceAll("\\$flinkTableName", flinkTableName)
                .replaceAll("\\$field", field)
                .replaceAll("\\$url", url)
                .replaceAll("\\$username", username)
                .replaceAll("\\$password", StringUtils.isEmpty(password) ? "" : password)
                .replaceAll("\\$tableName", tableName)
                .replaceAll("\\$dataFilter", StringUtils.isNotEmpty(dataFilter) ? dataFilter : "");
    }

    /**
     * flink batch doris
     *
     * @param flinkTableName
     * @param field
     * @param hostname
     * @param port
     * @param databaseName
     * @param tableName
     * @param username
     * @param password
     * @return
     */
    public static String getFlinkBatchDorisSql(String flinkTableName, String field, String hostname, String port, String databaseName, String tableName, String username, String password, EtlNodeType nodeType) {
        String flinkSql = ResourceUtils.getResourceContent("flink/flink-doris");
        // 替换基础参数
        flinkSql = flinkSql.replaceAll("\\$flinkTableName", flinkTableName)
                .replaceAll("\\$field", field)
                .replaceAll("\\$hostname", hostname)
                .replaceAll("\\$port", port)
                .replaceAll("\\$databaseName", databaseName)
                .replaceAll("\\$tableName", tableName)
                .replaceAll("\\$username", username)
                .replaceAll("\\$password", password);
        // 添加flink-doris的配置
        if (nodeType == EtlNodeType.SINK) {
            flinkSql = flinkSql.replace("$sink", "," + FLINK_DORIS_SINK)
                    .replace("$labelPrefix", IdWorker.getIdStr());
        } else if (nodeType == EtlNodeType.SOURCE) {
            flinkSql = flinkSql.replace("$sink", "");
        }
        return flinkSql;
    }

    /**
     * 获取flink field sql
     *
     * @param fieldName
     * @param fieldType
     * @param length
     * @param decimalLength
     * @return
     */
    public static String getFieldSql(String fieldName, String fieldType, Long length, Long decimalLength) {
        return fieldName + " " + FlinkFieldRefUtils.convertField(fieldType, length, decimalLength);
    }

    public static List<String> getInsertFieldSqlList(String dbType, List<Field> fields, String tableName) {
        // 转换字段
        List<Field> fieldList = remoteToSource(dbType, fields);

        // 拼接字段sql
        return fieldList.stream().map(field -> {
            if ("iceberg".equalsIgnoreCase(dbType) && field.getFlinkFieldType().contains("TIMESTAMP")) {
                field.setDecimalLength(6L);
            }
            String prefix = "";
            if (StringUtils.isNotEmpty(tableName)) {
                prefix = "`" + tableName + "`.";
            }
            return prefix + "`" + field.getFieldName() + "`";
        }).collect(Collectors.toList());
    }

    /**
     * 转换成flink字段类型
     *
     * @param fields
     * @return
     */
    public static List<String> getFieldSqlList(String dbType, List<Field> fields) {
        // 转换字段
        List<Field> fieldList = remoteToSource(dbType, fields);

        // 拼接字段sql
        return fieldList.stream().map(field -> {
            if ("iceberg".equalsIgnoreCase(dbType) && field.getFlinkFieldType().contains("TIMESTAMP")) {
                field.setDecimalLength(6L);
            }
            return "`" + field.getFieldName() + "`" + " " + FlinkFieldRefUtils.convertField(field.getFlinkFieldType(), field.getLength(), field.getDecimalLength());
        }).collect(Collectors.toList());
    }

    /**
     * 获取flink 主键 field sql
     *
     * @param pkFieldName
     * @return
     */
    public static String getPkFieldSql(String pkFieldName) {
        return "PRIMARY KEY (" + pkFieldName + ") NOT ENFORCED";
    }

    /**
     * TODO:待解决iceberg的流读
     * 批/流
     *
     * @return
     */
    public static String getBatchOrStreamSql(String sql, RuntimeMode runtimeMode) {
        // if (isBatch) {
        //     return sql;
        // }
        // return sql + " /*+ OPTIONS('streaming'='true', 'monitor-interval'='10s')*/";
        return sql;
    }

    private static List<Field> remoteToSource(String dbType, List<Field> fields) {
        RemoteFieldRelationService remoteFieldRelationService = SpringUtils.getBean(RemoteFieldRelationService.class);
        R<List<Field>> r = remoteFieldRelationService.toSource(dbType, fields);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

}
