package com.datalink.fdop.quality.service;


import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.quality.api.domain.DataQualityMenu;
import com.datalink.fdop.quality.api.domain.DataQualityTree;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IDataQualityMenuService {

    int create(DataQualityMenu dataQualityMenu);

    int update(DataQualityMenu dataQualityMenu);

    int delete(List<Long> ids);

    List<DataQualityTree> tree(String sort, String code, Boolean isQueryNode);

    PageDataInfo<DataQuality> overview(Long pid, String sort, SearchVo searchVo);

}
