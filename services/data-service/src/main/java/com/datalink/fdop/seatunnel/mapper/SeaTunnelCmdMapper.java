package com.datalink.fdop.seatunnel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface SeaTunnelCmdMapper extends BaseMapper<SeaTunnelCmd> {

    int createSeaTunnelCmdAndMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertSeaTunnelCmd(@Param("seaTunnelCmd") SeaTunnelCmd seaTunnelCmd);

    int updateById(SeaTunnelCmd seaTunnelCmd);

    int updateSeaTunnelTaskJobIdEmpty(@Param("id") Long id);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteSeaTunnelCmdAndMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<SeaTunnelCmd> selectById(Long id);

    VlabelItem<SeaTunnelCmd> selectByCode(String code);

    VlabelItem<SeaTunnelCmd> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<SeaTunnelCmdTree> selectTree(@Param("sort") String sort, @Param("code") String code);

    Integer querySerialNumber();

}
