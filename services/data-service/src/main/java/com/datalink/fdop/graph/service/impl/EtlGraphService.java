package com.datalink.fdop.graph.service.impl;

import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.element.api.RemoteEntityService;
import com.datalink.fdop.element.api.RemoteEntityTableService;
import com.datalink.fdop.graph.api.enums.NodeRelation;
import com.datalink.fdop.graph.api.enums.RuntimeMode;
import com.datalink.fdop.graph.api.graph.etl.EtlDataEntity;
import com.datalink.fdop.graph.api.graph.etl.EtlDataSource;
import com.datalink.fdop.graph.api.graph.etl.EtlNode;
import com.datalink.fdop.graph.api.graph.etl.EtlNodeData;
import com.datalink.fdop.graph.api.graph.etl.enums.EtlSourceType;
import com.datalink.fdop.graph.api.graph.index.*;
import com.datalink.fdop.graph.api.graph.index.enums.*;
import com.datalink.fdop.graph.utils.FlinkFieldRefUtils;
import com.datalink.fdop.graph.utils.FlinkSqlUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/16 17:05
 */
@Service
public class EtlGraphService {

    private static final Logger logger = LoggerFactory.getLogger(EtlGraphService.class);

    @Autowired
    private RemoteEntityService remoteEntityService;

    @Autowired
    private RemoteEntityTableService remoteEntityTableService;

    public LinkedHashMap<String, String> getEtlGraphSql(EtlNode transformNode) {
        // 校验
        GraphJson graph = checkEtlGraphJson(transformNode);

        List<NodeRelation> nodeRelation = graph.getGraphNodeRelation();

        List<GraphNode> graphNodes = graph.getGraphNodes();

        // 节点关系和节点数量需要保持一致
        if (nodeRelation.size() != graphNodes.stream().filter(graphNode -> graphNode.getGraphNodeType() != GraphNodeType.SOURCE).count()) {
            throw new ServiceException("节点关系校验失败");
        }

        // 获取projection节点的flink sql
        List<GraphNode> projectionNodes = graphNodes.stream().filter(graphNode -> graphNode.getGraphNodeType() == GraphNodeType.PROJECTION).collect(Collectors.toList());
        LinkedHashMap<String, String> projectionSqls = getProjectionSqls(projectionNodes);

        // 获取union节点的flink sql
        List<GraphNode> unionNodes = graphNodes.stream().filter(graphNode -> graphNode.getGraphNodeType() == GraphNodeType.UNION).collect(Collectors.toList());
        LinkedHashMap<String, String> unionSqls = getUnionSqls(unionNodes);

        // 获取join节点的flink sql
        List<GraphNode> joinNodes = graphNodes.stream().filter(graphNode -> graphNode.getGraphNodeType() == GraphNodeType.JOIN).collect(Collectors.toList());
        LinkedHashMap<String, String> joinSqls = getJoinSqls(joinNodes);

        // 获取aggregation节点的flink sql
        List<GraphNode> aggregationNodes = graphNodes.stream().filter(graphNode -> graphNode.getGraphNodeType() == GraphNodeType.AGGREGATION).collect(Collectors.toList());
        LinkedHashMap<String, String> aggregationSqls = getAggregationSqls(aggregationNodes);

        // 获取rank节点的flink sql
        List<GraphNode> rankNodes = graphNodes.stream().filter(graphNode -> graphNode.getGraphNodeType() == GraphNodeType.RANK).collect(Collectors.toList());
        LinkedHashMap<String, String> rankSqls = getRankSqls(rankNodes);

        // 所有finksql
        LinkedHashMap<String, String> allFinkSqls = new LinkedHashMap<>();

        allFinkSqls.putAll(projectionSqls);
        allFinkSqls.putAll(unionSqls);
        allFinkSqls.putAll(joinSqls);
        allFinkSqls.putAll(aggregationSqls);
        allFinkSqls.putAll(rankSqls);

        // 根据节点顺序排序flink sql
        LinkedHashMap<String, String> finkOrderSqls = new LinkedHashMap<>();
        for (GraphNode graphNode : graphNodes) {
            if (graphNode.getGraphNodeType() == GraphNodeType.SOURCE) {
                continue;
            }
            finkOrderSqls.put(graphNode.getNodeCode(), allFinkSqls.get(graphNode.getNodeCode()));
        }

        return finkOrderSqls;
    }

    private LinkedHashMap<String, String> getRankSqls(List<GraphNode> rankNodes) {
        LinkedHashMap<String, String> rankSqls = new LinkedHashMap<>(rankNodes.size());

        for (GraphNode rankNode : rankNodes) {
            String flinkSql = "SELECT ";

            List<GraphOutputField> outputFieldList = rankNode.getOutputFieldList();

            // 普通字段sql table.field1,table.field2,field3
            String fieldSql = outputFieldList.stream()
                    // .filter(outputField -> outputField.getFieldSource() == GraphFieldSourceType.CITE)
                    .map(outputField -> {
                        if (outputField.getFieldSource() == GraphFieldSourceType.CITE) {
                            return outputField.getPreNodeName() + ".`" + outputField.getPreFieldName() + "` AS `" + outputField.getFieldName() + "`";
                        } else if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                            String sql = "";
                            GraphRank rank = outputField.getRank();

                            logger.info("windowFun:{}", rank.getWindowFun());

                            // 窗口聚合字段
                            sql += rank.getWindowFun().name();
                            switch (rank.getWindowFun()) {
                                case RANK:
                                case ROW_NUMBER:
                                    sql += "()";
                                    break;
                                default:
                                    sql += "(" + rank.getWindowFieldName() + ")";
                                    break;
                            }

                            sql += " OVER (";

                            // partition by sql
                            sql += rank.getPartitionFieldNames().stream().collect(Collectors.joining(",", "PARTITION BY ", " "));

                            // order by sql
                            sql += rank.getOrderFields().stream()
                                    .map(orderField -> {
                                        return orderField.getOrderFieldName() + " " + orderField.getSort().name();
                                    })
                                    .collect(Collectors.joining(",", "ORDER BY ", " "));

                            // 设置窗口聚合别名
                            if ((rank.getWindowFun() == WindowFun.RANK || rank.getWindowFun() == WindowFun.ROW_NUMBER) && rank.getLimitType() != LimitType.ALL) {
                                sql += ") AS rownum";
                            } else {
                                sql += ") AS `" + outputField.getFieldName() + "`";
                            }

                            return sql;
                        } else {
                            throw new ServiceException("graph中的节点{" + rankNode.getNodeName() + "}出现未知类型{" + outputField.getFieldSource() + "}");
                        }
                    }).collect(Collectors.joining(","));

            // 获取前置节点名称
            String preNodeName = rankNode.getPreNodes().get(0).getNodeCode();

            flinkSql += fieldSql + " FROM " + preNodeName;

            // RANK是否计算，只能有一个RANK计算
            List<GraphOutputField> rankOutputFieldList = outputFieldList.stream()
                    .filter(outputField -> outputField.getFieldSource() == GraphFieldSourceType.CALC && outputField.getRank().getLimitType() != null && outputField.getRank().getLimitType() != LimitType.ALL)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(rankOutputFieldList)) {
                GraphOutputField rankGraphOutputField = rankOutputFieldList.get(0);
                GraphRank rank = rankGraphOutputField.getRank();

                flinkSql = "SELECT * FROM (" + flinkSql + ") WHERE rownum <=";

                if (rank.getLimitType() == LimitType.FIXED) {
                    flinkSql += rank.getLimit();
                } else if (rank.getLimitType() == LimitType.PARAM) {
                    // TODO:待实现
                }
            }

            flinkSql = "CREATE VIEW " + rankNode.getNodeCode() + " AS " + flinkSql;

            rankSqls.put(rankNode.getNodeCode(), flinkSql);
        }
        return rankSqls;
    }

    /**
     * // 获取aggregation节点的flink sql
     *
     * @param aggregationNodes
     * @return
     */
    private LinkedHashMap<String, String> getAggregationSqls(List<GraphNode> aggregationNodes) {
        LinkedHashMap<String, String> aggregationSqls = new LinkedHashMap<>(aggregationNodes.size());

        for (GraphNode aggregationNode : aggregationNodes) {
            String flinkSql = "CREATE VIEW " + aggregationNode.getNodeCode() + " AS SELECT ";

            // 字段sql table.field1,table.field2,field3
            List<GraphOutputField> outputFieldList = aggregationNode.getOutputFieldList();
            String fieldSql = outputFieldList.stream()
                    .map(outputField -> {
                        if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                            return outputField.getCalcFunction() + " AS `" + outputField.getFieldName() + "`";
                        } else if (outputField.getFieldSource() == GraphFieldSourceType.CITE) {
                            if (outputField.getAggFunction() != AggFunction.NONE) {
                                return outputField.getAggFunction() + "(" + outputField.getPreNodeName() + ".`" + outputField.getPreFieldName() + "`) AS `" + outputField.getFieldName() + "`";
                            } else {
                                return outputField.getPreNodeName() + ".`" + outputField.getPreFieldName() + "` AS `" + outputField.getFieldName() + "`";
                            }
                        } else {
                            throw new ServiceException("graph中的节点{" + aggregationNode.getNodeName() + "}出现未知类型{" + outputField.getFieldSource() + "}");
                        }
                    }).collect(Collectors.joining(","));

            // 聚合函数为NONE的全部为group by字段
            String groupByFieldSql = outputFieldList.stream()
                    // 过滤NONE类型的聚合函数
                    .filter(outputField -> {
                        if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                            return false;
                        }
                        if (outputField.getAggFunction() != AggFunction.NONE) {
                            return false;
                        }
                        return true;
                    }).map(outputField -> {
                        /*if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                            return outputField.getFieldName();
                        } else */
                        if (outputField.getFieldSource() == GraphFieldSourceType.CITE) {
                            // return outputField.getPreNodeName() + ".`" + outputField.getPreFieldName() + "`" + " AS `" + outputField.getFieldName() + "`";
                            return outputField.getPreNodeName() + ".`" + outputField.getPreFieldName() + "`";
                        } else {
                            throw new ServiceException("graph中的节点{" + aggregationNode.getNodeName() + "}出现未知类型{" + outputField.getFieldSource() + "}");
                        }
                    }).collect(Collectors.joining(","));

            // 获取前置节点名称
            String preNodeName = aggregationNode.getPreNodes().get(0).getNodeCode();
            flinkSql += fieldSql + " FROM " + preNodeName;

            // 过滤条件
            if (StringUtils.isNotEmpty(aggregationNode.getWhere())) {
                flinkSql += " WHERE " + aggregationNode.getWhere();
            }

            // group by
            if (StringUtils.isNotEmpty(groupByFieldSql)) {
                flinkSql += " GROUP BY " + groupByFieldSql;
            }

            aggregationSqls.put(aggregationNode.getNodeCode(), flinkSql);
        }
        return aggregationSqls;
    }


    /**
     * 获取join节点的flink sql
     *
     * @param joinNodes
     * @return
     */
    private LinkedHashMap<String, String> getJoinSqls(List<GraphNode> joinNodes) {
        LinkedHashMap<String, String> joinSqls = new LinkedHashMap<>(joinNodes.size());

        for (GraphNode joinNode : joinNodes) {
            String flinkSql = "CREATE VIEW " + joinNode.getNodeCode() + " AS ";

            // 前置节点信息
            List<GraphNode> preNodes = joinNode.getPreNodes();

            JoinRelation joinRelation = joinNode.getJoinRelation();
            // 左节点
            GraphNode leftGraphNode = preNodes.stream().filter(preNode -> preNode.getNodeId().equalsIgnoreCase(joinRelation.getLeftNodeId())).collect(Collectors.toList()).get(0);
            // 右节点
            GraphNode rightGraphNode = preNodes.stream().filter(preNode -> preNode.getNodeId().equalsIgnoreCase(joinRelation.getRightNodeId())).collect(Collectors.toList()).get(0);

            // 字段sql table.field1,table.field2,field3
            List<GraphOutputField> outputFieldList = joinNode.getOutputFieldList();
            String fieldSql = outputFieldList.stream().map(outputField -> {
                if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                    return outputField.getCalcFunction() + " AS `" + outputField.getFieldName() + "`";
                    // throw new ServiceException("join中的节点{" + joinNode.getNodeName() + "}没有计算列{" + outputField.getFieldName() + "}");
                } else if (outputField.getFieldSource() == GraphFieldSourceType.CITE) {
                    return outputField.getPreNodeName() + ".`" + outputField.getPreFieldName() + "`" + " AS `" + outputField.getFieldName() + "`";
                } else {
                    throw new ServiceException("graph中的节点{" + joinNode.getNodeName() + "}出现未知类型{" + outputField.getFieldSource() + "}");
                }
            }).collect(Collectors.joining(","));

            String joinSql = "SELECT " + fieldSql + " FROM " + leftGraphNode.getNodeCode() + " " + joinRelation.getJoinType().getDesc() + " " + rightGraphNode.getNodeCode();

            // on条件不能为空
            if (CollectionUtils.isEmpty(joinRelation.getJoinFieldRelations())) {
                throw new ServiceException("graph中的节点{" + joinNode.getNodeName() + "}必须要有一个on条件");
            }
            // 拼接on条件
            joinSql += " ON ";
            joinSql += joinRelation.getJoinFieldRelations().stream().map(joinFieldRelation -> {
                GraphOutputField leftField = leftGraphNode.getOutputFieldList().stream()
                        .filter(leftOutputField -> leftOutputField.getFieldId().equalsIgnoreCase(joinFieldRelation.getLeftFieldId()))
                        .collect(Collectors.toList()).get(0);
                GraphOutputField rightField = rightGraphNode.getOutputFieldList().stream().filter(rightOutputField -> rightOutputField.getFieldId().equalsIgnoreCase(joinFieldRelation.getRightFieldId())).collect(Collectors.toList()).get(0);
                return String.format("`%s`.`%s`",leftGraphNode.getNodeCode(),leftField.getFieldName()) + " = " + String.format("`%s`.`%s`",rightGraphNode.getNodeCode(),rightField.getFieldName());
            }).collect(Collectors.joining(" AND "));

            // 拼接
            flinkSql += joinSql;

            joinSqls.put(joinNode.getNodeCode(), flinkSql);
        }
        return joinSqls;
    }


    /**
     * 获取union节点的flink sql
     *
     * @param unionNodes
     * @return
     */
    private LinkedHashMap<String, String> getUnionSqls(List<GraphNode> unionNodes) {
        LinkedHashMap<String, String> unionSqls = new LinkedHashMap<>(unionNodes.size());

        for (GraphNode unionNode : unionNodes) {
            String flinkSql = "CREATE VIEW " + unionNode.getNodeCode() + " AS ";

            // 前置节点信息
            List<GraphNode> preNodes = unionNode.getPreNodes();
            // TODO:需要验证union的节点是字段，字段顺序，字段类型是否一致

            // 是否去重
            String union = "";
            if (unionNode.getDistinct()) {
                union = " UNION ";
            } else {
                union = " UNION ALL ";
            }

            // 获取所有unionFieldList
            List<UnionField> unionFieldList = new ArrayList<>();
            for (GraphOutputField outputField : unionNode.getOutputFieldList()) {
                List<UnionField> outputUnionFieldList = outputField.getUnionFieldList();
                // 将union后的类型赋值给里面union的字段
                outputUnionFieldList = outputUnionFieldList.stream()
                        .map(outputUnionField -> {
                            outputUnionField.setFieldType(outputField.getFieldType());
                            return outputUnionField;
                        }).collect(Collectors.toList());
                unionFieldList.addAll(outputUnionFieldList);
            }

            // 拼接union sql
            String unionSql = preNodes.stream().map(preNode -> {
                // 获取union field sql
                String unionFieldSql = unionFieldList.stream()
                        // 获取不同来源节点的union sql
                        .filter(unionField -> preNode.getNodeId().equalsIgnoreCase(unionField.getCiteNodeId()))
                        .map(unionField -> {
                            switch (unionField.getFieldSource()) {
                                case CALC:
                                    return FlinkFieldRefUtils.convertIntegerType(unionField.getFieldName()) + " AS `" + unionField.getOutputFieldName() + "`";
                                case NULL:
                                    // return "null AS `" + unionField.getOutputFieldName() + "`";
                                    return "cast(null AS " + FlinkFieldRefUtils.convertField(unionField.getFieldType()) + ") AS `" + unionField.getOutputFieldName() + "`";
                                case CITE:
                                    return unionField.getPreNodeName() + ".`" + unionField.getPreFieldName() + "` AS `" + unionField.getOutputFieldName() + "`";
                                default:
                                    throw new ServiceException("union中的节点{" + unionNode.getNodeName() + "}出现未知类型{" + unionField.getFieldSource() + "}");
                            }
                        }).collect(Collectors.joining(","));
                return "SELECT " + unionFieldSql + " FROM " + preNode.getNodeCode();
            }).collect(Collectors.joining(union));

            // 拼接
            flinkSql += unionSql;

            unionSqls.put(unionNode.getNodeCode(), flinkSql);
        }
        return unionSqls;
    }


    /**
     * projection是一进
     * 获取projection节点的flink sql
     *
     * @param projectionNodes
     * @return
     */
    private LinkedHashMap<String, String> getProjectionSqls(List<GraphNode> projectionNodes) {
        LinkedHashMap<String, String> projectionSqls = new LinkedHashMap<>(projectionNodes.size());

        for (GraphNode projectionNode : projectionNodes) {
            // 获取前置节点名称
            GraphNode preGraphNode = projectionNode.getPreNodes().get(0);

            // 投影节点的来源可能是从iceberg来的
            String flinkSql = "CREATE VIEW " + projectionNode.getNodeCode() + " AS SELECT ";

            // 字段sql table.field1,table.field2,field3
            List<GraphOutputField> outputFieldList = projectionNode.getOutputFieldList();
            String fieldSql = outputFieldList.stream().map(outputField -> {
                if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                    return outputField.getCalcFunction() + " AS `" + outputField.getFieldName() + "`";
                } else if (outputField.getFieldSource() == GraphFieldSourceType.CITE) {
                    return outputField.getPreNodeName() + ".`" + outputField.getPreFieldName() + "`" + " AS `" + outputField.getFieldName() + "`";
                } else {
                    throw new ServiceException("graph中的节点{" + projectionNode.getNodeName() + "}出现未知类型{" + outputField.getFieldSource() + "}");
                }
            }).collect(Collectors.joining(","));


            flinkSql += fieldSql + " FROM " + preGraphNode.getNodeCode();

            // 过滤条件
            if (StringUtils.isNotEmpty(projectionNode.getWhere())) {
                flinkSql += " WHERE " + projectionNode.getWhere();
            }
            // 批/流
            flinkSql = FlinkSqlUtils.getBatchOrStreamSql(flinkSql, projectionNode.getRuntimeMode());
            projectionSqls.put(projectionNode.getNodeCode(), flinkSql);
        }
        return projectionSqls;
    }

    /**
     * 检查GraphJson
     *
     * @param transformNode
     * @return
     */
    public GraphJson checkEtlGraphJson(EtlNode transformNode) {
        // TODO:检查节点
        // transform节点数据
        EtlNodeData nodeData = transformNode.getNodeData();

        // 批/流
        GraphJson graph = nodeData.getGraph();

        List<NodeRelation> graphNodeRelation = graph.getGraphNodeRelation();

        List<GraphNode> graphNodes = graph.getGraphNodes();
        for (GraphNode graphNode : graphNodes) {
            // 设置批/流,默认批
            if (graph.getRuntimeMode() != RuntimeMode.NONE) {
                graphNode.setRuntimeMode(graph.getRuntimeMode() == null ? RuntimeMode.BATCH : graph.getRuntimeMode());
            }
            // 源节点以外的GraphJson中的节点都需要加上前缀，防止和EtlJson中的节点的名称重复
            if (graphNode.getGraphNodeType() != GraphNodeType.SOURCE) {
                graphNode.setNodeCode(transformNode.getNodeCode() + "_" + graphNode.getNodeCode());
            }
            // 跳过在EtlJson中添加的源节点
            if (graphNode.getGraphNodeType() == GraphNodeType.SOURCE) {
                // 检查节点
                checkEtlGraphNode(graphNode);
                continue;
            }
            buildGraphNode(transformNode, graphNodeRelation, graphNodes, graphNode);
        }
        graph.setGraphNodes(graphNodes);
        return graph;
    }

    private void checkEtlGraphNode(GraphNode graphNode) {
        switch (graphNode.getGraphNodeType()) {
            case SOURCE:
                if (graphNode.getSourceType() == EtlSourceType.DATASOURCE) {
                    checkSourceEtlGraphNodeData(graphNode);
                } else if (graphNode.getSourceType() == EtlSourceType.DATAENTITY) {
                    checkEntityEtlGraphNodeData(graphNode);
                }
                break;
            case PROJECTION:
            case UNION:
            case JOIN:
            case AGGREGATION:
            case RANK:
                break;
            default:
                throw new ServiceException(Status.UNKNOWN_NODE_TYPE);
        }
    }

    /**
     * 检查数据来源是数据实体类型的节点数据
     *
     * @param sourceGraphNode
     */
    private void checkEntityEtlGraphNodeData(GraphNode sourceGraphNode) {
        // 获取数据实体信息
        EtlDataEntity dataEntity = sourceGraphNode.getDataEntity();
        Long dataEntityId = dataEntity.getDataEntityId();
        // 默认不是脏表
        if (dataEntity.getIsDirtyTable() == null) {
            dataEntity.setIsDirtyTable(false);
        }

        // // 获取数据实体关联表信息
        // R<DataEntityTable> dataEntityTableResult = remoteEntityTableService.selectById(dataEntityId);
        // if (dataEntityTableResult.getCode() != Status.SUCCESS.getCode()) {
        //     throw new ServiceException(dataEntityTableResult.getMsg());
        // }
        // DataEntityTable dataEntityTable = dataEntityTableResult.getData();

        // 数据实体关联表的数据源信息
        // R<DataSource> queryDataSourceResult = remoteDriveService.queryDataSource(dataEntityTable.getDataSourceId());
        // if (queryDataSourceResult.getCode() != Status.SUCCESS.getCode()) {
        //     throw new ServiceException(queryDataSourceResult.getMsg());
        // }
        // // 获取source数据源
        // DataSource dataSource = queryDataSourceResult.getData();
        // if (!"iceberg".equalsIgnoreCase(dataSource.getType())) {
        //     return;
        // }

        // TODO: iceberg类型需要改变节点code,节点code需要拼接库名和表名: iceberg.web.logs,给后续的flinksql使用: iceberg.web.logs
        // sourceGraphNode.setNodeCode(sourceGraphNode.getNodeCode() + "." + dataEntityTable.getDatabaseName() + "." + dataEntityTable.getTableName());
    }

    /**
     * 检查数据来源是数据源类型的节点数据
     *
     * @param sourceGraphNode
     */
    private void checkSourceEtlGraphNodeData(GraphNode sourceGraphNode) {
        // 获取source数据源
        EtlDataSource etlDataSource = sourceGraphNode.getDataSource();

        if (etlDataSource == null) {
            throw new ServiceException(Status.DATA_SOURCE_INFORMATION_CANNOT_BE_EMPTY);
        }
        if (etlDataSource.getDataSourceId() == null) {
            throw new ServiceException(Status.DATA_SOURCE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isEmpty(etlDataSource.getDatabaseName())) {
            throw new ServiceException(Status.DATABASE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isEmpty(etlDataSource.getTableName())) {
            throw new ServiceException(Status.TABLE_CANNOT_BE_EMPTY);
        }
        // R<DataSource> queryDataSourceResult = remoteDriveService.queryDataSource(etlDataSource.getDataSourceId());
        // if (queryDataSourceResult.getCode() != Status.SUCCESS.getCode()) {
        //     throw new ServiceException(queryDataSourceResult.getMsg());
        // }
        // DataSource dataSource = queryDataSourceResult.getData();
        // if (!"iceberg".equalsIgnoreCase(dataSource.getType())) {
        //     return;
        // }

        // TODO: iceberg类型需要改变节点code,节点code需要拼接库名和表名: iceberg.web.logs,给后续的flinksql使用: iceberg.web.logs
        // sourceGraphNode.setNodeCode(sourceGraphNode.getNodeCode() + "." + etlDataSource.getDatabaseName() + "." + etlDataSource.getTableName());
    }

    /**
     * 解析节点
     *
     * @param nodeRelation
     * @param nodes
     * @param currentNode
     */
    private void buildGraphNode(EtlNode transformNode, List<NodeRelation> nodeRelation, List<GraphNode> nodes, GraphNode currentNode) {
        // 获取当前节点的前置节点
        boolean isPreNode = nodeRelation.stream().anyMatch(relation -> relation.getNodeId().equalsIgnoreCase(currentNode.getNodeId()));
        if (!isPreNode) {
            throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}没有前置节点");
        }
        List<String> preNodeIds = nodeRelation.stream()
                .filter(relation -> relation.getNodeId().equalsIgnoreCase(currentNode.getNodeId()))
                .map(NodeRelation::getPreNodeIds)
                .collect(Collectors.toList()).get(0);
        if (CollectionUtils.isEmpty(preNodeIds)) {
            throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}没有前置节点");
        }

        // 获取前置节点的具体信息，当前节点的前置节点一定是字段中的前置节点
        List<GraphNode> preEtlNodes = nodes.stream().map(node -> {
            for (String preNodeId : preNodeIds) {
                if (preNodeId.equalsIgnoreCase(node.getNodeId())) {
                    return node;
                }
            }
            return null;
        }).filter(node -> node != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(preEtlNodes)) {
            throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}的前置节点不存在,请检查当前节点的前置节点是否在图中");
        }

        // 设置前置节点名称
        currentNode.setPreNodeNameMap(preEtlNodes.stream().collect(Collectors.toMap(GraphNode::getNodeId, GraphNode::getNodeCode)));

        // 设置前置节点
        currentNode.setPreNodes(preEtlNodes);

        // TODO:待优化，存在公共的build
        switch (currentNode.getGraphNodeType()) {
            case PROJECTION:
            case JOIN:
            case AGGREGATION:
                getGraphNodeField(transformNode, currentNode, preEtlNodes);
                break;
            case RANK:
                getGraphRankNodeField(transformNode, currentNode, preEtlNodes);
                break;
            case UNION:
                getGraphUnionNodeField(transformNode, currentNode, preEtlNodes);
                break;
        }

    }

    /**
     * 解析GraphJson中节点的字段
     *
     * @param currentNode
     * @param preEtlNodes
     */
    private void getGraphNodeField(EtlNode transformNode, GraphNode currentNode, List<GraphNode> preEtlNodes) {
        List<GraphOutputField> outputFieldList = currentNode.getOutputFieldList();
        for (GraphOutputField outputField : outputFieldList) {

            // 计算列没有前置节点
            if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                // 如果是投影节点
                if (currentNode.getGraphNodeType() == GraphNodeType.PROJECTION) {
                    GraphNode preNode = currentNode.getPreNodes().get(0);
                    // 如果前置节点是源节点
                    if (preNode.getGraphNodeType() == GraphNodeType.SOURCE) {
                        // 如果字段是计算列
                        if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                            // 替换计算列中的值
                            String calcFunction = outputField.getCalcFunction();
                            outputField.setCalcFunction(calcFunction.replaceAll(transformNode.getNodeCode() + "_" + preNode.getNodeCode(), preNode.getNodeCode()));
                        }
                    }
                }
                continue;
            }
            // 检查节点中的字段的来源节点是否存在
            boolean isFieldPreNode = preEtlNodes.stream().anyMatch(preEtlNode -> preEtlNode.getNodeId().equalsIgnoreCase(outputField.getCiteNodeId()));
            if (!isFieldPreNode) {
                throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形节点{" + currentNode.getNodeName() + "}中的字段{" + outputField.getFieldName() + "}的来源节点的不存在，请检查");
            }
            // 获取字段的前置节点
            GraphNode preGraphNode = preEtlNodes.stream()
                    .filter(preEtlNode -> preEtlNode.getNodeId().equalsIgnoreCase(outputField.getCiteNodeId()))
                    .collect(Collectors.toList()).get(0);
            // 设置字段前置节点的名称
            outputField.setPreNodeName(preGraphNode.getNodeCode());

            List<GraphOutputField> preOutputFieldList = preGraphNode.getOutputFieldList();
            // 检查字段的前置节点的字段是否存在
            boolean isPreOutputField = preOutputFieldList.stream().anyMatch(preOutputField -> preOutputField.getFieldId().equalsIgnoreCase(outputField.getCiteFieldId()));
            if (!isPreOutputField) {
                throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形节点{" + currentNode.getNodeName() + "}中的字段{" + outputField.getFieldName() + "}的来源节点的字段不存在，请检查");
            }
            // 获取字段的前置节点的字段的信息
            GraphOutputField preOutputField = preOutputFieldList.stream().filter(preNodeField -> preNodeField.getFieldId().equalsIgnoreCase(outputField.getCiteFieldId())).collect(Collectors.toList()).get(0);
            // 设置字段前置节点中的字段的名称
            outputField.setPreFieldName(preOutputField.getFieldName());
        }
        currentNode.setOutputFieldList(outputFieldList);
    }

    private void getGraphRankNodeField(EtlNode transformNode, GraphNode currentNode, List<GraphNode> preEtlNodes) {
        List<GraphOutputField> outputFieldList = currentNode.getOutputFieldList();
        for (GraphOutputField outputField : outputFieldList) {
            // 计算列没有前置节点
            if (outputField.getFieldSource() == GraphFieldSourceType.CALC) {
                continue;
            }
            // 检查节点中的字段的来源节点是否存在
            boolean isFieldPreNode = preEtlNodes.stream().anyMatch(preEtlNode -> preEtlNode.getNodeId().equalsIgnoreCase(outputField.getCiteNodeId()));
            if (!isFieldPreNode) {
                throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}中的字段{" + outputField.getFieldName() + "}的来源节点的不存在，请检查");
            }
            // 获取字段的前置节点
            GraphNode preGraphNode = preEtlNodes.stream()
                    .filter(preEtlNode -> preEtlNode.getNodeId().equalsIgnoreCase(outputField.getCiteNodeId()))
                    .collect(Collectors.toList()).get(0);
            // 设置字段前置节点的名称
            outputField.setPreNodeName(preGraphNode.getNodeCode());

            List<GraphOutputField> preOutputFieldList = preGraphNode.getOutputFieldList();
            // 检查字段的前置节点的字段是否存在
            boolean isPreOutputField = preOutputFieldList.stream().anyMatch(preOutputField -> preOutputField.getFieldId().equalsIgnoreCase(outputField.getCiteFieldId()));
            if (!isPreOutputField) {
                throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}中的字段{" + outputField.getFieldName() + "}的来源节点的字段不存在，请检查");
            }
            // 获取字段的前置节点的字段的信息
            GraphOutputField preOutputField = preOutputFieldList.stream().filter(preNodeField -> preNodeField.getFieldId().equalsIgnoreCase(outputField.getCiteFieldId())).collect(Collectors.toList()).get(0);

            // 设置字段前置节点中的字段的名称
            outputField.setPreFieldName(preOutputField.getFieldName());
        }

        List<GraphOutputField> rankGraphOutputFieldList = outputFieldList.stream()
                .filter(outputField -> outputField.getFieldSource() == GraphFieldSourceType.CALC && outputField.getRank().getLimitType() != null && outputField.getRank().getLimitType() != LimitType.ALL)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rankGraphOutputFieldList) && rankGraphOutputFieldList.size() > 1) {
            throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}中的RANK计算列中只能存在一个限值，请检查");
        }

        currentNode.setOutputFieldList(outputFieldList);
    }

    private void getGraphUnionNodeField(EtlNode transformNode, GraphNode currentNode, List<GraphNode> preEtlNodes) {
        List<GraphOutputField> outputFieldList = currentNode.getOutputFieldList();
        for (GraphOutputField outputField : outputFieldList) {

            // 获取union field list
            List<UnionField> unionFieldList = outputField.getUnionFieldList();
            for (UnionField unionField : unionFieldList) {
                // union中的每个字段都有前置节点
                // 检查union节点的来源节点是否存在
                boolean isFieldPreNode = preEtlNodes.stream().anyMatch(preEtlNode -> preEtlNode.getNodeId().equalsIgnoreCase(unionField.getCiteNodeId()));
                if (!isFieldPreNode) {
                    throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}中union字段{" + outputField.getFieldName() + "}的来源节点的不存在，请检查");
                }

                // 获取字段的前置节点
                GraphNode preGraphNode = preEtlNodes.stream()
                        .filter(preEtlNode -> preEtlNode.getNodeId().equalsIgnoreCase(unionField.getCiteNodeId()))
                        .collect(Collectors.toList()).get(0);
                // 设置字段前置节点的名称
                unionField.setPreNodeName(preGraphNode.getNodeCode());

                switch (unionField.getFieldSource()) {
                    // 计算列没有前置节点的字段
                    case CALC:
                    case NULL:
                        break;
                    case CITE:
                        List<GraphOutputField> preOutputFieldList = preGraphNode.getOutputFieldList();
                        // 检查字段的前置节点的字段是否存在
                        boolean isPreOutputField = preOutputFieldList.stream().anyMatch(preOutputField -> preOutputField.getFieldId().equalsIgnoreCase(unionField.getCiteFieldId()));
                        if (!isPreOutputField) {
                            throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}中的字段{" + outputField.getFieldName() + "}的来源节点的字段不存在，请检查");
                        }
                        // 获取字段的前置节点的字段的信息
                        GraphOutputField preOutputField = preOutputFieldList.stream().filter(preNodeField -> preNodeField.getFieldId().equalsIgnoreCase(unionField.getCiteFieldId())).collect(Collectors.toList()).get(0);
                        // 设置字段前置节点中的字段的名称
                        unionField.setPreFieldName(preOutputField.getFieldName());
                        break;
                    default:
                        throw new ServiceException("{" + transformNode.getNodeName() + "}过程图形中的节点{" + currentNode.getNodeName() + "}中的union字段{" + outputField.getFieldName() + "}出现未知类型{" + unionField.getFieldSource() + "}");
                }

                // 设置最终输出的字段名称
                unionField.setOutputFieldName(outputField.getFieldName());
            }
            outputField.setUnionFieldList(unionFieldList);
        }
        currentNode.setOutputFieldList(outputFieldList);
    }


}
