package com.datalink.fdop.drive.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.ExportJSONFileUtil;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datasource.annotation.SwitchDataSource;
import com.datalink.fdop.common.datasource.dynamic.DataSourceHolder;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.RemoteHive2Service;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.api.domain.FileSystem;
import com.datalink.fdop.drive.api.domain.dto.*;
import com.datalink.fdop.drive.api.plugin.*;
import com.datalink.fdop.drive.mapper.CustomSqlMapper;
import com.datalink.fdop.drive.mapper.DataSourceMapper;
import com.datalink.fdop.drive.mapper.JmReportSourceMapper;
import com.datalink.fdop.drive.provider.DataSourceProvider;
import com.datalink.fdop.drive.service.IDataSourceService;
import com.datalink.fdop.drive.service.IFieldRelationService;
import com.datalink.fdop.drive.utils.SourceInfoUtils;
import com.gitee.starblues.core.PluginInfo;
import com.gitee.starblues.core.PluginState;
import com.gitee.starblues.integration.operator.PluginOperator;
import com.gitee.starblues.integration.user.PluginUser;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/6 13:43
 */
@Service
public class DataSourceServiceImpl implements IDataSourceService {

    private static final Logger logger = LoggerFactory.getLogger(DataSourceServiceImpl.class);

    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Autowired
    private IFieldRelationService fieldRelationService;

    @Autowired
    private PluginUser pluginUser;

    @Autowired
    private PluginOperator pluginOperator;

    @Autowired
    private CustomSqlMapper customSqlMapper;

    @Autowired
    private JmReportSourceMapper jmReportSourceMapper;

    @Autowired
    private RemoteHive2Service remoteHive2Service;

    /**
     * 判断读写权限
     *
     * @param dataSource
     */
    private void checkIsRead(DataSource dataSource) {
        if (dataSource.getIsRead() != null && dataSource.getIsRead()) {
            throw new ServiceException(MessageFormat.format(Status.THE_DATA_SOURCE_HAS_ONLY_READ_PERMISSIONS.getMsg(), dataSource.getCode()));
        }
    }

    /**
     * 获取公共的bena
     *
     * @param pluginId
     * @return
     */
    private CommonService getCommonService(String pluginId) {
        List<CommonService> CommonServiceList = pluginUser.getBeanByInterface(pluginId, CommonService.class);

        if (CollectionUtils.isEmpty(CommonServiceList)) {
            throw new ServiceException(Status.PLUGIN_DOES_NOT_EXIST);
        }
        return CommonServiceList.get(0);
    }

    /**
     * 根据插件id获取插件bean
     *
     * @param pluginId
     * @return
     */
    private JdbcCommonService getJdbcCommonService(String pluginId) {
        List<JdbcCommonService> jdbcCommonServiceList = pluginUser.getBeanByInterface(pluginId, JdbcCommonService.class);
        if (CollectionUtils.isEmpty(jdbcCommonServiceList)) {
            throw new ServiceException(Status.PLUGIN_DOES_NOT_EXIST);
        }
        return jdbcCommonServiceList.get(0);
    }

    /**
     * 获取iceberg的bean
     *
     * @param pluginId
     * @return
     */
    private BigDataCommonService getBigDataCommonService(String pluginId) {
        List<BigDataCommonService> bigDataCommonServiceList = pluginUser.getBeanByInterface(pluginId, BigDataCommonService.class);
        if (CollectionUtils.isEmpty(bigDataCommonServiceList)) {
            throw new ServiceException(Status.PLUGIN_DOES_NOT_EXIST);
        }
        return bigDataCommonServiceList.get(0);
    }

    private FileCommonService getFileCommonService(String pluginId) {
        List<FileCommonService> fileCommonServiceList = pluginUser.getBeanByInterface(pluginId, FileCommonService.class);
        if (CollectionUtils.isEmpty(fileCommonServiceList)) {
            throw new ServiceException(Status.PLUGIN_DOES_NOT_EXIST);
        }
        return fileCommonServiceList.get(0);
    }

    /**
     * 获取消息队列的bean
     *
     * @param pluginId
     * @return
     */
    private MessageQueueCommonService getMessageQueueCommonService(String pluginId) {
        List<MessageQueueCommonService> messageQueueCommonServiceList = pluginUser.getBeanByInterface(pluginId, MessageQueueCommonService.class);
        if (CollectionUtils.isEmpty(messageQueueCommonServiceList)) {
            throw new ServiceException(Status.PLUGIN_DOES_NOT_EXIST);
        }
        return messageQueueCommonServiceList.get(0);
    }

    /**
     * 获取 JdbcTemplate
     *
     * @param commonService
     * @param dataSourceBasicInfo
     * @return
     */
    private JdbcTemplate getJdbcTemplate(CommonService commonService, DataSourceBasicInfo dataSourceBasicInfo) {
        // 获取插件中自定义的DataSourceBasicInfo
        dataSourceBasicInfo = commonService.getDataSourceBasicInfo(dataSourceBasicInfo);
        // new JdbcTemplate
        return new JdbcTemplate(DataSourceProvider.getInstance().getDataSource(dataSourceBasicInfo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createDataSource(DataSource dataSource) {
        int insert = 0;
        try {
            // check code can use or not
            if (checkCode(dataSource.getCode())) {
                throw new ServiceException(Status.DATASOURCE_EXIST);
            }
            // 导入时可以指定数据源id
            Long dataSourceId = dataSource.getId();
            if (dataSourceId == null || dataSourceId <= 0L) {
                dataSourceId = IdWorker.getId();
                dataSource.setId(dataSourceId);
            }
            DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
            if (dataSourceBasicInfo != null) {
                // 获取插件中对数据源信息修饰后的对象
                CommonService commonService = getCommonService(dataSourceBasicInfo.getPluginId());
                DataSourceBasicInfo dataSourceBasicInfo1 = commonService.getDataSourceBasicInfo(dataSourceBasicInfo);
                dataSourceBasicInfo1.setDataSourceId(dataSourceId);
                dataSource.setDataSourceBasicInfo(JSONObject.toJSONString(dataSourceBasicInfo1));
            }
            // check connect
            // Boolean isConnection = checkConnection(dataSourceBasicInfo);
            // if (!isConnection) {
            //     throw new ServiceException(Status.CONNECTION_TEST_FAILURE);
            // }
            dataSource.setCreateBy(SecurityUtils.getUsername());
            dataSource.setCreateTime(new Date());
            insert = dataSourceMapper.insertDataSourceGraph(dataSource);

            // 创建元素边关系
            if (insert > 0 && dataSource.getPid() != -1L) {
                // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
                dataSourceMapper.createElementAndMenuEdge(dataSource.getPid(), Arrays.asList(dataSource.getId()));
            }
            // 添加成功则添加数据源
//        if (insert > 0 && !"iceberg".equalsIgnoreCase(dataSource.getType())) {
//            DataSourceProvider.getInstance().remoteDataSource(dataSource.getId());
//            DataSourceProvider.getInstance().getDataSource(dataSourceBasicInfo1);
//            try {
//                // 添加积木报表的数据源
//                jmReportSourceMapper.insert(JmReportSourceUtils.getJmReportDataSource(dataSource));
//            } catch (RuntimeException e) {
//                logger.error("未初始化积木报表");
//            }
//        }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return insert;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public int updateDataSource(DataSource dataSource) {
        VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectById(dataSource.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }

        DataSource query = vlabelItem.getProperties();

        //check name can use or not
        if (!query.getCode().trim().equals(query.getCode()) && checkCode(query.getCode())) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }

        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        if (dataSourceBasicInfo != null) {
            // 获取插件中对数据源信息修饰后的对象
            CommonService commonService = getCommonService(dataSourceBasicInfo.getPluginId());
            DataSourceBasicInfo dataSourceBasicInfo1 = commonService.getDataSourceBasicInfo(dataSourceBasicInfo);
            dataSourceBasicInfo1.setDataSourceId(dataSource.getId());
            dataSource.setDataSourceBasicInfo(JSONObject.toJSONString(dataSourceBasicInfo1));
        }
        // Boolean isConnection = checkConnection(dataSourceBasicInfo);
        // if (!isConnection) {
        //     throw new ServiceException(Status.CONNECTION_TEST_FAILURE);
        // }
        dataSource.setUpdateBy(SecurityUtils.getUsername());
        dataSource.setUpdateTime(new Date());
        int update = dataSourceMapper.updateDataSourceGraph(dataSource);

        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            dataSourceMapper.deleteElementAndMenuEdge(Arrays.asList(dataSource.getId()), dataSource.getPid());
            if (dataSource.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                dataSourceMapper.createElementAndMenuEdge(dataSource.getPid(), Arrays.asList(dataSource.getId()));
            }
        }
        // 添加成功则添加数据源
//        if (update > 0 && !"iceberg".equalsIgnoreCase(dataSource.getType())) {
//            DataSourceProvider.getInstance().remoteDataSource(dataSource.getId());
//            DataSourceProvider.getInstance().getDataSource(dataSourceBasicInfo1);
//            try {
//                // 修改积木报表的数据源
//                jmReportSourceMapper.updateById(JmReportSourceUtils.getJmReportDataSource(dataSource));
//            } catch (Exception e) {
//                logger.error("未初始化积木报表");
//            }
//        }
        return update;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public int deleteDataSource(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<DataSource> dataSourceVlabelItem = dataSourceMapper.selectById(id);
            if (dataSourceVlabelItem == null) {
                throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
            }
            DataSource dataSource = dataSourceVlabelItem.getProperties();

            // 删除数据源时先校验是否被其他实体或其他什么对象引用
            List<String> entityCodeList = dataSourceMapper.selectSourceEntityEdge(id);
            if (CollectionUtils.isNotEmpty(entityCodeList)) {
                throw new ServiceException("数据源[" + dataSource.getCode() + "]被实体[" + entityCodeList.stream().collect(Collectors.joining(",")) + "]引用");
            }
            DataSourceProvider.getInstance().remoteDataSource(id);
        }
        int delete = dataSourceMapper.deleteDataSourceGraph(ids);
        // try {
        //     // 删除积木报表的数据源
        //     jmReportSourceMapper.deleteBatchIds(ids.stream().map(id -> id.toString()).collect(Collectors.toList()));
        // } catch (Exception e) {
        //     logger.error("未初始化积木报表");
        // }
        return delete;
    }

    @Override
    public DataSource queryDataSource(Long id) {
        VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public DataSource queryDataSourceByTenantId(Long tenantId, Long id) {
        // 强制切换数据源查询指定租户数据源
        DataSourceHolder.setDataSourceKey(Constants.FORCE_SWITCH_TENANT_PREFIX + tenantId);
        VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }
        // 查询结果后还原
        DataSourceHolder.setDataSourceKey(null);
        return vlabelItem.getProperties();
    }

    @Override
    public DataSource queryDataSourceByTenantIdAndCode(Long tenantId, String code) {
        // 强制切换数据源查询指定租户数据源
        DataSourceHolder.setDataSourceKey(Constants.FORCE_SWITCH_TENANT_PREFIX + tenantId);
        VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectByCode(code);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }
        // 查询结果后还原
        DataSourceHolder.setDataSourceKey(null);
        return vlabelItem.getProperties();
    }

    @Override
    public DataSource queryDataSource(String code) {
        VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectByCode(code);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public List<String> getAllDataSourceType() {
        List<PluginInfo> pluginInfoList = pluginOperator.getPluginInfo();
        if (CollectionUtils.isEmpty(pluginInfoList)) {
            throw new ServiceException("没有正在运行的插件");
        }

        List<String> dbTypeList = pluginInfoList.stream()
                // 获得启动成功的插件
                .filter(pluginInfo -> pluginInfo.getPluginState() == PluginState.STARTED)
                // 获取所有插件对应的dbType
                .map(pluginInfo -> {
                    CommonService commonService = getCommonService(pluginInfo.getPluginId());
                    return commonService.getDbType();
                }).collect(Collectors.toList());
        return dbTypeList;
    }

    @Override
    public PageDataInfo<DataSource> queryDataSourceList(DataSource dataSource, String sort) {
        // 获取分页参数
        Page<VlabelItem> page = PageUtils.getPage(VlabelItem.class);
        IPage<DataSource> dataSourceIPage = dataSourceMapper.selectList(page, dataSource, sort);

        return PageUtils.getPageInfo(dataSourceIPage.getRecords(), (int) dataSourceIPage.getTotal());
    }

    @Override
    public PageDataInfo<DataSource> queryUnStructuredAll(DataSource dataSource, String sort) {
        // 获取分页参数
        Page<VlabelItem> page = PageUtils.getPage(VlabelItem.class);
        IPage<DataSource> dataSourceIPage = dataSourceMapper.selectUnStructuredList(page, dataSource, sort);

        return PageUtils.getPageInfo(dataSourceIPage.getRecords(), (int) dataSourceIPage.getTotal());
    }

    @Override
    public List<DataSource> selectAll(String sort) {
        Page<VlabelItem> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<DataSource> dataSourceIPage = dataSourceMapper.selectList(page, new DataSource(), sort);
        return dataSourceIPage.getRecords();
    }

    @Override
    public Boolean checkConnection(DataSourceBasicInfo dataSourceBasicInfo) {
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            // 大数据文件类型插件单独处理
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            try {
                return bigDataCommonService.connect(bigDataCommonService.getDataSourceBasicInfo(dataSourceBasicInfo));
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
        } else if (isFileSystem(dataSourceBasicInfo.getType())) {
            // 服务器文件系统sftp/ftp
            FileCommonService fileCommonService = getFileCommonService(dataSourceBasicInfo.getPluginId());
            return fileCommonService.connect(dataSourceBasicInfo);
        } else if (isMessageQueue(dataSourceBasicInfo.getType())) {
            // 消息队列类型数据源kafka等
            MessageQueueCommonService messageQueueCommonService = getMessageQueueCommonService(dataSourceBasicInfo.getPluginId());
            try {
                return messageQueueCommonService.connect(messageQueueCommonService.getDataSourceBasicInfo(dataSourceBasicInfo));
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            // hive2 rest api
            R<Boolean> r = remoteHive2Service.connect(dataSourceBasicInfo);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            return r.getData();
        }

        JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
        // 测试的数据源给它生成一个雪花id
        long testId = IdWorker.getId();
        dataSourceBasicInfo.setDataSourceId(testId);

        try {
            return jdbcCommonService.connect(getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo));
        } catch (Exception e) {
            logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
            throw new ServiceException(e.getMessage());
        } finally {
            DataSourceProvider.getInstance().remoteDataSource(testId);
        }
    }

    @Override
    public String getDbType(String pluginId) {
        CommonService commonService = getCommonService(pluginId);
        return commonService.getDbType();
    }

    @Override
    public String getDbVersion(String pluginId) {
        CommonService commonService = getCommonService(pluginId);
        return commonService.getVersion();
    }

    private boolean checkCode(String code) {
        if (StringUtils.isEmpty(code)) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }
        VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectByCode(code.trim());
        return vlabelItem != null;
    }

    private DataSource checkDataSource(Long id) {
        VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }


    @Override
    public PageDataInfo getDatabases(Long id, String databaseName, Integer pageNo, Integer pageSize) {
        PageDataInfo pageDataInfo = new PageDataInfo(pageNo, pageSize);

        // 查询结果
        Map<String, Object> result = new HashMap<>();

        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);


        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            result = bigDataCommonService.getDataBases(dataSourceBasicInfo, databaseName, pageNo, pageSize);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, pageNo, pageSize);
            R<Map<String, Object>> r = remoteHive2Service.getDataBases(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            result = r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            result = jdbcCommonService.getDatabases(jdbcTemplate, databaseName, pageNo, pageSize);
        }

        pageDataInfo.setTotal(MapUtils.getInteger(result, "total"));
        pageDataInfo.setTotalList((List) MapUtils.getObject(result, "data"));
        return pageDataInfo;
    }

    @Override
    public List<String> getSchemasDatabases(Long id) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
        if ("postgresql".equalsIgnoreCase(dataSourceBasicInfo.getType()) || "sqlserver".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            return jdbcCommonService.getSchemasDatabases(jdbcTemplate);
        } else {
            throw new ServiceException("不支持除postgresql、sqlserver以外的数据源");
        }
    }

    @Override
    public PageDataInfo getSchemasByDatabase(Long id, String dbName, String databaseName, Integer pageNo, Integer pageSize) {
        PageDataInfo pageDataInfo = new PageDataInfo(pageNo, pageSize);
        // 查询结果
        Map<String, Object> result = new HashMap<>();


        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        String type = dataSource.getType();
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, type, dbName);
        // 数据源连接信息

        dataSourceBasicInfo.setDataSourceId(id);
        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            result = bigDataCommonService.getSchemasByDatabase(dataSourceBasicInfo, databaseName, pageNo, pageSize);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, pageNo, pageSize);
            R<Map<String, Object>> r = remoteHive2Service.getSchemasByDatabase(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            result = r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            long testId = IdWorker.getId();
            dataSourceBasicInfo.setDataSourceId(testId);
            // 获取JdbcTemplate
            try {
                JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
                result = jdbcCommonService.getSchemasByDatabase(jdbcTemplate, dbName, databaseName, pageNo, pageSize);
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
                throw new ServiceException(e.getMessage());
            } finally {
                DataSourceProvider.getInstance().remoteDataSource(testId);
            }

        }

        pageDataInfo.setTotal(MapUtils.getInteger(result, "total"));
        pageDataInfo.setTotalList((List) MapUtils.getObject(result, "data"));
        return pageDataInfo;
    }

    @Override
    public PageDataInfo getSchemas(Long id, String databaseName, Integer pageNo, Integer pageSize) {
        PageDataInfo pageDataInfo = new PageDataInfo(pageNo, pageSize);

        // 查询结果
        Map<String, Object> result = new HashMap<>();

        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            // 文件类型插件单独处理
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            result = bigDataCommonService.getDataBases(dataSourceBasicInfo, databaseName, pageNo, pageSize);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, pageNo, pageSize);
            R<Map<String, Object>> r = remoteHive2Service.getDataBases(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            result = r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            result = jdbcCommonService.getSchemaName(jdbcTemplate, databaseName, pageNo, pageSize);
        }

        pageDataInfo.setTotal(MapUtils.getInteger(result, "total"));
        pageDataInfo.setTotalList((List) MapUtils.getObject(result, "data"));
        return pageDataInfo;
    }

    @Override
    public PageDataInfo getTables(Long id, String dbName, String tableName, Integer pageNo, Integer pageSize, String databaseName) {
        PageDataInfo pageDataInfo = new PageDataInfo(pageNo, pageSize);

        // 查询结果
        Map<String, Object> result = new HashMap<>();

        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, dataSourceBasicInfo.getType(), dbName);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            result = bigDataCommonService.getTables(dataSourceBasicInfo, databaseName, tableName, pageNo, pageSize);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName, pageNo, pageSize);
            R<Map<String, Object>> r = remoteHive2Service.getTables(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            result = r.getData();
        } else if (isMessageQueue(dataSourceBasicInfo.getType())) {
            // 消息队列类型数据源kafka等
            MessageQueueCommonService messageQueueCommonService = getMessageQueueCommonService(dataSourceBasicInfo.getPluginId());
            result = messageQueueCommonService.getTopics(dataSourceBasicInfo, tableName, pageNo, pageSize);
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());

            long testId = IdWorker.getId();
            dataSourceBasicInfo.setDataSourceId(testId);
            // 获取JdbcTemplate
            try {
                JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
                result = jdbcCommonService.getTables(jdbcTemplate, databaseName, tableName, pageNo, pageSize);
            } catch (Exception e) {
                logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
                throw new ServiceException(e.getMessage());
            } finally {
                DataSourceProvider.getInstance().remoteDataSource(testId);
            }
        }

        pageDataInfo.setTotal(MapUtils.getInteger(result, "total"));
        pageDataInfo.setTotalList((List) MapUtils.getObject(result, "data"));
        return pageDataInfo;
    }

    @Override
    public PageDataInfo<Table> getTableDetails(Long id, String dbName, String tableName, Integer pageNo, Integer pageSize, String databaseName) {
        PageDataInfo<Table> pageDataInfo = new PageDataInfo(pageNo, pageSize);

        // 查询结果
        Map<String, Object> result = new HashMap<>();

        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, dataSourceBasicInfo.getType(), dbName);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            result = bigDataCommonService.getTableDetails(dataSourceBasicInfo, databaseName, tableName, pageNo, pageSize);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName, pageNo, pageSize);
            R<Map<String, Object>> r = remoteHive2Service.getTables(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            result = r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            long testId = IdWorker.getId();
            dataSourceBasicInfo.setDataSourceId(testId);
            // 获取JdbcTemplate
            try {
                JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
                result = jdbcCommonService.getTableDetails(jdbcTemplate, databaseName, tableName, pageNo, pageSize);
            } catch (Exception e) {
                logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
                throw new ServiceException(e.getMessage());
            } finally {
                DataSourceProvider.getInstance().remoteDataSource(testId);
            }
        }
        pageDataInfo.setTotal(MapUtils.getInteger(result, "total"));
        pageDataInfo.setTotalList((List) MapUtils.getObject(result, "data"));
        return pageDataInfo;
    }

    @Override
    @SwitchDataSource(value = "current")
    public PageDataInfo<Table> getTableDetailsByTenantId(Long tenantId, Long id, String dbName, String tableName, Integer pageNo, Integer pageSize, String databaseName) {
        PageDataInfo<Table> pageDataInfo = new PageDataInfo(pageNo, pageSize);

        // 查询结果
        Map<String, Object> result = new HashMap<>();

        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, dataSourceBasicInfo.getType(), dbName);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            result = bigDataCommonService.getTableDetails(dataSourceBasicInfo, databaseName, tableName, pageNo, pageSize);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName, pageNo, pageSize);
            R<Map<String, Object>> r = remoteHive2Service.getTables(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            result = r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            long testId = IdWorker.getId();
            dataSourceBasicInfo.setDataSourceId(testId);
            // 获取JdbcTemplate
            try {
                JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
                result = jdbcCommonService.getTableDetails(jdbcTemplate, databaseName, tableName, pageNo, pageSize);
            } catch (Exception e) {
                logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
                throw new ServiceException(e.getMessage());
            } finally {
                DataSourceProvider.getInstance().remoteDataSource(testId);
            }
        }
        pageDataInfo.setTotal(MapUtils.getInteger(result, "total"));
        pageDataInfo.setTotalList((List) MapUtils.getObject(result, "data"));
        return pageDataInfo;
    }

    @Override
    @SwitchDataSource(value = "current")
    public PageDataInfo getTablesByTenantId(Long tenantId, Long id, String dbName, String tableName, Integer pageNo, Integer pageSize, String databaseName) {
        PageDataInfo pageDataInfo = new PageDataInfo(pageNo, pageSize);

        // 查询结果
        Map<String, Object> result = new HashMap<>();

        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, dataSourceBasicInfo.getType(), dbName);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            result = bigDataCommonService.getTables(dataSourceBasicInfo, databaseName, tableName, pageNo, pageSize);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName, pageNo, pageSize);
            R<Map<String, Object>> r = remoteHive2Service.getTables(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            result = r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());

            long testId = IdWorker.getId();
            dataSourceBasicInfo.setDataSourceId(testId);
            // 获取JdbcTemplate
            try {
                JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
                result = jdbcCommonService.getTables(jdbcTemplate, databaseName, tableName, pageNo, pageSize);
            } catch (Exception e) {
                logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
                throw new ServiceException(e.getMessage());
            } finally {
                DataSourceProvider.getInstance().remoteDataSource(testId);
            }
        }

        pageDataInfo.setTotal(MapUtils.getInteger(result, "total"));
        pageDataInfo.setTotalList((List) MapUtils.getObject(result, "data"));
        return pageDataInfo;
    }

    @Override
    public PageDataInfo getViews(Long id, String dbName, String viewName, Integer pageNo, Integer pageSize, String databaseName) {
        PageDataInfo pageDataInfo = new PageDataInfo(pageNo, pageSize);

        // 查询结果
        Map<String, Object> result = new HashMap<>();

        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);

        String type = dataSource.getType();
        SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, type, dbName);
        if (isFileSystemDataSource(dataSourceBasicInfo.getType()) || "hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            // 文件类型
            throw new ServiceException(Status.ICEBERG_DOES_NOT_SUPPORT_VIEWS);
        }

        JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
        long testId = IdWorker.getId();
        dataSourceBasicInfo.setDataSourceId(testId);
        // 获取JdbcTemplate
        try {
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            result = jdbcCommonService.getViews(jdbcTemplate, databaseName, viewName, pageNo, pageSize);
        } catch (Exception e) {
            logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
            throw new ServiceException(e.getMessage());
        } finally {
            DataSourceProvider.getInstance().remoteDataSource(testId);
        }

        pageDataInfo.setTotal(MapUtils.getInteger(result, "total"));
        pageDataInfo.setTotalList((List) MapUtils.getObject(result, "data"));
        return pageDataInfo;
    }

    @Override
    public List<Field> getFields(Long id, String dbName, String tableName, String databaseName) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);

        // 判断表是否存在
        Boolean existTable = isExistTable(id, dbName, databaseName, tableName);
        if (!existTable) {
            throw new ServiceException(MessageFormat.format(Status.TABLE_DOES_NOT_EXIST.getMsg(), tableName));
        }
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        String type = dataSource.getType();
        List<Field> fieldList;
        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            fieldList = bigDataCommonService.getFields(dataSourceBasicInfo, databaseName, tableName);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName);
            R<List<Field>> r = remoteHive2Service.getFields(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            fieldList = r.getData();
        } else {
            SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, type, dbName);
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            long testId = IdWorker.getId();
            dataSourceBasicInfo.setDataSourceId(testId);
            // 获取JdbcTemplate
            try {
                JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
                fieldList = jdbcCommonService.getFields(jdbcTemplate, databaseName, tableName);
                // 定义实体主键
                defineEntityPrimaryKey(tableName, databaseName, fieldList, jdbcCommonService, jdbcTemplate);
            } catch (Exception e) {
                logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
                throw new ServiceException(e.getMessage());
            } finally {
                DataSourceProvider.getInstance().remoteDataSource(testId);
            }
        }
        return fieldRelationService.toBase(dataSource.getType(), fieldList);
    }

    @Override
    public List<Field> getMessageQueueFields(Long id, String topicName, String jsonPath) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        List<Field> fieldList = Collections.emptyList();
        if (isMessageQueue(dataSourceBasicInfo.getType())) {
            // 消息队列类型数据源kafka等
            MessageQueueCommonService messageQueueCommonService = getMessageQueueCommonService(dataSourceBasicInfo.getPluginId());
            fieldList = messageQueueCommonService.getTopicFields(dataSourceBasicInfo, topicName, jsonPath);
        }
        return fieldList;
    }

    @Override
    public List<Map<String, Object>> getMessageQueueData(Long id, String topicName, String jsonPath, int maxMessages) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        List<Map<String, Object>> queueData = Collections.emptyList();
        if (isMessageQueue(dataSourceBasicInfo.getType())) {
            // 消息队列类型数据源kafka等
            MessageQueueCommonService messageQueueCommonService = getMessageQueueCommonService(dataSourceBasicInfo.getPluginId());
            queueData = messageQueueCommonService.getTopicMessages(dataSourceBasicInfo, topicName, jsonPath, maxMessages);
        }
        return queueData;
    }

    @Override
    public List<Field> getFieldsByTenantId(Long tenantId, Long id, String dbName, String tableName, String databaseName) {
        // 切换到对应租户
        DataSourceHolder.setDataSourceKey(Constants.FORCE_SWITCH_TENANT_PREFIX + tenantId);
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);

        // 判断表是否存在
        Boolean existTable = isExistTable(id, dbName, databaseName, tableName);
        if (!existTable) {
            throw new ServiceException(MessageFormat.format(Status.TABLE_DOES_NOT_EXIST.getMsg(), tableName));
        }

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);

        String type = dataSource.getType();
        SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, type, dbName);

        List<Field> fieldList;
        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            fieldList = bigDataCommonService.getFields(dataSourceBasicInfo, databaseName, tableName);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName);
            R<List<Field>> r = remoteHive2Service.getFields(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            fieldList = r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            long testId = IdWorker.getId();
            dataSourceBasicInfo.setDataSourceId(testId);
            // 获取JdbcTemplate
            try {
                JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
                fieldList = jdbcCommonService.getFields(jdbcTemplate, databaseName, tableName);
                // 定义实体主键
                defineEntityPrimaryKey(tableName, databaseName, fieldList, jdbcCommonService, jdbcTemplate);
            } catch (Exception e) {
                logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
                throw new ServiceException(e.getMessage());
            } finally {
                DataSourceProvider.getInstance().remoteDataSource(testId);
            }
        }
        // 还原租户
        DataSourceHolder.setDataSourceKey(null);
        return fieldRelationService.toBase(dataSource.getType(), fieldList);
    }

    /**
     * 定义实体主键
     *
     * @param tableName         表名
     * @param databaseName      库/Schema
     * @param fieldList         字段列表
     * @param jdbcCommonService 数据源操作类
     * @param jdbcTemplate      JdbcTemplate
     */
    private void defineEntityPrimaryKey(String tableName, String databaseName, List<Field> fieldList, JdbcCommonService jdbcCommonService, JdbcTemplate jdbcTemplate) {
        List<Field> pkFieldList = fieldList.stream().filter(Field::getIsPk).collect(Collectors.toList());
        // 如果没有主键，则获取唯一字段标记为主键
        if (pkFieldList.isEmpty()) {
            List<UniqueField> uniqueFields = jdbcCommonService.getUniqueFields(jdbcTemplate, databaseName, tableName);
            if (CollectionUtils.isNotEmpty(uniqueFields)) {
                Map<String, List<String>> mergeUniqueFieldMap = uniqueFields.stream().collect(Collectors.groupingBy(UniqueField::getIndexName, Collectors.mapping(UniqueField::getFieldName, Collectors.toList())));
                // 遍历mergeUniqueFieldMap，对比Map中List集合中的元素是否存在与fieldList并且列不为空
                Iterator<Map.Entry<String, List<String>>> iterator = mergeUniqueFieldMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, List<String>> entry = iterator.next();
                    List<String> fieldNames = entry.getValue();
                    int indexSize = fieldNames.size();
                    // 统计唯一索引中列不为空的数量(符合定义为主键)
                    int indexNullSize = (int) fieldNames.stream().filter(fieldName -> fieldList.stream().anyMatch(f -> f.getFieldName().equals(fieldName) && !f.getIsNull())).count();
                    // 对比两边条件如果相等表示该索引下的列可以被定义为主键，不再检查其他约束是否满足条件
                    if (indexSize == indexNullSize) {
                        fieldList.stream().filter(field -> fieldNames.contains(field.getFieldName())).forEach(field -> field.setIsPk(true));
                        break;
                    }
                }
            }
        }
    }

    @Override
    public List<Field> getConvertFields(Long id, String databaseName, String tableName) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);

        JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());

        // 获取JdbcTemplate
        JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
        List<Field> fields = jdbcCommonService.getFields(jdbcTemplate, databaseName, tableName);
        // 定义实体主键
        defineEntityPrimaryKey(tableName, databaseName, fields, jdbcCommonService, jdbcTemplate);
        return fieldRelationService.toBase(jdbcCommonService.getDbType(), fields).stream().map(field -> {
            field.setFieldType(field.getBaseFieldType().name());
            return field;
        }).collect(Collectors.toList());
    }

    @Override
    public void execDdlSql(Long id, String sql) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 判断读写权限
        checkIsRead(dataSource);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);

        if (isHiveDataSource(dataSource.getType())) {
            if (!isConfigHiveJdbc(dataSourceBasicInfo)) {
                throw new ServiceException("缺失 Hive Jdbc 地址，无法使用execDdlSql功能");
            }
            // 需要校验数据源中是否配置了hive jdbc的相关配置
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(bigDataCommonService, dataSourceBasicInfo);
            // 去除特殊符号
            sql = sql.replaceAll("\r", "").replaceAll("\n", " ");
            logger.info("执行的Hive Jdbc DDL语句:{}", sql);
            bigDataCommonService.execDdlSql(jdbcTemplate, sql);
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            // 去除特殊符号
            sql = sql.replaceAll("\r", "").replaceAll("\n", " ");
            logger.info("执行的DDL语句:{}", sql);
            jdbcCommonService.execDdlSql(jdbcTemplate, sql);
        }
    }

    @Override
    @SwitchDataSource(value = "current")
    public void execDdlSqlByTenantId(Long tenantId, Long id, String sql) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 判断读写权限
        checkIsRead(dataSource);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);

        if (isHiveDataSource(dataSource.getType())) {
            if (!isConfigHiveJdbc(dataSourceBasicInfo)) {
                throw new ServiceException("缺失 Hive Jdbc 地址，无法使用execDdlSql功能");
            }
            // 需要校验数据源中是否配置了hive jdbc的相关配置
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(bigDataCommonService, dataSourceBasicInfo);
            // 去除特殊符号
            sql = sql.replaceAll("\r", "").replaceAll("\n", " ");
            logger.info("执行的Hive Jdbc DDL语句:{}", sql);
            bigDataCommonService.execDdlSql(jdbcTemplate, sql);
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            // 去除特殊符号
            sql = sql.replaceAll("\r", "").replaceAll("\n", " ");
            logger.info("执行的DDL语句:{}", sql);
            jdbcCommonService.execDdlSql(jdbcTemplate, sql);
        }
    }

    @Override
    public List<Map<String, Object>> execDqlSql(Long id, String sql) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // iceberg替换成trino来查询 todo 这种设计有问题，后面可以参考hive的设计方式，trino集成到iceberg数据源中统一配置即可
        if ("iceberg".equalsIgnoreCase(dataSource.getType())) {
            VlabelItem<DataSource> trinoDataSource = dataSourceMapper.selectByCode("trino");
            if (trinoDataSource == null) {
                throw new ServiceException("trino数据源不存在");
            }
            dataSource = trinoDataSource.getProperties();
        }
        // TODO:需要验证sql是否是select，而不是insert、update、delete
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        if (isHiveDataSource(dataSource.getType())) {
            if (!isConfigHiveJdbc(dataSourceBasicInfo)) {
                throw new ServiceException("缺失 Hive Jdbc 地址，无法使用execDqlSql功能");
            }
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(bigDataCommonService, dataSourceBasicInfo);
            // 去除特殊符号
            sql = sql.replaceAll("\r", "").replaceAll("\n", " ");
            logger.info("执行的Hive Jdbc DQL语句:{}", sql);
            return bigDataCommonService.execDqlSql(jdbcTemplate, sql);
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            logger.info("执行的DQL语句:{}", sql);
            return jdbcCommonService.execDqlSql(jdbcTemplate, sql);
        }
    }

    // TODO:存在sql注入问题
    @Override
    public int insert(Long id, String databaseName, String tableName, Map<String, Object> insertField) {
        // // 检查数据源是否存在
        // DataSource dataSource = checkDataSource(id);
        // // 获取DataSourceClient
        // DataSourceClient dataSourceClient = getDataSourceClient(dataSource.getType(), dataSource.getConnectionParams());
        // // 获取sql
        // String sql = dataSourceClient.insert(databaseName, tableName, insertField);
        // // 获取jdbcTemplate并执行sql
        // return dataSourceClient.getJdbcTemplate().update(sql);
        return 0;
    }

    @Override
    public int insertBatch(Long id, String databaseName, String tableName, List<Map<String, Object>> insertFieldList) {
        // // 检查数据源是否存在
        // DataSource dataSource = checkDataSource(id);
        // // 获取DataSourceClient
        // DataSourceClient dataSourceClient = getDataSourceClient(dataSource.getType(), dataSource.getConnectionParams());
        // // 获取sql
        // String sql = dataSourceClient.insertBatch(databaseName, tableName, insertFieldList);
        // // 获取jdbcTemplate并执行sql
        // return dataSourceClient.getJdbcTemplate().update(sql);
        return 0;
    }

    @Override
    public int update(Long id, String databaseName, String tableName, List<FieldValue> setFieldValueDtoList, List<FieldValue> whereFieldValueDtoList) {
        // // 检查数据源是否存在
        // DataSource dataSource = checkDataSource(id);
        // // 获取DataSourceClient
        // DataSourceClient dataSourceClient = getDataSourceClient(dataSource.getType(), dataSource.getConnectionParams());
        // // 获取sql
        // String sql = dataSourceClient.update(databaseName, tableName, setFieldValueDtoList, whereFieldValueDtoList);
        // // 获取jdbcTemplate并执行sql
        // return dataSourceClient.getJdbcTemplate().update(sql);
        return 0;
    }

    @Override
    public int delete(Long id, String databaseName, String tableName, List<FieldValue> fieldValueDtoList) {
        // // 检查数据源是否存在
        // DataSource dataSource = checkDataSource(id);
        // // 获取DataSourceClient
        // DataSourceClient dataSourceClient = getDataSourceClient(dataSource.getType(), dataSource.getConnectionParams());
        // // 获取sql
        // String sql = dataSourceClient.delete(databaseName, tableName, fieldValueDtoList);
        // // 获取jdbcTemplate并执行sql
        // return dataSourceClient.getJdbcTemplate().update(sql);
        return 0;
    }

    @Override
    public int createTable(CreateTableDto createTable) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(createTable.getDataSourceId());
        // 判断读写权限
        checkIsRead(dataSource);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            bigDataCommonService.createTable(dataSourceBasicInfo, createTable.getDatabaseName(), createTable.getTableName(), createTable.getFieldList(), createTable.getSpecialMap());
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, createTable.getDatabaseName(), createTable.getTableName(), createTable.getFieldList(), createTable.getSpecialMap());
            R r = remoteHive2Service.createTable(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            // // 判断表是否存在
            // Boolean existTable = jdbcCommonService.isExistTable(jdbcTemplate, createTable.getDatabaseName(), createTable.getTableName());
            // if (existTable) {
            //     // 存在则删除表
            //     jdbcCommonService.dropTable(jdbcTemplate, createTable.getDatabaseName(), createTable.getTableName());
            // }

            // TODO:验证长度是否合法

            // 创表
            jdbcCommonService.createTable(jdbcTemplate, createTable.getDatabaseName(), createTable.getTableName(), createTable.getFieldList(), createTable.getSpecialMap());
        }
        return 1;
    }

    @Override
    @SwitchDataSource(value = "current")
    public int createTableByTenantId(Long tenantId, CreateTableDto createTable) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(createTable.getDataSourceId());
        // 判断读写权限
        checkIsRead(dataSource);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            bigDataCommonService.createTable(dataSourceBasicInfo, createTable.getDatabaseName(), createTable.getTableName(), createTable.getFieldList(), createTable.getSpecialMap());
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, createTable.getDatabaseName(), createTable.getTableName(), createTable.getFieldList(), createTable.getSpecialMap());
            R r = remoteHive2Service.createTable(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            // // 判断表是否存在
            // Boolean existTable = jdbcCommonService.isExistTable(jdbcTemplate, createTable.getDatabaseName(), createTable.getTableName());
            // if (existTable) {
            //     // 存在则删除表
            //     jdbcCommonService.dropTable(jdbcTemplate, createTable.getDatabaseName(), createTable.getTableName());
            // }

            // TODO:验证长度是否合法

            // 创表
            jdbcCommonService.createTable(jdbcTemplate, createTable.getDatabaseName(), createTable.getTableName(), createTable.getFieldList(), createTable.getSpecialMap());
        }
        return 1;
    }

    @Override
    public Boolean isExistTable(Long id, String databaseName, String tableName) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            return bigDataCommonService.isExistTable(dataSourceBasicInfo, databaseName, tableName);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName);
            R<Boolean> r = remoteHive2Service.isExistTable(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            return r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            return jdbcCommonService.isExistTable(jdbcTemplate, databaseName, tableName);
        }
    }

    @Override
    @SwitchDataSource(value = "current")
    public Boolean isExistTableByTenantId(Long tenantId, Long id, String databaseName, String tableName) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            return bigDataCommonService.isExistTable(dataSourceBasicInfo, databaseName, tableName);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName);
            R<Boolean> r = remoteHive2Service.isExistTable(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            return r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            return jdbcCommonService.isExistTable(jdbcTemplate, databaseName, tableName);
        }
    }

    @Override
    public Boolean isExistTable(Long id, String databaseName, String schema, String tableName) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        String type = dataSource.getType();
        SourceInfoUtils.updateJdbcUrl(dataSourceBasicInfo, type, databaseName);
        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            return bigDataCommonService.isExistTable(dataSourceBasicInfo, schema, tableName);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, schema, tableName);
            R<Boolean> r = remoteHive2Service.isExistTable(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
            return r.getData();
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            long testId = IdWorker.getId();
            dataSourceBasicInfo.setDataSourceId(testId);
            // 获取JdbcTemplate
            try {
                JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
                return jdbcCommonService.isExistTable(jdbcTemplate, schema, tableName);
            } catch (Exception e) {
                logger.error("datasource test connection error,  dataSourceBasicInfo:{}, message:{}.", dataSourceBasicInfo, e.getMessage());
                throw new ServiceException(e.getMessage());
            } finally {
                DataSourceProvider.getInstance().remoteDataSource(testId);
            }
        }
    }

    @Override
    public void dropTable(Long id, String databaseName, String tableName) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 判断读写权限
        checkIsRead(dataSource);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            bigDataCommonService.dropTable(dataSourceBasicInfo, databaseName, tableName);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName);
            R r = remoteHive2Service.dropTable(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            // 存在则删除，不存在则不删
            jdbcCommonService.dropTable(jdbcTemplate, databaseName, tableName);
        }
    }

    @Override
    @SwitchDataSource(value = "current")
    public void dropTableByTenantId(Long tenantId, Long id, String databaseName, String tableName) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 判断读写权限
        checkIsRead(dataSource);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            bigDataCommonService.dropTable(dataSourceBasicInfo, databaseName, tableName);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName, tableName);
            R r = remoteHive2Service.dropTable(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            // 存在则删除，不存在则不删
            jdbcCommonService.dropTable(jdbcTemplate, databaseName, tableName);
        }
    }

    @Override
    public DataSource getDataSource(Long datasourceId) {
        VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectById(datasourceId);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public Set<String> getFieldBySql(Long id, String sql, Boolean flag) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 去除 ; 符号
        if (sql.contains(";")) {
            sql = sql.replace(";", "");
        }
        // 构造查询语句
        String finalSql = sql;
        if (flag) {
            // 使用参数化方式构造临时表查询（需配合实际业务逻辑）
            finalSql = "select * from (" + finalSql + ") as temporary limit 1";
        }
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
        // 获取JdbcTemplate
        JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
        logger.info("查询列SQL: {}", finalSql);
        // 查询表字段名称
        Set<String> columnNames = new CopyOnWriteArraySet<>();
        // 执行查询并获取 SqlRowSet
        SqlRowSet rowSet = jdbcTemplate.queryForRowSet(finalSql);
        // 直接从 SqlRowSet 中提取元数据
        SqlRowSetMetaData metaData = rowSet.getMetaData();
        int columnCount = metaData.getColumnCount();
        logger.info("查询列数: {}", columnCount);
        for (int i = 1; i <= columnCount; i++) {
            String label = metaData.getColumnLabel(i);
            columnNames.add(label != null ? label : metaData.getColumnName(i));
        }
        return columnNames;
    }

    @Override
    public void execDMlSql(Long id, String sql) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 判断读写权限
        checkIsRead(dataSource);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        // TODO:需要验证sql是否是select，而不是insert、update、delete
        // 是否为hive数据源，如果是hive数据源采用hive jdbc 去执行 dml语句
        if (isHiveDataSource(dataSource.getType())) {
            if (!isConfigHiveJdbc(dataSourceBasicInfo)) {
                throw new ServiceException("缺失 Hive Jdbc 地址，无法使用execDMlSql功能");
            }
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(bigDataCommonService, dataSourceBasicInfo);
            // 去除特殊符号
            sql = sql.replaceAll("\r", "").replaceAll("\n", " ");
            logger.info("执行的Hive Jdbc DML语句:{}", sql);
            bigDataCommonService.execDMlSql(jdbcTemplate, sql);
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            // 去除特殊符号
            sql = sql.replaceAll("\r", "").replaceAll("\n", " ");
            logger.info("执行的DML语句:{}", sql);
            jdbcCommonService.execDMlSql(jdbcTemplate, sql);
        }

    }

    @Override
    public void createSchema(Long id, String databaseName) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 判断读写权限
        checkIsRead(dataSource);

        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);

        // 文件类型插件单独处理
        if (isFileSystemDataSource(dataSourceBasicInfo.getType())) {
            BigDataCommonService bigDataCommonService = getBigDataCommonService(dataSourceBasicInfo.getPluginId());
            bigDataCommonService.createSchema(dataSourceBasicInfo, databaseName);
        } else if ("hive2".equalsIgnoreCase(dataSourceBasicInfo.getType())) {
            HiveAPIDto hiveAPIDto = new HiveAPIDto(dataSourceBasicInfo, databaseName);
            R r = remoteHive2Service.createSchema(hiveAPIDto);
            if (r.getCode() != R.SUCCESS) {
                throw new ServiceException(r.getMsg());
            }
        } else {
            JdbcCommonService jdbcCommonService = getJdbcCommonService(dataSourceBasicInfo.getPluginId());
            // 获取JdbcTemplate
            JdbcTemplate jdbcTemplate = getJdbcTemplate(jdbcCommonService, dataSourceBasicInfo);
            // 存在则删除，不存在则不删
            jdbcCommonService.createSchema(jdbcTemplate, databaseName);
        }
    }

    @Override
    public List<FileSystem> searchDirectoryAndFiles(Long id, String folderPath, boolean useDir) {
        // 检查数据源是否存在
        DataSource dataSource = checkDataSource(id);
        // 数据源连接信息
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        dataSourceBasicInfo.setDataSourceId(id);
        FileCommonService fileCommonService = getFileCommonService(dataSourceBasicInfo.getPluginId());
        return fileCommonService.searchDirectoryAndFiles(dataSourceBasicInfo, folderPath, null, useDir);
    }

    @Override
    public void batchExportDataSourceByIds(List<Long> ids, HttpServletResponse response) {
        List<DataSource> dataSources = dataSourceMapper.selectDataSourceList(ids);
        ExportJSONFileUtil.exportJson(response, dataSources);
    }

    @Override
    public R importDataSource(MultipartFile file) {
        // 读取文件解析json字符串
        String explainJson = ExportJSONFileUtil.file2String(file);
        if (StringUtils.isEmpty(explainJson)) {
            return R.fail("导入数据源为空");
        }
        List<DataSource> dataSourceList = JSONObject.parseArray(explainJson, DataSource.class);
        // 导入成功数量
        int success = 0;
        // 导入失败数量
        int fail = 0;
        for (DataSource dataSource : dataSourceList) {
            VlabelItem<DataSource> vlabelItem = dataSourceMapper.selectById(dataSource.getId());
            try {
                if (vlabelItem == null) {
                    // 新增数据源
                    createDataSource(dataSource);
                } else {
                    // 更新数据源
                    updateDataSource(dataSource);
                }
                success++;
            } catch (Exception e) {
                fail++;
                logger.info("数据源[{}]导入新增/更新失败，详情：{}", dataSource.getCode(), e.getMessage());
            }
        }
        if (success < dataSourceList.size()) {
            return R.ok(String.format("数据源部分导入成功，成功数量：%s，失败数量：%s", success, fail));
        } else {
            return R.ok("数据源导入成功");
        }
    }

    /**
     * 是否为文件系统的数据源（iceberg，hive3，hdfs等）
     *
     * @return true:是文件系统数据源 false:不是文件系统数据源
     */
    private boolean isFileSystemDataSource(String type) {
        // 这里hive2 因为跟hive3冲突 不做判断单独调用rest api 调用hive2
        return "iceberg".equalsIgnoreCase(type) || "hive3".equalsIgnoreCase(type);
    }

    /**
     * 是否为文件系统（sftp，ftp）
     *
     * @return true:是文件系统 false:不是文件系统
     */
    private boolean isFileSystem(String type) {
        return "sftp".equalsIgnoreCase(type) || "ftp".equalsIgnoreCase(type);
    }

    /**
     * 是否为消息队列类型数据源（kafka等）
     *
     * @return true:是消息队列数据源 false:不是消息队列数据源
     */
    private boolean isMessageQueue(String type) {
        return "kafka".equalsIgnoreCase(type);
    }

    /**
     * 是否为hive数据源，hive数据源可以通过hive jdbc去执行dql ddl dml语句
     *
     * @param type 数据源类型
     */
    private boolean isHiveDataSource(String type) {
        return "hive2".equalsIgnoreCase(type) || "hive3".equalsIgnoreCase(type);
    }

    /**
     * 是否配置了hive jdbc相关配置 配置了才可以使用hive jdbc
     *
     * @param dataSourceBasicInfo
     * @return
     */
    private boolean isConfigHiveJdbc(DataSourceBasicInfo dataSourceBasicInfo) {
        return StringUtils.isNotEmpty(dataSourceBasicInfo.getJdbcUrl());
    }


}
