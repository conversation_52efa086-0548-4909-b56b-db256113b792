package com.datalink.fdop.element.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.split.SplitUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datasource.annotation.SwitchDataSource;
import com.datalink.fdop.common.datasource.dynamic.DataSourceHolder;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteFieldRelationService;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.api.domain.dto.CreateTableDto;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.element.api.enums.BuiltinField;
import com.datalink.fdop.element.api.enums.CreateTableWay;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.element.mapper.DataEntityMapper;
import com.datalink.fdop.element.mapper.DataEntityTableMapper;
import com.datalink.fdop.element.mapper.DataEntityTableMappingMapper;
import com.datalink.fdop.element.service.IDataEntityService;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import com.datalink.fdop.element.service.IDataEntityTableService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
@Slf4j
public class DataEntityTableServiceImpl implements IDataEntityTableService {

    @Autowired
    private DataEntityMapper dataEntityMapper;

    @Autowired
    private DataEntityTableMapper dataEntityTableMapper;

    @Autowired
    private DataEntityTableMappingMapper dataEntityTableMappingMapper;

    @Autowired
    private RemoteJdbcService remoteJdbcService;

    @Autowired
    private RemoteFieldRelationService remoteFieldRelationService;

    @Autowired
    private IDataEntityStructureService dataEntityStructureService;

    @Autowired
    private RemoteDriveService remoteDriveService;

    @Autowired
    private IDataEntityService dataEntityService;

    // 验证数据实体
    private DataEntity checkDataEntity(Long dataEntityId) {
        DataEntity dataEntity = dataEntityMapper.selectById(dataEntityId);
        if (dataEntity == null) {
            throw new ServiceException(Status.DATA_ENTITY_NOT_EXIST);
        }
        return dataEntity;
    }

    // 验证表
    @Override
    public DataEntityTable checkDataEntityTable(Long tableId) {
        if (tableId == null) {
            throw new ServiceException(Status.DATA_ENTITY_TABLE_NOT_EXIST);
        }
        DataEntityTable dataEntityTable = dataEntityTableMapper.selectById(tableId);
        if (dataEntityTable == null) {
            throw new ServiceException(Status.DATA_ENTITY_TABLE_NOT_EXIST);
        }
        return dataEntityTable;
    }

    // 验证明细
    private void checkDetails(DataEntityTable dataEntityTable) {
        if (dataEntityTable.getDataSourceId() == null) {
            throw new ServiceException(Status.DATA_ENTITY_TABLE_SOURCE_NOT_NULL);
        }

        if (StringUtils.isEmpty(dataEntityTable.getDatabaseName())) {
            throw new ServiceException(Status.DATA_ENTITY_TABLE_DATABASE_NOT_NULL);
        }

        if (StringUtils.isEmpty(dataEntityTable.getTableName())) {
            throw new ServiceException(Status.DATA_ENTITY_TABLE_TABLE_NOT_NULL);
        }

        if (dataEntityTable.getIsRead() == null) {
            throw new ServiceException(Status.SPECIFY_THE_READ_WRITE_MODE);
        }

        if (dataEntityTable.getCreateTableWay() == CreateTableWay.VIEW && !dataEntityTable.getIsRead()) {
            throw new ServiceException(Status.VIEWS_CAN_ONLY_BE_READ_AND_WRITTEN_IN_READ_ONLY);
        }

        if (dataEntityTable.getCreateTableWay() == CreateTableWay.MANUAL && StringUtils.isEmpty(dataEntityTable.getCreateTablesql())) {
            throw new ServiceException(Status.DATA_ENTITY_TABLE_SQL_NOT_NULL);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataEntityTable createOnUpdate(DataEntityTable dataEntityTable) {
        // 验证数据实体
        Long dataEntityId = dataEntityTable.getDataEntityId();
        DataEntity dataEntity = checkDataEntity(dataEntityId);
        // tableId不为空则修改表
        if (dataEntity.getTableId() != null) {
            dataEntityTable.setId(dataEntity.getTableId());
            this.update(dataEntityTable);
            return dataEntityTable;
        }
        // 验证明细
        checkDetails(dataEntityTable);
        // 新增实体表
        dataEntityTable.setId(IdWorker.getId());
        int insert = dataEntityTableMapper.insertTable(dataEntityTable);
        // 修改实体关联表信息
        if (insert > 0) {
            // 绑定数据源信息
            // 先删除数据实体的关联表和数据源的关系，再保存数据实体的关联表和数据源的关系
            dataEntityTableMapper.deleteDataSourceTable(dataEntityTable.getId());
            dataEntityTableMapper.updateDataSourceTable(dataEntityTable.getId(), dataEntityTable.getDataSourceId());
            // 查询数据源信息
            R<DataSource> dataSourceR = remoteDriveService.queryDataSource(dataEntityTable.getDataSourceId());
            if (dataSourceR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(dataSourceR.getMsg());
            }
            DataSource dataSource = dataSourceR.getData();
            DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
            // 先删除实体关联表和元数据表的关系
            dataEntityTableMapper.deleteEntityTableTableEdge(dataEntityTable.getId());
            // 再创建实体关联表和元数据表的关系
            dataEntityTableMapper.createEntityTableTableEdge(dataEntityTable.getId(), dataSource.getId(), dataSourceBasicInfo.getDatabase(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
            // 绑定关联表和字段的mapping关系
            this.saveMapping(dataEntityTable);
            // 绑定实体和关联表
            dataEntityMapper.updateById(new DataEntity(dataEntityId, dataEntityTable.getId()));
        }
        return dataEntityTable;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataEntityTable dataEntityTable) {
        // 验证数据实体
        Long dataEntityId = dataEntityTable.getDataEntityId();
        checkDataEntity(dataEntityId);

        // 验证表
        checkDataEntityTable(dataEntityTable.getId());

        // 验证明细
        checkDetails(dataEntityTable);

        int update = dataEntityTableMapper.updateById(dataEntityTable);
        // 修改实体关联表信息
        if (update > 0) {
            // 绑定数据源信息
            // 先删除数据实体的关联表和数据源的关系，再保存数据实体的关联表和数据源的关系
            dataEntityTableMapper.deleteDataSourceTable(dataEntityTable.getId());
            dataEntityTableMapper.updateDataSourceTable(dataEntityTable.getId(), dataEntityTable.getDataSourceId());

            // 查询数据源信息
            R<DataSource> dataSourceR = remoteDriveService.queryDataSource(dataEntityTable.getDataSourceId());
            if (dataSourceR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(dataSourceR.getMsg());
            }
            DataSource dataSource = dataSourceR.getData();
            DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
            // 先删除实体关联表和元数据表的关系
            dataEntityTableMapper.deleteEntityTableTableEdge(dataEntityTable.getId());
            // 再创建实体关联表和元数据表的关系
            dataEntityTableMapper.createEntityTableTableEdge(dataEntityTable.getId(), dataSource.getId(), dataSourceBasicInfo.getDatabase(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());

            // 绑定关联表和字段的mapping关系
            this.saveMapping(dataEntityTable);
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int isDeleteOldTable(Long dataEntityId, Long dataSourceId, String databaseName, String tableName, Boolean isDelete) {
        // 验证数据实体
        checkDataEntity(dataEntityId);

        // 删除表
        if (isDelete) {
            R dropTableResult = remoteJdbcService.dropTable(dataSourceId, databaseName, tableName);
            if (dropTableResult.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(dropTableResult.getMsg());
            }
            // 删除脏表
            //remoteJdbcService.dropTable(dataSourceId, databaseName, tableName + Constants.DIRTY_SUFFIX);
        }
        return 1;
    }

    /**
     * 保存实体关联表和mapping字段的关系
     *
     * @param dataEntityTable
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveMapping(DataEntityTable dataEntityTable) {
        // 修改为直接读取源库表与实体字段由后台同名匹配忽略大小写，解决前端匹配由于字段数量过多，导致匹配不上的缺陷问题（主要在同步的场景下）
        // 查询实体结构
        List<DataEntityStructureVo> dataEntityStructureVoList = dataEntityStructureService.selectStructureById(dataEntityTable.getDataEntityId(), true);
        // 查询源库表字段信息
        List<Field> fields = getDataSourceFields(dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
        return saverMappingCommon(dataEntityTable, dataEntityStructureVoList, fields);
    }

    @Override
    public int saveMapping(Long tenantId, DataEntityTable dataEntityTable) {
        // 修改为直接读取源库表与实体字段由后台同名匹配忽略大小写，解决前端匹配由于字段数量过多，导致匹配不上的缺陷问题（主要在同步的场景下）
        // 查询实体结构
        List<DataEntityStructureVo> dataEntityStructureVoList = dataEntityStructureService.selectStructureById(dataEntityTable.getDataEntityId(), true);
        // 查询源库表字段信息
        List<Field> fields = getDataSourceFields(tenantId, dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
        return saverMappingCommon(dataEntityTable, dataEntityStructureVoList, fields);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @SwitchDataSource(value = "current")
    public void saveMappingOnSyncAndCreatTable(Long tenantId, DataEntityTable dataEntityTable) {
        List<DataEntityTableMapping> dataEntityTableMappingList = dataEntityTable.getDataEntityTableMappingList();
        // 删除mapping关系
        dataEntityTableMappingMapper.deleteMapping(dataEntityTable.getId());
        if (CollectionUtils.isNotEmpty(dataEntityTableMappingList)) {
            // 保存实体表中自定义字段和mapping的关系
            List<DataEntityTableMapping> customizeMappingList = dataEntityTableMappingList.stream().filter(dataEntityTableMapping -> dataEntityTableMapping.getEntityInsertType() == EntityInsertType.CUSTOMIZE || dataEntityTableMapping.getEntityInsertType() == EntityInsertType.BUILTIN).map(dataEntityTableMapping -> {
                dataEntityTableMapping.setDataEntityId(dataEntityTable.getDataEntityId());
                dataEntityTableMapping.setDataTableId(dataEntityTable.getId());
                return dataEntityTableMapping;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customizeMappingList)) {
                // 每50提交一次
                // 创建实体表和mapping的关系，mapping和实体字段的关系
                List<List<DataEntityTableMapping>> customizeMappingSplitList = SplitUtils.splitList(customizeMappingList, 50);
                for (List<DataEntityTableMapping> customizeMappingSplit : customizeMappingSplitList) {
                    dataEntityTableMappingMapper.insertCustomizeMapping(dataEntityTable.getId(), customizeMappingSplit);
                }
            }
            // 保存实体表中预定义字段和mapping的关系
            List<DataEntityTableMapping> predefinedMappingList = dataEntityTableMappingList.stream().filter(dataEntityTableMapping -> dataEntityTableMapping.getEntityInsertType() == EntityInsertType.BUILTIN).map(dataEntityTableMapping -> {
                dataEntityTableMapping.setDataEntityId(dataEntityTable.getDataEntityId());
                dataEntityTableMapping.setDataTableId(dataEntityTable.getId());
                return dataEntityTableMapping;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(predefinedMappingList)) {
                log.info("predefinedMappingList print {}", JSONObject.toJSONString(predefinedMappingList));
                // 每50提交一次
                // 创建实体表和mapping的关系，mapping和元素字段的关系
                List<List<DataEntityTableMapping>> predefinedMappingSplitList = SplitUtils.splitList(predefinedMappingList, 50);
                for (List<DataEntityTableMapping> predefinedMappingSplit : predefinedMappingSplitList) {
                    dataEntityTableMappingMapper.insertPredefinedMapping(dataEntityTable.getId(), predefinedMappingSplit);
                }
            }
        }
        dataEntityTableMapper.updateById(dataEntityTable);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int createTable(Long dataEntityId, DataEntityTable dataEntityTable) {
        log.info("创表开始时间：{}", System.currentTimeMillis());
        // 验证数据实体
        checkDataEntity(dataEntityId);
        // 验证明细
        checkDetails(dataEntityTable);
        // 创表方式: 自动/手动/已有表
        CreateTableWay createTableWay = dataEntityTable.getCreateTableWay();
        // 已有表类型不能创表
        if (createTableWay == CreateTableWay.TABLE) {
            throw new ServiceException(Status.TABLE_ALREADY_EXISTS_CAN_NOT_CREATE);
        }
        // 已有视图类型不能创表
        if (createTableWay == CreateTableWay.VIEW) {
            throw new ServiceException(Status.VIEW_ALREADY_EXISTS_CAN_NOT_CREATE);
        }
        Long dataSourceId = dataEntityTable.getDataSourceId();
        String dataBaseName = dataEntityTable.getDatabaseName();
        String tableName = dataEntityTable.getTableName();
        // 判断当前源 库下面是否存在需要创建的表
        R<PageDataInfo<String>> tablesResult = remoteJdbcService.getTables(dataSourceId, dataBaseName, 1, Integer.MAX_VALUE);
        if (tablesResult.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(tablesResult.getMsg());
        }
        List<String> tableList = tablesResult.getData().getTotalList();
        if (tableList.contains(tableName)) {
            throw new ServiceException(Status.DATA_ENTITY_TABLE_TABLE_EXIST);
        }

        // 获取数据源信息
        R<DataSource> queryDataSourceResult = remoteDriveService.queryDataSource(dataSourceId);
        if (queryDataSourceResult.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(queryDataSourceResult.getMsg());
        }
        DataSource dataSource = queryDataSourceResult.getData();
        // 获取当前实体的表结构
        List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntityId, true);
        if (CollectionUtils.isEmpty(dataEntityStructureVos)) {
            throw new ServiceException(Status.THE_ENTITY_HAS_NO_FIELDS_AND_CANNOT_BE_CREATED_TABLE);
        }
        // 将实体的表结构转换成创表需要的表结构
        log.info("转换表结构开始时间：{}", System.currentTimeMillis());
        List<Field> fieldList = dataEntityStructureVos.stream().map(dataEntityStructureVo -> {
            Field field = new Field();
            // sqlserver类型数据源区分大小写
            if ("sqlserver".equalsIgnoreCase(dataSource.getType())) {
                field.setFieldName(dataEntityStructureVo.getCode());
            } else {
                // 转小写
                field.setFieldName(dataEntityStructureVo.getCode().toLowerCase());
            }
            field.setFieldDesc(dataEntityStructureVo.getName());
            field.setBaseFieldType(dataEntityStructureVo.getFieldType());
            field.setLength(dataEntityStructureVo.getLength());
            field.setDecimalLength(dataEntityStructureVo.getDecimalLength());
            field.setIsPk(dataEntityStructureVo.getIsPk());
            field.setIsNull(!field.getIsPk());
            return field;
        }).collect(Collectors.toList());
        log.info("转换表结构结束时间：{}", System.currentTimeMillis());
        try {
            // 转换数据类型
            fieldList = remoteToSource(dataSource.getType(), fieldList);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        if (createTableWay == CreateTableWay.AUTO) {
            R<Boolean> existTableResult = remoteJdbcService.isExistTable(dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
            if (existTableResult.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(existTableResult.getMsg());
            }
            Boolean isExistTable = existTableResult.getData();
            if (isExistTable) {
                throw new ServiceException(Status.A_TABLE_WITH_THE_SAME_NAME_EXISTS);
            }
            // TODO:删除表
            // R dropTableResult = remoteJdbcService.dropTable(dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
            // if (dropTableResult.getCode() != Status.SUCCESS.getCode()) {
            //     throw new ServiceException(dropTableResult.getMsg());
            // }
            // 判断是否存在guality_log_id字段
            if (fieldList.stream().anyMatch(field -> BuiltinField.GUALITY_LOG_ID.getDesc().equalsIgnoreCase(field.getFieldName()))) {
                throw new ServiceException("实体中不能存在字段:[" + BuiltinField.GUALITY_LOG_ID.getDesc() + "]");
            }
            // 自动建表
            CreateTableDto createTable = new CreateTableDto();
            createTable.setDataSourceId(dataSourceId);
            createTable.setDatabaseName(dataBaseName);
            createTable.setTableName(tableName);
            createTable.setFieldList(fieldList);
            createTable.setSpecialMap(JSONObject.parseObject(dataEntityTable.getCreateTableConfig(), Map.class));
            // 创表
            log.info("创表开始时间：{}", System.currentTimeMillis());
            R tableResult = remoteJdbcService.createTable(createTable);
            if (tableResult.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(tableResult.getMsg());
            }
            log.info("创表结束时间：{}", System.currentTimeMillis());
            // 创建脏表
            // 脏表多一个规则日志id
//            Field field = new Field();
//            field.setFieldName(BuiltinField.GUALITY_LOG_ID.getDesc());
//            field.setFieldDesc("规则日志id");
//            field.setBaseFieldType(FieldType.长整数类型);
//            field.setLength(8L);
//            field.setDecimalLength(0L);
//            field.setIsPk(false);
//            field.setIsNull(false);
//            fieldList.add(field);
//            // 转换数据类型
//            fieldList = remoteFieldRelationService.toSource(dataSource.getType(), fieldList);
//            String dirtyTableName = createTable.getTableName() + Constants.DIRTY_SUFFIX;
//            // 先删除脏表，再重新创建脏表
//            remoteJdbcService.dropTable(dataSourceId, dataBaseName, dirtyTableName);
//            createTable.setTableName(dirtyTableName);
//            createTable.setFieldList(fieldList);
//            R dirtyTableResult = remoteJdbcService.createTable(createTable);
//            if (dirtyTableResult.getCode() != Status.SUCCESS.getCode()) {
//                throw new ServiceException(dirtyTableResult.getMsg());
//            }
        } else if (createTableWay == CreateTableWay.MANUAL) {
            // 手动建表
            String createTablesql = dataEntityTable.getCreateTablesql();
            // 创表
            R execDdlSqlResult = remoteJdbcService.execDdlSql(dataSourceId, createTablesql);
            if (execDdlSqlResult.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(execDdlSqlResult.getMsg());
            }
            // 创脏表
            // TODO:解析sql（只能解析druid支持的数据源）
//            SQLStatementParser sqlStatementParser = SQLParserUtils.createSQLStatementParser(createTablesql, DbType.mysql);
//            SQLCreateTableStatement sqlStatement = (SQLCreateTableStatement) sqlStatementParser.parseStatement();
//            String dirtyTableName = tableName + Constants.DIRTY_SUFFIX;
//            // TODO:从sql中截取库名、表名
//            // String databaseName = sqlStatement.getTableSource().getSchema();
//            // String tableName = sqlStatement.getTableSource().getName().getSimpleName();
//            String databaseAndTable = sqlStatement.getTableSource().toString();
//            // String tableName1 = JSONObject.parseObject(tableName, String.class) + "_dirty";
//            // 修改表
//            // sqlStatement.getTableSource().setExpr(databaseAndTable.replace(tableName, JSONObject.toJSONString(tableName1)));
//            sqlStatement.getTableSource().setExpr(databaseAndTable.replace(tableName, dirtyTableName));
//            // 添加字段
//            List<SQLTableElement> tableElementList = sqlStatement.getTableElementList();
//            SQLColumnDefinition sqlColumnDefinition = new SQLColumnDefinition();
//            sqlColumnDefinition.setName(BuiltinField.GUALITY_LOG_ID.getDesc());
//            sqlColumnDefinition.setDataType(new SQLDataTypeImpl("BIGINT"));
//            tableElementList.add(sqlColumnDefinition);
//            // 创表前先删除脏表
//            remoteJdbcService.dropTable(dataSourceId, dataBaseName, dirtyTableName);
//            R dirtyTableResult = remoteJdbcService.execDdlSql(dataSourceId, sqlStatement.toString());
//            if (dirtyTableResult.getCode() != Status.SUCCESS.getCode()) {
//                throw new ServiceException(dirtyTableResult.getMsg());
//            }
        } else {
            throw new ServiceException(Status.UNKNOWN_CREATION_TYPE);
        }
        if (dataEntityTable.getDataEntityId() != null) {
            // 更新创表信息
            return update(dataEntityTable);
        }
        // 创表成功
        return 1;
    }

    @Override
    @SwitchDataSource(value = "current")
    public int createTable(Long tenantId, Long dataEntityId, DataEntityTable dataEntityTable, boolean dropOldTable) {
        log.info("创表开始时间：{}", System.currentTimeMillis());
        // 验证数据实体
        checkDataEntity(dataEntityId);
        // 验证明细
        checkDetails(dataEntityTable);
        // 创表方式: 自动/手动/已有表
        CreateTableWay createTableWay = dataEntityTable.getCreateTableWay();
        // 已有表类型不能创表
        if (createTableWay == CreateTableWay.TABLE) {
            throw new ServiceException(Status.TABLE_ALREADY_EXISTS_CAN_NOT_CREATE);
        }
        // 已有视图类型不能创表
        if (createTableWay == CreateTableWay.VIEW) {
            throw new ServiceException(Status.VIEW_ALREADY_EXISTS_CAN_NOT_CREATE);
        }
        Long dataSourceId = dataEntityTable.getDataSourceId();
        String dataBaseName = dataEntityTable.getDatabaseName();
        String tableName = dataEntityTable.getTableName();
        // 判断当前源 库下面是否存在需要创建的表
        if (!dropOldTable) {
            R<Boolean> existTableResult = remoteJdbcService.isExistTableByTenantId(tenantId, dataSourceId, dataBaseName, tableName);
            if (existTableResult.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(existTableResult.getMsg());
            }
            Boolean isExistTable = existTableResult.getData();
            if (isExistTable) {
                throw new ServiceException(Status.A_TABLE_WITH_THE_SAME_NAME_EXISTS);
            }
        }
        // 获取数据源信息
        R<DataSource> queryDataSourceResult = remoteDriveService.queryDataSourceByTenantId(tenantId, dataSourceId);
        if (queryDataSourceResult.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(queryDataSourceResult.getMsg());
        }
        DataSource dataSource = queryDataSourceResult.getData();
        // 获取当前实体的表结构
        List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntityId, true);
        if (CollectionUtils.isEmpty(dataEntityStructureVos)) {
            throw new ServiceException(Status.THE_ENTITY_HAS_NO_FIELDS_AND_CANNOT_BE_CREATED_TABLE);
        }
        // 将实体的表结构转换成创表需要的表结构
        log.info("转换表结构开始时间：{}", System.currentTimeMillis());
        List<Field> fieldList = dataEntityStructureVos.stream().map(dataEntityStructureVo -> {
            Field field = new Field();
            // sqlserver类型数据源区分大小写
            if ("sqlserver".equalsIgnoreCase(dataSource.getType())) {
                field.setFieldName(dataEntityStructureVo.getCode());
            } else {
                // 转小写
                field.setFieldName(dataEntityStructureVo.getCode().toLowerCase());
            }
            field.setFieldDesc(dataEntityStructureVo.getName());
            field.setBaseFieldType(dataEntityStructureVo.getFieldType());
            field.setLength(dataEntityStructureVo.getLength());
            field.setDecimalLength(dataEntityStructureVo.getDecimalLength());
            field.setIsPk(dataEntityStructureVo.getIsPk());
            field.setIsNull(!field.getIsPk());
            return field;
        }).collect(Collectors.toList());
        log.info("转换表结构结束时间：{}", System.currentTimeMillis());
        try {
            // 转换数据类型
            fieldList = remoteToSource(dataSource.getType(), fieldList);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        if (createTableWay == CreateTableWay.AUTO) {
            // 自动建表
            CreateTableDto createTable = new CreateTableDto();
            createTable.setDataSourceId(dataSourceId);
            createTable.setDatabaseName(dataBaseName);
            createTable.setTableName(tableName);
            createTable.setFieldList(fieldList);
            createTable.setSpecialMap(JSONObject.parseObject(dataEntityTable.getCreateTableConfig(), Map.class));
            // 添加检查目标数据源是否只读以及删除已存在表的逻辑
            if (dropOldTable) {
                // 判断目标数据源非只读
                if (dataSource.getIsRead() == null || !dataSource.getIsRead()) {
                    // 检查表是否存在
                    R<Boolean> tableExistsR = remoteJdbcService.isExistTableByTenantId(
                            tenantId, dataSourceId, dataBaseName, tableName);
                    if (tableExistsR.getCode() == Status.SUCCESS.getCode() && tableExistsR.getData()) {
                        // 表存在则先删除
                        R<Void> dropResult = remoteJdbcService.dropTableByTenantId(
                                tenantId, dataSourceId, dataBaseName, tableName);
                        if (dropResult.getCode() != Status.SUCCESS.getCode()) {
                            throw new ServiceException("删除已存在的表失败: " + dropResult.getMsg());
                        }
                    }
                }
            }
            // 创表
            log.info("创表开始时间：{}", System.currentTimeMillis());
            R tableResult = remoteJdbcService.createTableByTenantId(tenantId, createTable);
            if (tableResult.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(tableResult.getMsg());
            }
            log.info("创表结束时间：{}", System.currentTimeMillis());
        } else if (createTableWay == CreateTableWay.MANUAL) {
            // 手动建表
            String createTablesql = dataEntityTable.getCreateTablesql();
            // 创表
            R execDdlSqlResult = remoteJdbcService.execDdlSqlByTenantId(tenantId, dataSourceId, createTablesql);
            if (execDdlSqlResult.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(execDdlSqlResult.getMsg());
            }
        } else {
            throw new ServiceException(Status.UNKNOWN_CREATION_TYPE);
        }
        if (dataEntityTable.getDataEntityId() != null) {
            // 更新创表信息
            return update(dataEntityTable);
        }
        // 创表成功
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(Long dataEntityId, Boolean isDelete, List<Long> ids) {
        // 验证数据实体
        checkDataEntity(dataEntityId);
        List<DataEntityTable> dataEntityTables = dataEntityTableMapper.selectByIdList(ids);
        for (DataEntityTable dataEntityTable : dataEntityTables) {
            // TODO:如果是已有表则不能删除关联表
            if (dataEntityTable.getCreateTableWay() == CreateTableWay.TABLE || dataEntityTable.getCreateTableWay() == CreateTableWay.VIEW) {
                log.info("----------已有表:{}---------", dataEntityTable.getTableName());
                continue;
            }
            // true代表删除表
            if (isDelete) {
                if (dataEntityTable.getIsRead() != null && !dataEntityTable.getIsRead()) {
                    // 删除表
                    R dropTableResult = remoteJdbcService.dropTable(dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
                    if (dropTableResult.getCode() != Status.SUCCESS.getCode()) {
                        throw new ServiceException(dropTableResult.getMsg());
                    }
                    // 删除脏表
                    //remoteJdbcService.dropTable(dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName() + Constants.DIRTY_SUFFIX);
                }
            }
        }
        // 删除mapping关系
        dataEntityTableMappingMapper.batchDeleteMapping(ids);
        // 删除关联表
        return dataEntityTableMapper.deleteBatchIds(dataEntityId, ids);
    }

    @Override
    public DataEntityTable selectById(Long dataEntityId) {
        // 验证数据实体
        DataEntity dataEntity = checkDataEntity(dataEntityId);
        if (dataEntity.getTableId() == null) {
            throw new ServiceException(Status.ENTITY_ASSOCIATION_TABLE_NOT_CREATED);
        }
        return checkDataEntityTable(dataEntity.getTableId());
    }

    @Override
    public DataEntityTable selectById(Long dataEntityId, Long tenantId) {
        // 强制切换数据源查询指定租户数据源
        DataSourceHolder.setDataSourceKey(Constants.FORCE_SWITCH_TENANT_PREFIX + tenantId);
        // 验证数据实体
        DataEntity dataEntity = checkDataEntity(dataEntityId);
        if (dataEntity.getTableId() == null) {
            throw new ServiceException(Status.ENTITY_ASSOCIATION_TABLE_NOT_CREATED);
        }
        DataEntityTable dataEntityTable = checkDataEntityTable(dataEntity.getTableId());
        // 查询结果后还原
        DataSourceHolder.setDataSourceKey(null);
        return dataEntityTable;
    }

    @Override
    public List<DataEntityTable> selectByIds(List<Long> dataEntityIds) {
        ArrayList<Long> tableIds = new ArrayList<>();
        for (Long dataEntityId : dataEntityIds) {
            DataEntity dataEntity = checkDataEntity(dataEntityId);
            // 验证数据实体
            if (dataEntity.getTableId() == null) {
                throw new ServiceException(Status.ENTITY_ASSOCIATION_TABLE_NOT_CREATED);
            }
            tableIds.add(dataEntity.getTableId());
        }
        // 创建实体表和mapping的关系，mapping和实体字段的关系
        List<List<Long>> lists = SplitUtils.splitList(tableIds, 50);
        List<DataEntityTable> result = new ArrayList<>();
        for (List<Long> list : lists) {
            List<DataEntityTable> dataEntityTables = dataEntityTableMapper.selectByIdList(list);
            result.addAll(dataEntityTables);
        }
        return result;


    }

    @Override
    public PageDataInfo<DataEntityTable> list(DataEntityTable dataEntityTable) {
        // 数据
        List<DataEntityTable> dataEntityTableList = dataEntityTableMapper.selectList(dataEntityTable);

        // 总数
        int total = dataEntityTableMapper.selectTotal(dataEntityTable.getDataEntityId());

        return PageUtils.getPageInfo(dataEntityTableList, total);
    }

    @Override
    public List<DataEntityTableMapping> selectTableMapping(Long dataEntityId, Long tableId) {
        // 验证数据实体
        checkDataEntity(dataEntityId);

        // 验证数据实体关联表
        checkDataEntityTable(tableId);

        return dataEntityTableMapper.selectTableMapping(dataEntityId, tableId);
    }

    @Override
    public List<DataEntityTableMapping> selectTableMapping(Long dataEntityId, Long tenantId, Long tableId) {
        // 强制切换数据源查询指定租户数据源
        DataSourceHolder.setDataSourceKey(Constants.FORCE_SWITCH_TENANT_PREFIX + tenantId);

        // 验证数据实体
        checkDataEntity(dataEntityId);

        // 验证数据实体关联表
        checkDataEntityTable(tableId);

        List<DataEntityTableMapping> dataEntityTableMappingList = dataEntityTableMapper.selectTableMapping(dataEntityId, tableId);

        // 查询结果后还原
        DataSourceHolder.setDataSourceKey(null);
        return dataEntityTableMappingList;
    }

    @Override
    public PageDataInfo<DataEntityStructureVo> selectTableMappingPaging(Long dataEntityId, Long tableId) {

        Page<DataEntityStructureVo> page = PageUtils.getPage(DataEntityStructureVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        // 验证数据实体
        DataEntity dataEntity = checkDataEntity(dataEntityId);

        // 验证数据实体关联表
        DataEntityTable dataEntityTable = checkDataEntityTable(tableId);


        List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntityId, true);

        // 获取映射字段
        List<DataEntityTableMapping> dataEntityTableMappingList = dataEntityTableMapper.selectTableMapping(dataEntityId, dataEntity.getTableId());

        R<List<Field>> fieldsR = remoteJdbcService.getFields(dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());

        if (fieldsR.getCode() != 200) {
            throw new ServiceException("获取字段失败");
        }

        List<Field> fields = fieldsR.getData();

        List<DataEntityStructureVo> result = Lists.newArrayList();

        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
            List<DataEntityTableMapping> mappings = dataEntityTableMappingList.stream().filter(dataEntityTableMapping -> dataEntityTableMapping.getFieldId().equals(dataEntityStructureVo.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mappings)) {
                dataEntityStructureVo.setDataEntityTableMapping(mappings.get(0));
            } else {
                List<Field> fieldList = fields.stream().filter(field -> field.getFieldName().equalsIgnoreCase(dataEntityStructureVo.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(fieldList)) {
                    dataEntityStructureVo.setField(fieldList.get(0));
                }
                result.add(dataEntityStructureVo);
            }
        }
        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
            if (dataEntityStructureVo.getDataEntityTableMapping() != null) {
                result.add(dataEntityStructureVo);
            }
        }


        return PageUtils.getPageInfo(result.stream().skip(pageSize * (current - 1)).limit(pageSize).collect(Collectors.toList()), result.size());
    }

    @Override
    public List<DataEntityTableMapping> selectEntityTableMapping(Long dataEntityId) {
        // 验证数据实体
        DataEntity dataEntity = checkDataEntity(dataEntityId);

        // 验证数据实体关联表
        checkDataEntityTable(dataEntity.getTableId());

        // 获取实体字段
        List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntityId, true);
        // 获取映射字段
        List<DataEntityTableMapping> dataEntityTableMappingList = dataEntityTableMapper.selectTableMapping(dataEntityId, dataEntity.getTableId());

        for (DataEntityTableMapping dataEntityTableMapping : dataEntityTableMappingList) {
            for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
                if (dataEntityTableMapping.getFieldId().equals(dataEntityStructureVo.getId())) {
                    dataEntityTableMapping.setEntityFieldCode(dataEntityStructureVo.getCode());
                    dataEntityTableMapping.setEntityFieldName(dataEntityStructureVo.getName());
                    dataEntityTableMapping.setEntityFieldDesc(dataEntityStructureVo.getDescription());
                    break;
                }
            }
        }
        return dataEntityTableMappingList;
    }

    @Override
    public List<DataEntityTableMapping> selectEntityTableMapping(Long dataEntityId, Long tenantId) {
        // 强制切换数据源查询指定租户数据源
        DataSourceHolder.setDataSourceKey(Constants.FORCE_SWITCH_TENANT_PREFIX + tenantId);
        // 验证数据实体
        DataEntity dataEntity = checkDataEntity(dataEntityId);

        // 验证数据实体关联表
        checkDataEntityTable(dataEntity.getTableId());

        // 获取实体字段
        List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntityId, true);
        // 获取映射字段
        List<DataEntityTableMapping> dataEntityTableMappingList = dataEntityTableMapper.selectTableMapping(dataEntityId, dataEntity.getTableId());

        for (DataEntityTableMapping dataEntityTableMapping : dataEntityTableMappingList) {
            for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
                if (dataEntityTableMapping.getFieldId().equals(dataEntityStructureVo.getId())) {
                    dataEntityTableMapping.setEntityFieldCode(dataEntityStructureVo.getCode());
                    dataEntityTableMapping.setEntityFieldName(dataEntityStructureVo.getName());
                    dataEntityTableMapping.setEntityFieldDesc(dataEntityStructureVo.getDescription());
                    break;
                }
            }
        }
        // 查询结果后还原
        DataSourceHolder.setDataSourceKey(null);
        return dataEntityTableMappingList;
    }

    @Override
    public void deleteEntityTableData(Long dataEntityId, String where) {
        // 验证数据实体
        DataEntity dataEntity = checkDataEntity(dataEntityId);

        // 验证数据实体关联表
        DataEntityTable dataEntityTable = checkDataEntityTable(dataEntity.getTableId());

        // TODO:不能删除已有表中的数据
        if (dataEntityTable.getCreateTableWay() == CreateTableWay.TABLE) {
            throw new ServiceException(Status.YOU_CANNOT_DELETE_TABLE_DATA_FOR_AN_EXISTING_TABLE_TYPE);
        }
        // TODO:不能删除已有视图中的数据
        if (dataEntityTable.getCreateTableWay() == CreateTableWay.VIEW) {
            throw new ServiceException(Status.YOU_CANNOT_DELETE_VIEW_DATA_FOR_AN_EXISTING_TABLE_TYPE);
        }

        // 判断表是否存在
        R<Boolean> existTableResult = remoteJdbcService.isExistTable(dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
        if (existTableResult.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(Status.THE_ASSOCIATED_TABLE_DOES_NOT_EXIST_OR_IS_ABNORMAL);
        }

        //获取数据源
        R<DataSource> queryDataSourceR = remoteDriveService.queryDataSource(dataEntityTable.getDataSourceId());
        if (queryDataSourceR.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(queryDataSourceR.getMsg());
        }
        DataSource dataSource = queryDataSourceR.getData();

        R truncateR = remoteJdbcService.truncate(dataSource.getId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName(), where);
        if (truncateR.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(truncateR.getMsg());
        }

    }

    /**
     * 获取数据源属性字段
     *
     * @param dataSourceId 数据源ID
     * @param databaseName 数据库名称
     * @param tableName    表名
     * @return 字段集合
     */
    private List<Field> getDataSourceFields(Long dataSourceId, String databaseName, String tableName) {
        R<List<Field>> r = remoteJdbcService.getFields(dataSourceId, databaseName, tableName);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException("读取数据源字段失败");
        }
        return r.getData();
    }

    /**
     * 获取数据源属性字段
     *
     * @param tenantId     租户ID
     * @param dataSourceId 数据源ID
     * @param databaseName 数据库名称
     * @param tableName    表名
     * @return 字段集合
     */
    private List<Field> getDataSourceFields(Long tenantId, Long dataSourceId, String databaseName, String tableName) {
        R<List<Field>> r = remoteJdbcService.getFieldsByTenantId(tenantId, dataSourceId, databaseName, tableName);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException("读取数据源字段失败");
        }
        return r.getData();
    }

    private int saverMappingCommon(DataEntityTable dataEntityTable, List<DataEntityStructureVo> dataEntityStructureVoList, List<Field> fields) {
        List<DataEntityTableMapping> dataEntityTableMappingList = new ArrayList<>();
        for (DataEntityStructureVo structureVo : dataEntityStructureVoList) {
            for (Field field : fields) {
                // 忽略大小写同名匹配
                if (structureVo.getCode().equalsIgnoreCase(field.getFieldName())) {
                    DataEntityTableMapping mapping = new DataEntityTableMapping();
                    mapping.setDataTableId(dataEntityTable.getDataEntityId());
                    mapping.setDataTableId(dataEntityTable.getId());
                    mapping.setEntityInsertType(structureVo.getEntityInsertType());
                    // 实体字段主键ID
                    mapping.setFieldId(structureVo.getId());
                    // 实体字段
                    mapping.setCode(structureVo.getCode());
                    // 源表字段
                    mapping.setFieldName(field.getFieldName());
                    dataEntityTableMappingList.add(mapping);
                    break;
                }
            }
        }
        // 删除mapping关系
        dataEntityTableMappingMapper.deleteMapping(dataEntityTable.getId());
        if (CollectionUtils.isNotEmpty(dataEntityTableMappingList)) {
            // 保存实体表中自定义字段和mapping的关系
            List<DataEntityTableMapping> customizeMappingList = dataEntityTableMappingList.stream().filter(dataEntityTableMapping -> dataEntityTableMapping.getEntityInsertType() == EntityInsertType.CUSTOMIZE || dataEntityTableMapping.getEntityInsertType() == EntityInsertType.BUILTIN).map(dataEntityTableMapping -> {
                dataEntityTableMapping.setDataEntityId(dataEntityTable.getDataEntityId());
                dataEntityTableMapping.setDataTableId(dataEntityTable.getId());
                return dataEntityTableMapping;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customizeMappingList)) {
                // 每50提交一次
                // 创建实体表和mapping的关系，mapping和实体字段的关系
                List<List<DataEntityTableMapping>> customizeMappingSplitList = SplitUtils.splitList(customizeMappingList, 50);
                for (List<DataEntityTableMapping> customizeMappingSplit : customizeMappingSplitList) {
                    dataEntityTableMappingMapper.insertCustomizeMapping(dataEntityTable.getId(), customizeMappingSplit);
                }
            }

            // 保存实体表中预定义字段和mapping的关系
            List<DataEntityTableMapping> predefinedMappingList = dataEntityTableMappingList.stream().filter(dataEntityTableMapping -> dataEntityTableMapping.getEntityInsertType() == EntityInsertType.PREDEFINED).map(dataEntityTableMapping -> {
                dataEntityTableMapping.setDataEntityId(dataEntityTable.getDataEntityId());
                dataEntityTableMapping.setDataTableId(dataEntityTable.getId());
                return dataEntityTableMapping;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(predefinedMappingList)) {
                log.info("predefinedMappingList print {}", JSONObject.toJSONString(predefinedMappingList));
                // 每50提交一次
                // 创建实体表和mapping的关系，mapping和元素字段的关系
                List<List<DataEntityTableMapping>> predefinedMappingSplitList = SplitUtils.splitList(predefinedMappingList, 50);
                for (List<DataEntityTableMapping> predefinedMappingSplit : predefinedMappingSplitList) {
                    dataEntityTableMappingMapper.insertPredefinedMapping(dataEntityTable.getId(), predefinedMappingSplit);
                }
            }
        }
        return dataEntityTableMapper.updateById(dataEntityTable);
    }

    private List<Field> remoteToSource(String dbType, List<Field> fieldList) {
        R<List<Field>> r = remoteFieldRelationService.toSource(dbType, fieldList);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

}
