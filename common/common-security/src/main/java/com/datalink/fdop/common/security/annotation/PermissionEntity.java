package com.datalink.fdop.common.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限实体注解 - 标记需要进行权限列扫描的实体类
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface PermissionEntity {
    /**
     * 实体描述
     */
    String value();
    
    /**
     * 所属模块，默认为CCMS
     */
    String module() default "CCMS";
}
