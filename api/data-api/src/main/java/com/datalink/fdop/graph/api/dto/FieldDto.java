package com.datalink.fdop.graph.api.dto;

import com.datalink.fdop.graph.api.enums.FieldSourceType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/19 15:06
 */
@Data
public class FieldDto {

    private String nodeId;

    private Boolean isEntity;

    private String preFieldId;

    private String fieldId;

    private FieldSourceType fieldSourceType;

    private Integer seq;

    public FieldDto(String nodeId, String preFieldId, String fieldId) {
        this.nodeId = nodeId;
        this.preFieldId = preFieldId;
        this.fieldId = fieldId;
    }

    public FieldDto(String nodeId, Boolean isEntity, String preFieldId, String fieldId) {
        this.nodeId = nodeId;
        this.isEntity = isEntity;
        this.preFieldId = preFieldId;
        this.fieldId = fieldId;
    }

    public FieldDto(String preFieldId, String fieldId) {
        this.preFieldId = preFieldId;
        this.fieldId = fieldId;
    }

    public FieldDto(String preFieldId, String fieldId, FieldSourceType fieldSourceType, Integer seq) {
        this.preFieldId = preFieldId;
        this.fieldId = fieldId;
        this.fieldSourceType = fieldSourceType;
        this.seq = seq;
    }
}
