package com.datalink.fdop.system.api.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 角色权限列分配请求
 */
@Data
@ApiModel("角色权限列分配请求")
public class RolePermissionColumnRequest {
    
    @ApiModelProperty("角色ID")
    private Long roleId;
    
    @ApiModelProperty("权限列名称列表")
    private List<String> columnNames;
}
