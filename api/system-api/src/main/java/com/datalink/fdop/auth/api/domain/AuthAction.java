package com.datalink.fdop.auth.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/4/25 14:53
 */
//@Document("AuthGroup")
@Data
@TableName("action")
@ApiModel("动作")
public class AuthAction implements Serializable {
    private static final long serialVersionUID = -5674147551433532450L;


    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "具体绑定值")
    private String value;

    @ApiModelProperty(value = "具体绑定值")
    private String topValue;

    @Override
    public String toString() {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{");
        if (getId() != null) ageString.append("id: " + getId() + ",");
        if (StringUtils.isNotEmpty(getValue())) ageString.append("value: '" + getValue() + "',");
        if (StringUtils.isNotEmpty(getTopValue())) ageString.append("topValue: '" + getTopValue() + "',");
        String string = ageString.toString();
        if (string.endsWith(",")){
            string=string.substring(0,string.length()-1);
        }
        string+="}";
        return string;
    }

}
