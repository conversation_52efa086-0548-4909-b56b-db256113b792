package com.datalink.fdop.system.api.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 权限列DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("权限列信息")
public class PermissionColumnDTO {
    /**
     * 列名
     */
    @ApiModelProperty("列名")
    private String columnName;
    
    /**
     * 列描述
     */
    @ApiModelProperty("列描述")
    private String columnDescription;
}
