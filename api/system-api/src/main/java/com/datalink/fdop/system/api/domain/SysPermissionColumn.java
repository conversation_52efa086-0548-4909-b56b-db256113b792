package com.datalink.fdop.system.api.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 权限列实体
 */
@Data
@NoArgsConstructor
@ApiModel("权限列")
public class SysPermissionColumn {

    @ApiModelProperty("列名")
    private String columnName;

    @ApiModelProperty("列描述")
    private String columnDescription;

    @ApiModelProperty("是否显示")
    private Boolean visible;

    public SysPermissionColumn(String columnName, String columnDescription) {
        this.columnName = columnName;
        this.columnDescription = columnDescription;
        this.visible = true; // 默认显示
    }
}
