package com.datalink.fdop.engine.api.domain;

import com.datalink.fdop.common.security.annotation.PermissionEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-02-17 16:55
 */
@Data
@PermissionEntity("账户报表100")
public class AccountReport100 {

    @ApiModelProperty("报表版本")
    private String reportVer;

    @ApiModelProperty("报表项目")
    private String reportItem;

    @ApiModelProperty("年份")
    private String year;

    @ApiModelProperty("版本ID")
    private String verId;

    @ApiModelProperty("控制区域ID")
    private String controlAreaId;

    @ApiModelProperty("公司ID")
    private String companyId;

    @ApiModelProperty("工厂ID")
    private String factoryId;

    @ApiModelProperty("报表项目L1 ID")
    private String reportItemL1Id;

    @ApiModelProperty("报表项目L1描述")
    private String reportItemL1Desc;

    @ApiModelProperty("报表项目L2 ID")
    private String reportItemL2Id;

    @ApiModelProperty("报表项目L2描述")
    private String reportItemL2Desc;

    @ApiModelProperty("来源类型")
    private String sourceType;

    @ApiModelProperty("原始成本中心ID")
    private String originCostCenterId;

    @ApiModelProperty("原始成本中心描述")
    private String originCostCenterDesc;

    @ApiModelProperty("数值001")
    private BigDecimal value001;

    @ApiModelProperty("数值002")
    private BigDecimal value002;

    @ApiModelProperty("数值003")
    private BigDecimal value003;

    @ApiModelProperty("数值004")
    private BigDecimal value004;

    @ApiModelProperty("数值005")
    private BigDecimal value005;

    @ApiModelProperty("数值006")
    private BigDecimal value006;

    @ApiModelProperty("数值007")
    private BigDecimal value007;

    @ApiModelProperty("数值008")
    private BigDecimal value008;

    @ApiModelProperty("数值009")
    private BigDecimal value009;

    @ApiModelProperty("数值010")
    private BigDecimal value010;

    @ApiModelProperty("数值011")
    private BigDecimal value011;

    @ApiModelProperty("数值012")
    private BigDecimal value012;

    @ApiModelProperty("数值013")
    private BigDecimal value013;

    @ApiModelProperty("数值014")
    private BigDecimal value014;

    @ApiModelProperty("数值015")
    private BigDecimal value015;

    @ApiModelProperty("数值016")
    private BigDecimal value016;

    @ApiModelProperty("第一季度数值")
    private BigDecimal valueQ1;

    @ApiModelProperty("第二季度数值")
    private BigDecimal valueQ2;

    @ApiModelProperty("第三季度数值")
    private BigDecimal valueQ3;

    @ApiModelProperty("第四季度数值")
    private BigDecimal valueQ4;

    @ApiModelProperty("总数值")
    private BigDecimal valueTotal;

}
