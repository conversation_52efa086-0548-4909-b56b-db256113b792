# CCMS权限列管理系统完整指南

## 📋 目录

- [项目介绍](#项目介绍)
- [核心功能](#核心功能)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
- [详细使用说明](#详细使用说明)
- [API接口文档](#api接口文档)
- [代码示例](#代码示例)
- [数据库设计](#数据库设计)
- [常见问题](#常见问题)

## 🚀 项目介绍

CCMS权限列管理系统是一个基于Spring Boot + Apache AGE图数据库的企业级权限管理解决方案，专门为CCMS（企业成本管理系统）设计，提供字段级的精细化权限控制。

### 核心特性

- **智能扫描** - 通过注解自动识别需要权限控制的实体和字段
- **字段级控制** - 支持对单个字段进行显示/隐藏控制
- **角色权限分配** - 灵活的角色权限分配机制
- **实时权限查询** - 快速查询用户可访问的字段列表
- **动态同步** - 支持实体变更后的权限列同步

## 🎯 核心功能

### 1. 权限列管理
- ✅ 扫描CCMS实体类获取字段信息
- ✅ 管理权限列的显示/隐藏状态
- ✅ 支持字段去重显示
- ✅ 动态同步权限列到数据库

### 2. 角色权限控制
- ✅ 为角色分配特定的权限列
- ✅ 查询角色拥有的权限列
- ✅ 批量权限分配和取消

### 3. 用户权限查询
- ✅ 查询用户可访问的字段列表
- ✅ 基于角色的权限继承
- ✅ 实时权限验证

## 🏗️ 系统架构

```
CCMS权限列管理系统
├── 注解层 (@PermissionEntity)
│   └── 标记需要权限控制的实体类
├── 扫描层 (PermissionColumnService)
│   └── 自动扫描实体获取字段信息
├── 存储层 (Apache AGE图数据库)
│   ├── sys_permission_column (权限列节点)
│   └── sys_role_permission_column (角色权限关联边)
├── 服务层 (Service)
│   ├── 权限列管理服务
│   └── 角色权限分配服务
└── 接口层 (Controller)
    ├── 权限列管理接口
    └── 角色权限分配接口
```

### 文件结构

```
data-platform/
├── api/
│   ├── system-api/
│   │   └── domain/
│   │       ├── SysPermissionColumn.java          # 权限列实体
│   │       └── dto/
│   │           ├── PermissionColumnDTO.java      # 权限列DTO
│   │           └── RolePermissionColumnRequest.java
│   └── ccm-api/
│       └── engine.api.domain/                    # CCMS实体类
├── common/
│   └── common-security/
│       └── annotation/
│           └── PermissionEntity.java             # 权限实体注解
├── services/
│   └── system-service/
│       ├── controller/
│       │   ├── PermissionColumnController.java   # 权限列控制器
│       │   └── SysRoleController.java           # 角色控制器
│       ├── service/
│       │   ├── IPermissionColumnService.java     # 权限列服务接口
│       │   └── impl/
│       │       └── PermissionColumnServiceImpl.java
│       ├── mapper/
│       │   ├── SysPermissionColumnMapper.java    # 权限列Mapper
│       │   └── SysRolePermissionColumnMapper.java
│       └── resources/mapper/system/
│           ├── SysPermissionColumnMapper.xml     # 权限列SQL
│           └── SysRolePermissionColumnMapper.xml
└── sql/init/
    └── permission_column_init.sql                # 初始化SQL
```

## 🚀 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- PostgreSQL 12+ with Apache AGE extension
- Spring Boot 2.x

### 1. 初始化数据库

执行初始化SQL脚本：

```sql
-- 执行 sql/init/permission_column_init.sql
psql -d your_database -f sql/init/permission_column_init.sql
```

### 2. 为CCMS实体添加注解

在需要权限控制的实体类上添加注解：

```java
@PermissionEntity("账户报表100")
public class AccountReport100 {
    
    @ApiModelProperty("公司ID")
    private String companyId;
    
    @ApiModelProperty("工厂ID")
    private String factoryId;
    
    @ApiModelProperty("报表版本")
    private String reportVer;
    
    // 其他字段...
}
```

### 3. 启动服务并同步权限列

```bash
# 启动系统服务
cd services/system-service
mvn spring-boot:run

# 同步权限列到数据库
curl -X POST http://localhost:8080/system/permissionColumn/sync
```

### 4. 分配角色权限

```bash
# 为角色ID为1的角色分配权限列
curl -X POST http://localhost:8080/system/role/assignPermissionColumns \
  -H "Content-Type: application/json" \
  -d '{
    "roleId": 1,
    "columnNames": ["companyId", "factoryId", "reportVer"]
  }'
```

## 📖 详细使用说明

### 步骤1：实体注解配置

#### 1.1 添加实体注解

为需要权限控制的CCMS实体类添加`@PermissionEntity`注解：

```java
@PermissionEntity(value = "实体描述", module = "模块名")
public class YourEntity {
    // 字段定义
}
```

#### 1.2 字段注解要求

确保所有需要权限控制的字段都有`@ApiModelProperty`注解：

```java
@ApiModelProperty("字段描述")
private String fieldName;
```

### 步骤2：权限列管理

#### 2.1 扫描权限列

```bash
# 扫描所有模块的权限列
GET /system/permissionColumn/scan

# 扫描特定模块的权限列
GET /system/permissionColumn/scan?module=CCMS
```

#### 2.2 同步到数据库

```bash
# 将扫描结果同步到数据库
POST /system/permissionColumn/sync
```

#### 2.3 管理权限列状态

```bash
# 查看所有权限列
GET /system/permissionColumn/list

# 隐藏权限列
PUT /system/permissionColumn/visible?columnName=sensitiveField&visible=false

# 显示权限列
PUT /system/permissionColumn/visible?columnName=normalField&visible=true
```

### 步骤3：角色权限分配

#### 3.1 查看角色权限

```bash
# 查看角色的权限列
GET /system/role/permissionColumns/{roleId}
```

#### 3.2 分配权限列

```bash
# 为角色分配权限列
POST /system/role/assignPermissionColumns
{
  "roleId": 1,
  "columnNames": ["companyId", "factoryId", "reportVer"]
}
```

#### 3.3 查询用户权限

```bash
# 查询用户可访问的权限列
GET /system/role/accessibleColumns/{userId}
```

## 📚 API接口文档

### 权限列管理接口

| 接口路径 | 请求方法 | 功能描述 | 参数说明 |
|---------|---------|---------|---------|
| `/system/permissionColumn/scan` | GET | 扫描实体获取权限列 | `module`: 模块名(可选) |
| `/system/permissionColumn/sync` | POST | 同步权限列到数据库 | 无 |
| `/system/permissionColumn/list` | GET | 获取所有权限列 | 无 |
| `/system/permissionColumn/visible` | PUT | 更新权限列显示状态 | `columnName`: 列名<br>`visible`: 是否显示 |

### 角色权限管理接口

| 接口路径 | 请求方法 | 功能描述 | 参数说明 |
|---------|---------|---------|---------|
| `/system/role/permissionColumns/{roleId}` | GET | 获取角色权限列 | `roleId`: 角色ID |
| `/system/role/assignPermissionColumns` | POST | 为角色分配权限列 | 请求体: `RolePermissionColumnRequest` |
| `/system/role/accessibleColumns/{userId}` | GET | 获取用户可访问的权限列 | `userId`: 用户ID |

### 请求响应示例

#### 扫描权限列响应

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "columnName": "companyId",
      "columnDescription": "公司ID"
    },
    {
      "columnName": "factoryId", 
      "columnDescription": "工厂ID"
    }
  ]
}
```

#### 角色权限分配请求

```json
{
  "roleId": 1,
  "columnNames": ["companyId", "factoryId", "reportVer"]
}
```

#### 用户可访问权限列响应

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": ["companyId", "factoryId", "reportVer"]
}
```
